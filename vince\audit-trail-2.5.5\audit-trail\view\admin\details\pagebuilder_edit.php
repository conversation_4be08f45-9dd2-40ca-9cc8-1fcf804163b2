<?php if (!defined ('ABSPATH')) die (); ?>

<h3><?php _e('页面构建器编辑详情', 'audit-trail'); ?></h3>

<table class="form-table">
    <tr>
        <th scope="row"><?php _e('标题', 'audit-trail'); ?>:</th>
        <td><?php echo esc_html($post->post_title); ?></td>
    </tr>
    <tr>
        <th scope="row"><?php _e('页面构建器类型', 'audit-trail'); ?>:</th>
        <td>
            <?php 
            $builder_types = array(
                'elementor' => 'Elementor',
                'elementor_document' => 'Elementor文档',
                'beaver_builder' => 'Beaver Builder',
                'siteorigin_panels' => 'SiteOrigin Page Builder',
                'wpbakery' => 'WPBakery Page Builder',
                'divi_builder' => 'Divi Builder',
                'gutenberg' => 'Gutenberg编辑器'
            );
            
            $builder_type = isset($builder_data['builder_type']) ? $builder_data['builder_type'] : '未知';
            echo isset($builder_types[$builder_type]) ? esc_html($builder_types[$builder_type]) : esc_html($builder_type);
            ?>
        </td>
    </tr>
    <tr>
        <th scope="row"><?php _e('编辑时间', 'audit-trail'); ?>:</th>
        <td><?php echo isset($builder_data['edit_time']) ? esc_html($builder_data['edit_time']) : current_time('mysql'); ?></td>
    </tr>
    <tr>
        <th scope="row"><?php _e('文章ID', 'audit-trail'); ?>:</th>
        <td><?php echo absint($post->ID); ?></td>
    </tr>
    <tr>
        <th scope="row"><?php _e('文章类型', 'audit-trail'); ?>:</th>
        <td><?php echo esc_html($post->post_type); ?></td>
    </tr>
    <tr>
        <th scope="row"><?php _e('文章状态', 'audit-trail'); ?>:</th>
        <td><?php echo esc_html($post->post_status); ?></td>
    </tr>
    <?php if (isset($builder_data['blocks_count'])): ?>
    <tr>
        <th scope="row"><?php _e('内容块数量', 'audit-trail'); ?>:</th>
        <td><?php echo absint($builder_data['blocks_count']); ?></td>
    </tr>
    <?php endif; ?>
    <?php if (isset($builder_data['is_built_with_elementor'])): ?>
    <tr>
        <th scope="row"><?php _e('使用Elementor构建', 'audit-trail'); ?>:</th>
        <td><?php echo $builder_data['is_built_with_elementor'] ? __('是', 'audit-trail') : __('否', 'audit-trail'); ?></td>
    </tr>
    <?php endif; ?>
    <?php if (isset($builder_data['document_type'])): ?>
    <tr>
        <th scope="row"><?php _e('文档类型', 'audit-trail'); ?>:</th>
        <td><?php echo esc_html($builder_data['document_type']); ?></td>
    </tr>
    <?php endif; ?>
</table>

<h4><?php _e('文章链接', 'audit-trail'); ?>:</h4>
<p>
    <a href="<?php echo esc_url(get_permalink($post->ID)); ?>" target="_blank"><?php _e('查看页面', 'audit-trail'); ?></a> | 
    <a href="<?php echo esc_url(get_edit_post_link($post->ID)); ?>"><?php _e('编辑页面', 'audit-trail'); ?></a>
</p>

<?php if (isset($post->post_modified) && isset($post->post_modified_gmt)): ?>
<p>
    <?php _e('最后修改时间', 'audit-trail'); ?>: <?php echo esc_html($post->post_modified); ?> 
    (GMT: <?php echo esc_html($post->post_modified_gmt); ?>)
</p>
<?php endif; ?> 