<p>未发现可疑文章内容</p>
    </div>
    
    <div id="suspicious-users" class="tab-content">
        <h3>可疑用户</h3>
<p>未发现可疑用户</p>
    </div>
    
    <div id="suspicious-options" class="tab-content">
        <h3>可疑选项值</h3>
<table><thead><tr>
<th>option_id</th><th>option_name</th><th>autoload</th>
</tr></thead><tbody>
<tr>
<td>24224</td><td>_transient_feed_9bbd59226dc36b9b26cd43f15694c5c3</td><td>no</td></tr>
<tr>
<td>24246</td><td>aam_toolbar_cache</td><td>yes</td></tr>
</tbody></table>
    </div>
    
    <div id="suspicious-comments" class="tab-content">
        <h3>可疑评论</h3>
<p>未发现可疑评论</p>
    </div>
    
    <div id="suspicious-postmeta" class="tab-content">
        <h3>可疑元数据</h3>
<p>未发现可疑元数据</p>
    </div>
    
    <div id="suspicious-dates" class="tab-content">
        <h3>可疑日期</h3>
<p>未发现可疑日期</p>
    </div>
    
    <footer>
        <p>WordPress数据库扫描工具 - 扫描路径: /var/www/jilian</p>
    </footer>
    
    <script>
        function openTab(evt, tabName) {
            var i, tabcontent, tabbuttons;
            
            // 隐藏所有标签内容
            tabcontent = document.getElementsByClassName("tab-content");
            for (i = 0; i < tabcontent.length; i++) {
                tabcontent[i].className = tabcontent[i].className.replace(" active", "");
            }
            
            // 移除所有标签按钮的active类
            tabbuttons = document.getElementsByClassName("tab-button");
            for (i = 0; i < tabbuttons.length; i++) {
                tabbuttons[i].className = tabbuttons[i].className.replace(" active", "");
            }
            
            // 显示当前标签，并添加active类到按钮
            document.getElementById(tabName).className += " active";
            evt.currentTarget.className += " active";
        }
    </script>
</body>
</html>
