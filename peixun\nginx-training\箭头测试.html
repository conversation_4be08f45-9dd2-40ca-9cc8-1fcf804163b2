<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>箭头方向测试</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            font-family: 'Microsoft YaHei', sans-serif;
            min-height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .container {
            position: relative;
            width: 600px;
            height: 400px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 20px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        /* 元素样式 */
        .element {
            position: absolute;
            width: 100px;
            height: 60px;
            background: linear-gradient(135deg, #4facfe, #00f2fe);
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
            font-size: 14px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
        }

        /* 中心元素 - Nginx */
        .center {
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 120px;
            height: 80px;
            background: linear-gradient(135deg, #6366f1, #8b5cf6);
            font-size: 16px;
        }

        /* 四个角的元素 */
        .top-left {
            top: 30px;
            left: 30px;
            background: linear-gradient(135deg, #06d6a0, #00c9ff);
        }

        .top-right {
            top: 30px;
            right: 30px;
            background: linear-gradient(135deg, #f093fb, #f5576c);
        }

        .bottom-left {
            bottom: 30px;
            left: 30px;
            background: linear-gradient(135deg, #4facfe, #00f2fe);
        }

        .bottom-right {
            bottom: 30px;
            right: 30px;
            background: linear-gradient(135deg, #43e97b, #38f9d7);
        }

        /* 箭头样式 */
        .arrow {
            position: absolute;
            width: 80px;
            height: 4px;
            background: linear-gradient(90deg, #06d6a0, #4facfe);
            border-radius: 2px;
        }

        .arrow::after {
            content: '';
            position: absolute;
            right: -12px;
            top: -8px;
            width: 0;
            height: 0;
            border-left: 20px solid #4facfe;
            border-top: 10px solid transparent;
            border-bottom: 10px solid transparent;
        }

        .arrow::before {
            content: '';
            position: absolute;
            left: -5px;
            top: 50%;
            transform: translateY(-50%);
            width: 8px;
            height: 8px;
            background: #06d6a0;
            border-radius: 50%;
        }

        /* 四个方向的箭头 */
        /* 左上 → 中心 */
        .arrow1 {
            top: 90px;
            left: 130px;
            transform: rotate(45deg);
        }

        /* 右上 → 中心 */
        .arrow2 {
            top: 90px;
            right: 130px;
            transform: rotate(-45deg);
        }

        /* 左下 → 中心 */
        .arrow3 {
            bottom: 90px;
            left: 130px;
            transform: rotate(-45deg);
        }

        /* 右下 → 中心 */
        .arrow4 {
            bottom: 90px;
            right: 130px;
            transform: rotate(45deg);
        }

        /* 动画效果 */
        .arrow {
            animation: flow 2s ease-in-out infinite;
        }

        @keyframes flow {
            0%, 100% { opacity: 0.7; }
            50% { opacity: 1; }
        }

        .title {
            position: absolute;
            top: -50px;
            left: 50%;
            transform: translateX(-50%);
            color: white;
            font-size: 24px;
            font-weight: bold;
            text-align: center;
        }

        .description {
            position: absolute;
            bottom: -80px;
            left: 50%;
            transform: translateX(-50%);
            color: white;
            font-size: 14px;
            text-align: center;
            opacity: 0.8;
            width: 100%;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="title">Nginx 中心枢纽模式测试</div>
        
        <!-- 四个角的元素 -->
        <div class="element top-left">互联网</div>
        <div class="element top-right">用户</div>
        <div class="element bottom-left">网站</div>
        <div class="element bottom-right">应用</div>
        
        <!-- 中心元素 -->
        <div class="element center">Nginx<br>超级服务器</div>
        
        <!-- 四个箭头，全部指向中心 -->
        <div class="arrow arrow1"></div>
        <div class="arrow arrow2"></div>
        <div class="arrow arrow3"></div>
        <div class="arrow arrow4"></div>
        
        <div class="description">
            🎯 所有箭头都指向中心的Nginx<br>
            体现Nginx作为中心枢纽的核心地位
        </div>
    </div>
</body>
</html>
