<?php
/* ============================================================================================================
	 This software is provided "as is" and any express or implied warranties, including, but not limited to, the
	 implied warranties of merchantibility and fitness for a particular purpose are disclaimed. In no event shall
	 the copyright owner or contributors be liable for any direct, indirect, incidental, special, exemplary, or
	 consequential damages (including, but not limited to, procurement of substitute goods or services; loss of
	 use, data, or profits; or business interruption) however caused and on any theory of liability, whether in
	 contract, strict liability, or tort (including negligence or otherwise) arising in any way out of the use of
	 this software, even if advised of the possibility of such damage.

	 This software is provided free-to-use, but is not free software.  The copyright and ownership remains
	 entirely with the author.  Please distribute and use as necessary, in a personal or commercial environment,
	 but it cannot be sold or re-used without express consent from the author.
   ============================================================================================================ */

function maybe_jsonize( $thing ) {
	if ( is_array( $thing ) || is_object( $thing ) )
		return json_encode( $thing );
	return $thing;
}

/**
 * Class to represent audit trail log items
 *
 * @package Audit Trail
 * <AUTHOR> Godley
 **/

class AT_Audit {
	public $happened_at;
	public $id;
	public $operation;
	public $ip;
	public $item_id;
	public $user_id;

	/**
	 * Constructor accepts an array of values with which to seed the object
	 *
	 * @param array $details Array of starting values (variable name => value)
	 * @return void
	 **/

	function __construct( $details = '' ) {
		if ( is_object( $details ) ) {
			foreach ($details AS $key => $value ) {
				$this->$key = $value;
			}
		}

		if (isset($this->happened_at)) {
		$this->happened_at = mysql2date( 'U', $this->happened_at );
		}
	}

	/**
	 * Get a log item (username is filled in)
	 *
	 * @param int $id Log ID
	 * @return AT_Audit
	 **/

	static function get( $id ) {
		global $wpdb;

		$row = $wpdb->get_row( $wpdb->prepare( "SELECT {$wpdb->prefix}audit_trail.*,{$wpdb->users}.user_nicename AS username FROM {$wpdb->prefix}audit_trail LEFT JOIN {$wpdb->users} ON {$wpdb->users}.ID={$wpdb->prefix}audit_trail.user_id WHERE {$wpdb->prefix}audit_trail.id=%d", $id ) );
		if ( $row )
			return new AT_Audit( $row );
		return false;
	}


	/**
	 * Get all log items (username is filled in)
	 *
	 * @return array Array of AT_Audit items
	 **/

	function get_everything() {
		global $wpdb;

		$rows = $wpdb->get_results( "SELECT {$wpdb->prefix}audit_trail.*,{$wpdb->users}.user_nicename AS username FROM {$wpdb->prefix}audit_trail LEFT JOIN {$wpdb->users} ON {$wpdb->users}.ID={$wpdb->prefix}audit_trail.user_id" );
		$data = array();
		if ( $rows ) {
			foreach ( $rows AS $row ) {
				$data[] = new AT_Audit( $row );
			}
		}

		return $data;
	}


	/**
	 * Get all log items for a given post (username is filled in)
	 *
	 * @param int $id Post ID
	 * @param int $max Maximum number of items to return
	 * @return array Array of AT_Audit items
	 **/

	function get_by_post( $id, $max = 10 ) {
		global $wpdb;

		$order = 'DESC';
		if ( get_option( 'audit_post_order' ) == true )
			$order = 'ASC';

		$rows = $wpdb->get_results( $wpdb->prepare( "SELECT {$wpdb->prefix}audit_trail.*,{$wpdb->users}.user_nicename AS username FROM {$wpdb->prefix}audit_trail LEFT JOIN {$wpdb->users} ON {$wpdb->users}.ID={$wpdb->prefix}audit_trail.user_id WHERE item_id=%d AND operation='save_post' ORDER BY happened_at $order LIMIT 1,%d", $id, $max ) );
		$data = array ();
		if ( $rows ) {
			foreach ( $rows AS $row ) {
				$data[] = new AT_Audit( $row );
			}
		}

		return $data;
	}


	/**
	 * Delete a log item
	 *
	 * @param int $id Item to delete
	 * @return void
	 **/

	static function delete( $id ) {
		global $wpdb;

		$wpdb->query( $wpdb->prepare( "DELETE FROM {$wpdb->prefix}audit_trail WHERE id=%d", $id ) );
	}

	/**
	 * 获取客户端IP地址
	 * 支持IPv4和IPv6格式
	 * 
	 * @return string 客户端IP地址
	 */
	static function get_ip() {
		$ip = '';
		
		// 按优先级获取IP地址
		if (isset($_SERVER['HTTP_CLIENT_IP'])) {
			$ip = $_SERVER['HTTP_CLIENT_IP'];
		} elseif (isset($_SERVER['HTTP_X_FORWARDED_FOR'])) {
			// 获取代理后的真实IP
			$ip_list = explode(',', $_SERVER['HTTP_X_FORWARDED_FOR']);
			$ip = trim($ip_list[0]);
		} elseif (isset($_SERVER['HTTP_X_FORWARDED'])) {
			$ip = $_SERVER['HTTP_X_FORWARDED'];
		} elseif (isset($_SERVER['HTTP_X_CLUSTER_CLIENT_IP'])) {
			$ip = $_SERVER['HTTP_X_CLUSTER_CLIENT_IP'];
		} elseif (isset($_SERVER['HTTP_FORWARDED_FOR'])) {
			$ip = $_SERVER['HTTP_FORWARDED_FOR'];
		} elseif (isset($_SERVER['HTTP_FORWARDED'])) {
			$ip = $_SERVER['HTTP_FORWARDED'];
		} elseif (isset($_SERVER['REMOTE_ADDR'])) {
			$ip = $_SERVER['REMOTE_ADDR'];
		}
		
		// 验证IP地址格式
		if (!filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_IPV4 | FILTER_FLAG_IPV6)) {
			// 无效IP地址，使用一个占位符
			$ip = '';
		}
		
		return $ip;
	}

	/**
	 * 判断是否应该记录此操作
	 * 用于高流量站点优化，仅对页面访问应用采样策略
	 * 
	 * @param string $operation 操作类型
	 * @param string $request_uri 请求URI
	 * @return bool 是否记录此操作
	 */
	static function should_log_operation($operation, $request_uri = '') {
		// 获取请求URI，如果未提供
		if (empty($request_uri) && isset($_SERVER['REQUEST_URI'])) {
			$request_uri = $_SERVER['REQUEST_URI'];
		}
		
		// 仅对template_redirect操作应用优化策略
		if ($operation !== 'template_redirect') {
			return true; // 其他操作类型始终记录
		}
		
		// 检查是否启用高流量模式
		if (!get_option('audit_high_volume_mode', true)) {
			return true; // 未启用高流量模式，始终记录
		}
		
		// 快速检查静态资源，使用数组合并以提高PHP 7.4兼容性
		$static_extensions = array(
			'.css', '.js', '.jpg', '.jpeg', '.png', '.gif', '.ico', '.svg', 
			'.woff', '.woff2', '.ttf', '.eot', '.pdf', '.mp4', '.webp', '.mp3'
		);
		
		if (!empty($request_uri)) {
			foreach ($static_extensions as $ext) {
				if (stripos($request_uri, $ext) !== false) {
					return false; // 静态资源不记录
				}
			}
		}
		
		// 检查是否在exclude_paths中
		$exclude_paths_option = get_option('audit_exclude_paths', 'wp-content/uploads,wp-includes/js,wp-includes/css');
		$exclude_paths = explode(',', $exclude_paths_option);
		
		if (!empty($request_uri)) {
			foreach ($exclude_paths as $path) {
				$path = trim($path);
				if (!empty($path) && stripos($request_uri, $path) !== false) {
					return false; // 在排除路径中，不记录
				}
			}
		}
		
		// 应用采样率
		$sample_rate = (int)get_option('audit_log_sample_rate', 1); // 默认1%记录
		if ($sample_rate <= 0) {
			$sample_rate = 1; // 最低采样率1%
		} elseif ($sample_rate > 100) {
			$sample_rate = 100; // 最高采样率100%
		}
		
		if ($sample_rate < 100) {
			// 使用更高效的随机数生成方法
			$random = mt_rand(1, 100);
			if ($random > $sample_rate) {
				return false; // 不在采样范围内，不记录
			}
		}
		
		return true; // 通过所有检查，记录此操作
	}

	/**
	 * Create a new log item
	 *
	 * @param string $operation What function is being monitored (e.g. 'save_post')
	 * @param int|string $item ID to the item being monitored (e.g post ID, comment ID)
	 * @param mixed $data Any data associated with the item (e.g. the post)
	 * @param string $title A title string in case the data may change in the future (i.e the current post title)
	 * @param int|bool $user The user ID (if different from the current user)
	 * @return int|false The ID of the new entry, or false on failure
	 **/

	static function create( $operation, $item = '', $data = '', $title = '', $user = false ) {
		global $wpdb;
		
		// 检查是否应该记录此操作
		$request_uri = isset($_SERVER['REQUEST_URI']) ? $_SERVER['REQUEST_URI'] : '';
		if (!self::should_log_operation($operation, $request_uri)) {
			return false;
		}
		
		// 使用INSERT IGNORE避免重复记录
		$table_name = $wpdb->prefix . 'audit_trail';

		// 获取当前用户ID，如果已提供则使用提供的ID
		if ($user === false) {
			$current_user = wp_get_current_user();
			if ($current_user && $current_user instanceof WP_User) {
				$user = $current_user->ID;
			} else {
				$user = 0; // 未登录用户
			}
		}
		
		// 确保item_id是整数，为空时默认为0
		$item_id = (int)$item;
		
		// 处理敏感数据
		$sensitive_fields = array(
			'password', 'pass', 'pwd', 'passwd', 'secret', 'token', 'key', 'auth', 
			'credit', 'card', 'cvv', 'ssn', 'social'
		);
		
		// 检查是否包含敏感数据并进行屏蔽
		$masked_data = $data;
		if (is_array($data) || is_object($data)) {
			$masked_data = self::mask_sensitive_data($data, $sensitive_fields);
		}
		
		// 准备JSON数据 - 使用try/catch避免JSON错误
		$json_data = '';
		if ($masked_data) {
			try {
				if (is_array($masked_data) || is_object($masked_data)) {
					$json_data = json_encode($masked_data, JSON_UNESCAPED_UNICODE | JSON_PARTIAL_OUTPUT_ON_ERROR);
					
					// 如果JSON编码失败，尝试替代方法
					if (json_last_error() !== JSON_ERROR_NONE) {
						$json_data = json_encode(
							array('error' => 'JSON编码失败: ' . json_last_error_msg(), 
								 'operation' => $operation,
								 'timestamp' => current_time('mysql')),
							JSON_UNESCAPED_UNICODE
						);
					}
				} else {
					$json_data = (string)$masked_data;
				}
			} catch (Exception $e) {
				$json_data = json_encode(
					array('error' => '数据处理异常: ' . $e->getMessage(),
						 'operation' => $operation,
						 'timestamp' => current_time('mysql')),
					JSON_UNESCAPED_UNICODE
				);
			}
		}
		
		// 获取当前时间和IP地址
		$now = current_time('mysql');
		$ip = self::get_ip();
		
		// 标题处理
		if (is_string($title)) {
			$title = substr(trim($title), 0, 100);
		} elseif (is_numeric($title) || is_bool($title)) {
			$title = (string)$title;
		} else {
			$title = '';
		}
		
		// 使用预处理语句插入数据
		try {
			$result = $wpdb->query($wpdb->prepare(
				"INSERT IGNORE INTO $table_name
				(operation, user_id, ip, happened_at, item_id, data, title)
				VALUES (%s, %d, %s, %s, %d, %s, %s)",
				$operation,
				$user,
				$ip,
				$now,
				$item_id,
				$json_data,
				$title
			));
			
			// 返回插入的ID或失败
			if ($result) {
				return $wpdb->insert_id;
			}
		} catch (Exception $e) {
			// 捕获数据库错误，但不阻止执行
			return false;
		}
		
		return false;
	}
	
	/**
	 * 递归处理数组或对象中的敏感数据
	 * 
	 * @param mixed $data 需要处理的数据
	 * @param array $sensitive_fields 敏感字段列表
	 * @return mixed 处理后的数据
	 */
	private static function mask_sensitive_data($data, $sensitive_fields) {
		if (is_array($data) || is_object($data)) {
			$data_array = (array)$data;
			
			foreach ($data_array as $key => $value) {
				// 检查当前字段是否敏感
				foreach ($sensitive_fields as $field) {
					if (stripos($key, $field) !== false) {
						$data_array[$key] = '******';
						break;
					}
				}
				
				// 递归处理嵌套数据
				if (is_array($value) || is_object($value)) {
					$data_array[$key] = self::mask_sensitive_data($value, $sensitive_fields);
				}
			}
			
			// 转回原始类型
			if (is_object($data)) {
				return (object)$data_array;
			}
			return $data_array;
		}
		
		return $data;
	}


	/**
	 * undocumented function
	 * @todo explain
	 * @return void
	 **/

	function get_operation( $open = true ) {
		$this->message = '';
		$obj = apply_filters( 'audit_show_operation', $this );

		if ( is_object( $obj ) && !empty( $obj->message ) ) {
			if ( $open == true )
				return str_replace( 'view_audit', 'view_audit_open', $obj->message );
			return str_replace( 'view_audit', 'view_audit_close', $obj->message );
		}

		return $this->operation;
	}


	/**
	 * undocumented function
	 *
	 * @todo explain
	 * @return void
	 **/

	function get_item() {
		$this->message = '';
		$obj = apply_filters( 'audit_show_item', $this );

		if ( is_object( $obj ) && !empty( $obj->message ) )
			return $obj->message;
		return $this->item_id;
	}


	/**
	 * undocumented function
	 *
	 * @todo explain
	 * @return void
	 **/

	function get_details() {
		$this->message = '';
		$obj = apply_filters( 'audit_show_details', $this );

		$details = '';
		if ( is_object( $obj ) && !empty( $obj->message ) )
			$details = $obj->message;
		return $this->get_operation( false ).'<br/>'.$details;
	}


	/**
	 * Delete all items that are over a given number of days old
	 *
	 * @param int $days Number of days old
	 * @return void
	 **/

	static function expire( $days ) {
		global $wpdb;

		if ( $days > 0 ) {
			// 使用LIMIT限制每次删除的数量，避免锁表
			$wpdb->query( $wpdb->prepare( "DELETE FROM {$wpdb->prefix}audit_trail WHERE DATE_SUB(CURDATE(),INTERVAL %d DAY) > happened_at LIMIT 1000", $days ) );
			
			// 记录清理结果
			$deleted_count = $wpdb->rows_affected;
			if ($deleted_count > 0) {
				// 如果有删除记录，则记录自动清理日志
				self::create('audit_auto_cleanup', 0, array('days' => $days, 'count' => $deleted_count), sprintf(__('自动清理%d天前的%d条日志', 'audit-trail'), $days, $deleted_count));
			}
			
			return $deleted_count;
		}
		
		return 0;
	}

	/**
	 * 按操作类型删除日志记录
	 * 
	 * @param string $operation 操作类型
	 * @return int 删除的记录数
	 */
	static function delete_by_operation($operation) {
		global $wpdb;
		
		if (empty($operation)) {
			return 0;
		}
		
		// 清理和验证操作类型
		$operation = sanitize_text_field($operation);
		
		// 记录删除前的统计信息
		$before_count = $wpdb->get_var(
			$wpdb->prepare(
				"SELECT COUNT(*) FROM {$wpdb->prefix}audit_trail WHERE operation = %s",
				$operation
			)
		);
		
		if (empty($before_count) || $before_count <= 0) {
			return 0;
		}
		
		// 使用事务确保操作的完整性
		$wpdb->query('START TRANSACTION');
		
		// 记录此删除操作
		$log_data = [
			'operation_type' => $operation,
			'count' => $before_count,
			'deleted_at' => current_time('mysql')
		];
		
		// 获取当前用户
		$current_user = wp_get_current_user();
		$user_id = $current_user && $current_user->ID ? $current_user->ID : 0;
		
		// 创建删除操作的日志
		self::create(
			'bulk_delete_by_operation',
			0,
			$log_data,
			sprintf('删除了%d条"%s"类型的记录', $before_count, $operation),
			$user_id
		);
		
		// 执行删除操作，使用LIMIT避免长时间锁表
		$batch_size = 1000; // 每批删除的记录数
		$deleted_count = 0;
		
		do {
			$result = $wpdb->query(
				$wpdb->prepare(
					"DELETE FROM {$wpdb->prefix}audit_trail WHERE operation = %s LIMIT %d",
					$operation,
					$batch_size
				)
			);
			
			if ($result) {
				$deleted_count += $result;
			} else {
				// 删除出错，回滚
				$wpdb->query('ROLLBACK');
				return 0;
			}
			
		} while ($result == $batch_size);
		
		// 提交事务
		$wpdb->query('COMMIT');
		
		return $deleted_count;
	}
	
	/**
	 * 删除指定天数之前的记录
	 * 
	 * @param int $days 天数
	 * @return int 删除的记录数
	 */
	static function delete_entries_older_than($days) {
		global $wpdb;
		
		// 验证天数参数
		$days = (int)$days;
		if ($days <= 0) {
			return 0;
		}
		
		// 计算截止日期
		$date = new DateTime();
		$date->modify("-$days days");
		$cutoff_date = $date->format('Y-m-d H:i:s');
		
		// 记录删除前的统计信息
		$before_count = $wpdb->get_var(
			$wpdb->prepare(
				"SELECT COUNT(*) FROM {$wpdb->prefix}audit_trail WHERE happened_at < %s",
				$cutoff_date
			)
		);
		
		if (empty($before_count) || $before_count <= 0) {
			return 0;
		}
		
		// 使用事务确保操作的完整性
		$wpdb->query('START TRANSACTION');
		
		// 记录此删除操作
		$log_data = [
			'days' => $days,
			'cutoff_date' => $cutoff_date,
			'count' => $before_count,
			'deleted_at' => current_time('mysql')
		];
		
		// 获取当前用户
		$current_user = wp_get_current_user();
		$user_id = $current_user && $current_user->ID ? $current_user->ID : 0;
		
		// 创建删除操作的日志
		self::create(
			'manual_cleanup',
			0,
			$log_data,
			sprintf('删除了%d条%d天前的记录', $before_count, $days),
			$user_id
		);
		
		// 执行删除操作，使用LIMIT避免长时间锁表
		$batch_size = 1000; // 每批删除的记录数
		$deleted_count = 0;
		
		do {
			$result = $wpdb->query(
				$wpdb->prepare(
					"DELETE FROM {$wpdb->prefix}audit_trail WHERE happened_at < %s LIMIT %d",
					$cutoff_date,
					$batch_size
				)
			);
			
			if ($result) {
				$deleted_count += $result;
			} else {
				// 删除出错，回滚
				$wpdb->query('ROLLBACK');
				return 0;
			}
			
		} while ($result == $batch_size);
		
		// 提交事务
		$wpdb->query('COMMIT');
		
		return $deleted_count;
	}
}
