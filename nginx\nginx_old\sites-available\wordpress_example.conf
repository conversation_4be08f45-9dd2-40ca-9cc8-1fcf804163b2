server {
    listen 80;
    server_name example.com www.example.com;  # 请替换为您的域名
    root /var/www/example.com;                # 请替换为您的WordPress安装目录
    index index.php index.html;
    
    # 日志配置
    access_log /var/log/nginx/example.com.access.log;
    error_log /var/log/nginx/example.com.error.log;
    
    # 引入通用规则
    include /etc/nginx/rules/wordpress_common.conf;
    include /etc/nginx/rules/php_security.conf;
    include /etc/nginx/rules/wordpress_security.conf;
    include /etc/nginx/rules/static_files.conf;
    include /etc/nginx/rules/file_access.conf;
    include /etc/nginx/rules/wp-admin.conf;
    include /etc/nginx/rules/path_traversal.conf;
    
    # 如果有特定于此站点的规则，可以在此处添加
} 