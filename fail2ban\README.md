# Fail2Ban 管理脚本

作者: vince

## 简介

这是一个轻量级的Fail2Ban管理脚本，提供命令行界面来简化Fail2Ban的安装、配置和管理。脚本支持多种Linux发行版，能够自动检测系统类型和防火墙类型，适配相应的配置。

## 主要功能

### 系统检测与配置
- 自动检测系统类型（Ubuntu/Debian/CentOS/RHEL等）
- 自动检测防火墙类型（UFW/FirewallD/iptables）
- 根据系统类型和防火墙类型调整配置

### 安装与基础配置
- 安装Fail2Ban及所需依赖
- 安装并配置防火墙
- 创建基础过滤器和动作配置
- 自动创建白名单和通知脚本

### 站点管理
- 添加所有网站到监控列表
- 追加单个站点到监控列表
- 从监控列表中删除站点
- 添加/删除默认日志监控
- 查看已配置的站点列表

### IP封禁管理
- 查看所有封禁的IP列表
- 解除特定IP的封禁
- 管理IP白名单（添加/删除）

### 监控与状态
- 显示Fail2Ban服务状态
- 查看各个jail的详细状态
- 查看封禁IP统计信息

## 支持的防火墙

- UFW (Ubuntu默认)
- FirewallD (CentOS/RHEL默认)
- iptables (通用支持)

## 使用方法

1. 使用root权限运行脚本
   ```bash
   sudo bash fail2ban.sh
   ```

2. 在主菜单中选择相应选项：
   - 1: 安装和配置Fail2Ban和防火墙
   - 2: 添加所有站点
   - 3: 管理站点配置
   - 4: 列出封禁的IP
   - 5: 解除IP封禁
   - 6: 列出已配置的站点
   - 7: 修改IP白名单
   - 8: 显示fail2ban状态
   - 0: 退出

## 详细使用教程

### 1. 安装和配置Fail2Ban

选择主菜单中的选项`1`，脚本将自动执行以下操作：
- 检测系统类型和防火墙类型
- 安装Fail2Ban和所需依赖
- 安装并配置合适的防火墙（如果未安装）
- 创建基础过滤器和动作配置
- 配置基本的防火墙规则（开放SSH和检测到的其他服务端口）

安装完成后，Fail2Ban服务将自动启动并运行。

### 2. 添加所有站点

选择主菜单中的选项`2`，脚本将：
- 扫描Nginx日志目录(`/var/log/nginx`)，查找所有近期活跃的站点日志
- 为每个找到的站点创建相应的监控配置
- 自动重启Fail2Ban服务以应用新配置

注意：此操作将清除现有的站点配置并重新添加所有站点。

### 3. 管理站点配置

选择主菜单中的选项`3`，进入站点配置管理子菜单：

1. **追加单个站点**
   - 列出所有可追加的站点（已有日志但未配置的站点）
   - 选择要追加的站点后添加到监控列表

2. **删除单个站点**
   - 列出所有已配置的站点
   - 选择要删除的站点从监控列表中移除

3. **添加默认日志监控**
   - 将Nginx的默认访问日志(access.log)添加到监控列表

4. **删除默认日志监控**
   - 从监控列表中移除默认日志监控

### 4. 列出封禁的IP

选择主菜单中的选项`4`，脚本将：
- 连接到Fail2Ban服务
- 获取所有活跃的jail列表
- 显示每个jail中当前封禁的IP
- 显示总计封禁IP数量

### 5. 解除IP封禁

选择主菜单中的选项`5`，可以：
- 输入要解封的IP地址
- 确认后，脚本将从所有jail中解除该IP的封禁
- 显示解封操作的结果

### 6. 列出已配置的站点

选择主菜单中的选项`6`，脚本将：
- 读取配置文件
- 列出所有已配置的站点及其状态

### 7. 修改IP白名单

选择主菜单中的选项`7`，进入IP白名单管理：

1. **添加IP到白名单**
   - 输入要添加的IP地址（多个IP用逗号分隔）
   - 脚本将更新配置文件并重启服务

2. **从白名单中删除IP**
   - 查看当前白名单中的IP列表
   - 选择要删除的IP编号
   - 脚本将更新配置文件并重启服务

被加入白名单的IP将永远不会被Fail2Ban封禁。

### 8. 显示fail2ban状态

选择主菜单中的选项`8`，脚本将：
- 检查Fail2Ban服务运行状态
- 显示版本信息
- 显示所有jail的详细状态
- 显示每个jail的过滤器、动作、当前封禁IP数等信息

## 配置文件

脚本使用以下主要配置文件：
- `/etc/fail2ban/jail.local`: 主配置文件
- `/etc/fail2ban/filter.d/`: 过滤器配置目录
- `/etc/fail2ban/action.d/`: 动作配置目录

## 日志文件

- 默认监控Nginx日志目录: `/var/log/nginx`
- 封禁/解封操作日志: `/var/log/fail2ban-actions.log`
- UFW操作日志: `/root/ufw.log`

## 注意事项

- 脚本需要root权限才能运行
- 修改配置后会自动重启Fail2Ban服务
- 默认封禁时间为3600秒(1小时)
- 默认最大重试次数为15次
- 默认查找时间为600秒(10分钟)
- 脚本已预配置了一些常见的IP白名单

## 自定义

可以通过编辑脚本开头的变量来自定义：
- 封禁时间
- 重试次数
- 查找时间窗口
- 日志目录
- 配置文件位置

## 故障排除

### Fail2Ban服务无法启动
- 检查配置文件语法是否正确
- 查看系统日志: `journalctl -u fail2ban`
- 确认防火墙服务是否正常运行

### 找不到站点日志
- 确认Nginx日志路径是否正确
- 检查日志文件权限
- 确认日志文件最近是否有更新（一天内）

### IP已封禁但仍能访问
- 检查防火墙规则是否正确应用: `iptables -L` 或 `ufw status`
- 确认Fail2Ban与防火墙的集成是否正常
- 检查IP是否在白名单中 