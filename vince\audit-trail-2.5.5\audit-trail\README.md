# WordPress Audit Trail 插件

## 插件概述
Audit Trail 是一款全面的 WordPress 管理活动审计和日志记录插件，专为详细追踪博客后台的所有操作而设计，支持数据恢复和版本控制功能。插件使用中文界面，提供强大的日志记录和搜索功能，同时注重性能优化和资源消耗控制。

## 当前版本2.5.3

## 核心功能

### 全面的操作日志
- **文章与页面管理**：记录创建、编辑、删除、状态变更等操作
- **页面构建器操作**：支持主流页面构建器（Elementor、Beaver Builder、Divi、WPBakery、SiteOrigin、Gutenberg）
- **用户活动**：记录登录、登出、密码重置、注册、个人资料更新、登录失败等
- **媒体库操作**：记录文件上传、编辑、删除等
- **评论管理**：记录评论编辑和删除操作
- **分类目录和链接**：记录分类和链接的添加、编辑、删除
- **主题和插件**：记录主题切换和插件激活/停用
- **页面访问**：可选择记录用户和访客的页面访问情况

### 日志管理功能
- **高级搜索**：按用户名、操作类型、IP地址、日期范围搜索
- **CSV导出**：支持将日志导出为CSV文件进行离线分析
- **权限控制**：可禁止特定用户使用审计插件
- **数据过滤**：包括排除页面访问记录等功能
- **自动清理**：支持自动清理过期日志，避免数据库膨胀
- **批量处理**：支持按日期和操作类型批量删除日志记录
- **系统状态**：显示日志统计信息和清理历史

### 性能优化功能
- **高流量模式**：针对高流量网站优化，减少数据库压力
- **智能采样**：页面访问日志的采样记录，可自定义采样率（1-100%）
- **批量日志记录**：减少数据库写入频率，提高性能
- **静态资源排除**：自动排除静态资源路径，减少无用日志
- **排除路径**：可配置不记录日志的URL路径
- **数据库优化**：通过索引提高查询性能

### 安全增强特性
- **敏感数据保护**：自动屏蔽密码等敏感信息
- **IPv6支持**：完整支持IPv4和IPv6地址记录
- **用户权限控制**：精细化的访问权限管理
- **安全验证**：使用WordPress nonce验证所有操作

## 技术实现与架构

### 数据存储结构
插件在WordPress数据库中创建`{wp_prefix}audit_trail`表，包含以下字段：
- `id`：日志记录的唯一标识符
- `operation`：操作类型（如save_post, wp_login等）
- `user_id`：执行操作的用户ID
- `ip`：用户IP地址（支持IPv4和IPv6）
- `happened_at`：操作发生的时间
- `item_id`：相关项目的ID（如文章ID、用户ID等）
- `data`：操作相关的详细数据（JSON格式）
- `title`：操作项目的标题或名称

### 数据库优化
- **索引优化**：为关键字段（operation, user_id, happened_at, ip）添加索引
- **IP字段升级**：从int类型升级为varchar(45)以支持IPv6格式
- **数据清理**：自动或手动清理过期数据，避免数据库膨胀
- **批量操作**：批量插入和删除优化

### 核心类与文件结构
1. **主要类**
   - `Audit_Trail`：主插件类，负责初始化、菜单创建和基本功能
   - `AT_Auditor`：核心监听类，处理所有操作的记录
   - `AT_Audit`：日志记录模型类，提供数据库操作接口
   - `Audit_Trail_Table`：表格显示类，继承WP_List_Table
   - `AuditAjax`：处理AJAX请求的类
   - `AuditTrailBatchLogger`：批量日志处理类，优化高流量站点性能

2. **文件结构**
   - `audit-trail.php`：主插件文件，包含初始化代码
   - `models/auditor.php`：包含监听和记录操作的核心逻辑
   - `models/audit.php`：数据模型和数据库操作
   - `models/pager.php`：分页和表格显示逻辑
   - `models/batch-logger.php`：批量日志处理逻辑
   - `view/`：包含界面模板
   - `locale/`：多语言支持文件
   - `ajax.php`：处理AJAX请求
   - `csv.php`：处理CSV导出功能

### 钩子系统
插件通过WordPress钩子系统监听各类操作，主要包括：
- 文章相关：`save_post`, `delete_post`, 多种状态转换钩子
- 用户相关：`wp_login`, `wp_logout`, `user_register`, `profile_update`, `login_errors`, `wp_login_failed`等
- 评论相关：`edit_comment`, `delete_comment`
- 附件相关：`add_attachment`, `edit_attachment`, `delete_attachment`
- 分类相关：`add_category`, `edit_category`, `delete_category`
- 链接相关：`add_link`, `edit_link`, `delete_link`
- 主题相关：`switch_theme`
- 插件相关：`activate_plugin`, `deactivate_plugin`
- 页面访问：`template_redirect`
- 页面构建器相关：
  - Elementor: `elementor/editor/after_save`, `elementor/document/after_save`, `elementor/editor/before_save`
  - Beaver Builder: `fl_builder_after_save_layout`, `fl_builder_before_save_layout`, `fl_builder_after_layout_rendered`
  - SiteOrigin: `siteorigin_panels_save_post`, `siteorigin_panels_after_render`
  - WPBakery: `vc_after_save_post`, `vc_before_save_post`, `vc_after_update`
  - Divi: `et_fb_save_layout`, `et_fb_ajax_save`, `et_builder_after_save_layout`
  - Gutenberg: `blocks_parsed`

### 自动任务系统
- **计划任务**：使用WordPress cron系统实现自动清理过期日志
- **关机钩子**：使用`shutdown`钩子确保批量日志在页面加载完成后记录
- **版本升级**：自动检测和平滑处理插件版本升级

## 安全设计
- **权限控制**：限制只有具有`publish_posts`、`audit_trail`或`edit_plugins`权限的用户才能访问审计日志
- **敏感数据处理**：自动识别并屏蔽密码、令牌、密钥等敏感信息
- **用户过滤**：可配置忽略特定用户的操作
- **禁用权限**：可设置禁止特定用户使用审计插件
- **IPv6支持**：适当处理IPv4和IPv6地址
- **CSV导出保护**：验证nonce和用户权限，防止未授权导出

## 性能优化设计
- **批量日志记录**：通过`AuditTrailBatchLogger`类实现页面访问日志的批量提交
- **智能采样**：可配置页面访问日志的采样率，减少数据库写入
- **静态资源排除**：自动排除CSS、JS、图片等静态资源的访问记录
- **自动清理机制**：定期清理过期日志，防止数据库膨胀
- **数据库索引**：优化查询性能的索引设计
- **分批删除**：使用LIMIT限制每次删除的数量，避免锁表

## 自定义扩展
插件提供多个过滤器和动作钩子，便于开发者自定义和扩展功能：
- `audit_collect`：添加新的监听操作类型
- `audit_show_operation`：自定义操作类型的显示方式
- `audit_show_item`：自定义操作项目的显示方式
- `audit_show_details`：自定义操作详情的显示方式
- `audit_listen`：添加自定义操作的监听钩子
- `audit_operation`：进一步自定义操作显示方式

## 用户界面特点
- **AJAX技术**：使用AJAX加载详细信息，减少页面刷新
- **响应式设计**：适应不同屏幕尺寸的管理界面
- **数据过滤**：提供多种过滤选项，便于管理大量日志数据
- **批量操作**：支持批量删除日志记录
- **自定义搜索**：高级搜索和过滤功能
- **统计信息**：显示日志数量和系统状态

## 配置选项
- **日志保留期**：设置自动清理日志的保留天数（默认30天）
- **高流量模式**：启用/禁用高流量优化功能（默认启用）
- **采样率设置**：设置页面访问日志的记录百分比（默认10%）
- **排除路径**：设置不记录日志的URL路径
- **忽略用户**：设置不记录其操作的用户ID
- **禁止用户**：设置禁止访问审计功能的用户ID

## 语言支持
默认使用简体中文界面，同时支持多种语言：
- 简体中文（默认）
- 英语
- 爱沙尼亚语
- 白俄罗斯语
- 德语
- 日语
- 罗马尼亚语
- 立陶宛语

## 版本更新与升级
插件包含自动升级机制，可以平滑处理数据库结构变更：
- 版本2.5.0引入了性能优化和安全增强功能
- 自动添加数据库索引提高查询性能
- IP字段从int类型升级为varchar(45)以支持IPv6
- 引入计划任务实现自动日志清理

## 未来功能规划
- **智能日志聚合**：合并短时间内的重复操作，进一步减少数据库负担
- **轻量级统计图表**：以简单图表展示用户活动情况，不增加过多资源消耗
- **关键操作通知**：仅针对重要操作发送通知，减少干扰
- **搜索结果导出优化**：仅导出必要字段，提高导出效率
- **更多页面构建器支持**：扩展对其他流行页面构建器的支持
