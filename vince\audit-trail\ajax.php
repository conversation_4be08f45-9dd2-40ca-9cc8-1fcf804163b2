<?php

/* ============================================================================================================
	 This software is provided "as is" and any express or implied warranties, including, but not limited to, the
	 implied warranties of merchantibility and fitness for a particular purpose are disclaimed. In no event shall
	 the copyright owner or contributors be liable for any direct, indirect, incidental, special, exemplary, or
	 consequential damages (including, but not limited to, procurement of substitute goods or services; loss of
	 use, data, or profits; or business interruption) however caused and on any theory of liability, whether in
	 contract, strict liability, or tort (including negligence or otherwise) arising in any way out of the use of
	 this software, even if advised of the possibility of such damage.

	 This software is provided free-to-use, but is not free software.  The copyright and ownership remains
	 entirely with the author.  Please distribute and use as necessary, in a personal or commercial environment,
	 but it cannot be sold or re-used without express consent from the author.
   ============================================================================================================ */

/**
 * Provide Audit Trail AJAX
 *
 * @package Audit Trail
 * <AUTHOR> Godley
 **/

/**
 * 处理AJAX请求的类
 * 
 * 安全设计：
 * 1. 所有请求都经过nonce验证
 * 2. 使用权限检查确保只有授权用户可以访问
 * 3. 所有输出都经过适当的转义
 * 4. 添加防爬虫保护
 */
class AuditAjax {
	function __construct() {
		add_action('wp_ajax_at_lookup_detail', array($this, 'ajax_lookup_detail'));
		add_action('wp_ajax_at_get_users', array($this, 'ajax_get_users'));
		
		// 设置AJAX请求的安全头
		add_action('admin_init', array($this, 'set_ajax_headers'));

		if ( current_user_can( 'manage_options' ) ) {
			add_action( 'wp_ajax_at_view', array( &$this, 'at_view' ) );
			add_action( 'wp_ajax_at_close', array( &$this, 'at_close' ) );
		}

		// 添加CSV导出AJAX操作
		add_action( 'wp_ajax_audit_export_csv', array( &$this, 'export_csv' ) );
	}
	
	/**
	 * 为AJAX请求设置安全头
	 */
	function set_ajax_headers() {
		if (wp_doing_ajax() && isset($_REQUEST['action']) && 
		    (strpos($_REQUEST['action'], 'at_') === 0)) {
			// 设置安全头部
			if (!headers_sent()) {
				header('X-Content-Type-Options: nosniff');
				header('X-XSS-Protection: 1; mode=block');
				header('X-Frame-Options: SAMEORIGIN');
				// 禁止缓存AJAX响应
				header('Cache-Control: no-store, no-cache, must-revalidate, max-age=0');
				header('Pragma: no-cache');
				header('Expires: Thu, 01 Jan 1970 00:00:00 GMT');
			}
		}
	}
	
	/**
	 * 验证用户权限
	 * 
	 * @return bool 用户是否有权限
	 */
	private function check_permissions() {
		// 检查用户是否有权限访问
		if (!current_user_can('publish_posts') && !current_user_can('audit_trail') && !current_user_can('edit_plugins')) {
			wp_send_json_error(['message' => '权限不足']);
			exit;
		}
		
		// 检查用户是否被禁止使用插件
		$current_user_id = get_current_user_id();
		$forbidden_users = explode(',', get_option('audit_forbidden_users', ''));
		if (in_array($current_user_id, $forbidden_users)) {
			wp_send_json_error(['message' => '您被禁止使用此功能']);
			exit;
		}
		
		return true;
	}
	
	/**
	 * 防爬虫检查
	 */
	private function check_bot() {
		$user_agent = isset($_SERVER['HTTP_USER_AGENT']) ? $_SERVER['HTTP_USER_AGENT'] : '';
		$bot_patterns = ['bot', 'crawl', 'spider', 'slurp', 'mediapartners-google', 'baidu', 'bing'];
		
		foreach ($bot_patterns as $pattern) {
			if (stripos($user_agent, $pattern) !== false) {
				wp_send_json_error(['message' => '请求被拒绝']);
				exit;
			}
		}
		
		// 检查浏览器特征标识，大多数爬虫不设置Accept或Referer头
		if (empty($_SERVER['HTTP_ACCEPT']) || empty($_SERVER['HTTP_REFERER'])) {
			// 增加请求的复杂度，但不直接拒绝(可能有合法请求没有这些头)
			sleep(1); // 短暂延迟
		}
	}
	
	/**
	 * 查询日志详情的AJAX处理函数
	 */
	function ajax_lookup_detail() {
		// 安全检查
		$this->check_permissions();
		$this->check_bot();
		
		// 验证nonce
		if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'audit-trail-lookup')) {
			wp_send_json_error(['message' => '安全验证失败']);
			exit;
		}
		
		// 安全处理输入
		$id = isset($_POST['id']) ? intval($_POST['id']) : 0;
		if ($id <= 0) {
			wp_send_json_error(['message' => '无效的ID参数']);
			exit;
		}
		
		// 获取记录
		global $wpdb;
		$item = $wpdb->get_row($wpdb->prepare(
			"SELECT {$wpdb->prefix}audit_trail.*,{$wpdb->users}.user_nicename AS username 
			FROM {$wpdb->prefix}audit_trail 
			LEFT JOIN {$wpdb->users} ON {$wpdb->users}.ID={$wpdb->prefix}audit_trail.user_id 
			WHERE {$wpdb->prefix}audit_trail.id=%d",
			$id
		));
		
		if (!$item) {
			wp_send_json_error(['message' => '找不到对应的记录']);
			exit;
		}
		
		// 转换为AT_Audit对象
		require_once(dirname(__FILE__) . '/models/audit.php');
		$audit = new AT_Audit($item);
		
		// 准备安全的输出数据
		$result = [
			'id' => $item->id,
			'operation' => esc_html($audit->get_operation()),
			'details' => $audit->get_details(),
			'user' => esc_html($item->username ? $item->username : '未知用户'),
			'user_id' => intval($item->user_id),
			'date' => mysql2date(get_option('date_format') . ' ' . get_option('time_format'), $item->happened_at),
			'ip' => esc_html($item->ip),
			'item_id' => intval($item->item_id),
			'title' => esc_html($item->title),
		];
		
		// 安全的将data转换为可显示的格式
		$data = $item->data;
		if ($data) {
			if (is_serialized($data)) {
				$data = maybe_unserialize($data);
			} else {
				$json = json_decode($data);
				if (json_last_error() === JSON_ERROR_NONE) {
					$data = $json;
				}
			}
			
			// 如果结果是数组或对象，格式化显示
			if (is_array($data) || is_object($data)) {
				$result['data'] = '<pre>' . esc_html(print_r($data, true)) . '</pre>';
			} else {
				$result['data'] = esc_html($data);
			}
		} else {
			$result['data'] = '无数据';
		}
		
		// 记录查看详情的操作
		AT_Audit::create('view_audit_detail', $id, '', sprintf('查看日志ID: %d', $id));
		
		wp_send_json_success($result);
	}
	
	/**
	 * 获取用户列表的AJAX处理函数 - 用于设置禁止用户
	 */
	function ajax_get_users() {
		// 安全检查
		$this->check_permissions();
		$this->check_bot();
		
		// 验证nonce
		if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'audit-trail-get-users')) {
			wp_send_json_error(['message' => '安全验证失败']);
			exit;
		}
		
		// 只查询必要的字段，限制用户数量
		$args = [
			'fields' => ['ID', 'user_login', 'display_name'],
			'number' => 100 // 限制返回用户数量
		];
		
		$users = get_users($args);
		$filtered_users = [];
		
		// 过滤和清理数据
		foreach ($users as $user) {
			$filtered_users[] = [
				'ID' => intval($user->ID),
				'user_login' => esc_html($user->user_login),
				'display_name' => esc_html($user->display_name)
			];
		}
		
		wp_send_json_success($filtered_users);
	}

	function at_view() {
		if ( check_ajax_referer( 'audittrail_view' ) ) {
			$id = intval( $_POST['id'] );

			$item = AT_Audit::get( $id );
			$this->render( 'trail_details', array( 'item' => $item ) );

			die();
		}
	}

	function at_close( $item ) {
		if ( check_ajax_referer( 'audittrail_view' ) ) {
			$id = intval( $_POST['id'] );

			$item = AT_Audit::get ($id);
			$this->render ('trail_item', array ('item' => $item));

			die();
		}
	}

	/**
	 * 处理CSV导出AJAX请求
	 * 这个函数作为中介，将请求重定向到csv.php，以避免NGINX拦截
	 */
	function export_csv() {
		// 验证用户权限
		if (!current_user_can('publish_posts') && !current_user_can('audit_trail') && !current_user_can('edit_plugins')) {
			wp_die(__('您没有足够的权限导出数据', 'audit-trail'), __('权限错误', 'audit-trail'), array('response' => 403));
			return;
		}
		
		// 包含CSV导出文件
		include_once(dirname(__FILE__) . '/csv.php');
		exit;
	}

	private function render( $template, $template_vars = array() ) {
		foreach ( $template_vars AS $key => $val ) {
			$$key = $val;
		}

		if ( file_exists( dirname( __FILE__ )."/view/admin/$template.php" ) )
			include dirname( __FILE__ )."/view/admin/$template.php";
	}
}
