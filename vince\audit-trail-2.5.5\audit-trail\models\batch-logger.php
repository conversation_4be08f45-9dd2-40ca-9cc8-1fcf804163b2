<?php
/**
 * AuditTrailBatchLogger - 批量日志记录器
 * 
 * 用于高流量站点优化，减少数据库写入频率
 * 通过批量提交机制，提高页面访问日志的记录效率
 * 
 * @package Audit Trail
 */

class AuditTrailBatchLogger {
    /**
     * 存储待批量处理的页面访问日志
     * @var array
     */
    private static $page_view_logs = [];
    
    /**
     * 每批次最大记录数量
     * @var int
     */
    private static $max_batch_size = 20;
    
    /**
     * 添加一条页面访问记录到批处理队列
     * 
     * @param int $item_id 相关项目ID（如文章ID）
     * @param array|object $data 日志详细数据
     * @param string $title 标题
     * @param int $user_id 用户ID
     */
    public static function add_page_view($item_id, $data, $title, $user_id) {
        // 添加到批处理队列
        self::$page_view_logs[] = [
            'operation' => 'template_redirect',
            'item_id' => (int)$item_id,
            'data' => $data,
            'title' => (string)$title,
            'user_id' => (int)$user_id,
            'time' => current_time('mysql'),
            'ip' => AT_Audit::get_ip()
        ];
        
        // 当队列达到最大批次大小时自动处理
        if (count(self::$page_view_logs) >= self::$max_batch_size) {
            self::flush();
        }
    }
    
    /**
     * 强制处理队列中的所有记录
     */
    public static function flush() {
        global $wpdb;
        
        // 如果队列为空，直接返回
        if (empty(self::$page_view_logs)) {
            return;
        }
        
        // 批量插入
        $values = [];
        $placeholders = [];
        
        foreach (self::$page_view_logs as $log) {
            $values[] = $log['user_id'];
            $values[] = $log['ip'];
            $values[] = $log['operation'];
            $values[] = $log['item_id'];
            $values[] = $log['time'];
            $values[] = maybe_jsonize($log['data']);
            $values[] = substr((string)$log['title'], 0, 100); // 确保标题长度不超过100
            
            $placeholders[] = "(%d, %s, %s, %d, %s, %s, %s)";
        }
        
        $table_name = $wpdb->prefix . 'audit_trail';
        $query = "INSERT IGNORE INTO {$table_name} 
                  (user_id, ip, operation, item_id, happened_at, data, title) 
                  VALUES " . implode(', ', $placeholders);
        
        $wpdb->query($wpdb->prepare($query, $values));
        
        // 清空队列
        self::$page_view_logs = [];
    }
}

// 在WordPress关闭时确保所有日志都被记录
add_action('shutdown', ['AuditTrailBatchLogger', 'flush']); 