<?php
/**
 * AuditTrailBatchLogger - 批量日志记录器
 * 
 * 用于高流量站点优化，减少数据库写入频率
 * 通过批量提交机制，提高页面访问日志的记录效率
 * 
 * @package Audit Trail
 */

// 检查类是否已经存在，避免重定义错误
if (!class_exists('AT_Batch_Logger')) {
    /**
     * 批量日志记录类
     * 优化高流量站点的日志处理
     */
    class AT_Batch_Logger {
        private static $instance = null;
        private $log_queue = array();
        private $max_batch_size = 20;
        private $is_shutdown_registered = false;
        
        /**
         * 获取单例实例
         */
        public static function get_instance() {
            if (self::$instance === null) {
                self::$instance = new self();
            }
            return self::$instance;
        }
        
        /**
         * 构造函数
         */
        private function __construct() {
            // 初始化
            $this->max_batch_size = apply_filters('audit_trail_batch_size', 20);
        }
        
        /**
         * 向队列添加日志项
         */
        public function add_to_queue($operation, $item = '', $data = '', $title = '', $user = false) {
            // 只有在高流量模式下才使用批量记录
            if (!get_option('audit_high_volume_mode', true)) {
                // 直接记录而不进队列
                return AT_Audit::create($operation, $item, $data, $title, $user);
            }
            
            // 添加到队列
            $this->log_queue[] = array(
                'operation' => $operation,
                'item' => $item,
                'data' => $data,
                'title' => $title,
                'user' => $user
            );
            
            // 如果达到批量大小，立即处理
            if (count($this->log_queue) >= $this->max_batch_size) {
                $this->process_queue();
            }
            
            // 确保在PHP关闭时处理剩余队列
            if (!$this->is_shutdown_registered) {
                $this->is_shutdown_registered = true;
                register_shutdown_function(array($this, 'process_queue'));
            }
            
            return true;
        }
        
        /**
         * 处理日志队列
         */
        public function process_queue() {
            if (empty($this->log_queue)) {
                return;
            }
            
            // 使用事务批量处理日志
            global $wpdb;
            $wpdb->query('START TRANSACTION');
            
            try {
                foreach ($this->log_queue as $log_item) {
                    AT_Audit::create(
                        $log_item['operation'],
                        $log_item['item'],
                        $log_item['data'],
                        $log_item['title'],
                        $log_item['user']
                    );
                }
                $wpdb->query('COMMIT');
            } catch (Exception $e) {
                $wpdb->query('ROLLBACK');
                // 记录错误
                error_log('Audit Trail批量日志处理错误: ' . $e->getMessage());
            }
            
            // 清空队列
            $this->log_queue = array();
        }
    }
}

// 修改最后的shutdown钩子注册
// 在WordPress关闭时确保所有日志都被记录
if (class_exists('AT_Batch_Logger')) {
    // 使用单例模式获取实例
    add_action('shutdown', [AT_Batch_Logger::get_instance(), 'process_queue']);
} 