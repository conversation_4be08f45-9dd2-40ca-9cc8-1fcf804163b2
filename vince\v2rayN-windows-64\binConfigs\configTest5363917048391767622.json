{"log": {"level": "warn", "timestamp": true}, "dns": {"servers": [{"tag": "local_local", "address": "*********", "detour": "direct"}], "rules": [{"server": "local_local", "domain": ["hy2-sg1.dhh.moe"]}, {"server": "remote", "clash_mode": "Global"}, {"server": "local_local", "clash_mode": "Direct"}]}, "inbounds": [{"type": "mixed", "tag": "mixed10861", "listen": "127.0.0.1", "listen_port": 10861}], "outbounds": [{"type": "direct", "tag": "direct"}, {"type": "block", "tag": "block"}, {"type": "dns", "tag": "dns_out"}, {"type": "hysteria2", "tag": "proxy10861", "server": "hy2-sg1.dhh.moe", "server_port": 14514, "up_mbps": 100, "down_mbps": 100, "password": "05ee387e-5361-4a01-9260-33fd2828dd7b", "tls": {"enabled": true, "server_name": "ssl.dhh.ac.cn", "insecure": true}}], "route": {"rules": [{"outbound": "dns_out", "protocol": ["dns"]}, {"outbound": "proxy10861", "inbound": ["mixed10861"]}]}}