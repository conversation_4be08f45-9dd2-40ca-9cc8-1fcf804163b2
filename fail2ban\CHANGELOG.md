# Fail2Ban 管理脚本更新日志

## [1.0.0] - 2023-06-01

### 初始版本
- 基础脚本框架创建
- 支持系统类型检测（Ubuntu/Debian/CentOS等）
- 支持防火墙类型检测（UFW/FirewallD/iptables）
- 实现Fail2Ban和防火墙的安装配置
- 添加基础站点管理功能
- 添加IP封禁管理功能
- 创建简单的命令行界面

## [1.1.0] - 2023-07-15

### 新增
- 添加彩色输出和友好界面
- 添加进度条显示
- 添加默认日志监控功能
- 添加IP白名单管理

### 改进
- 优化站点管理逻辑
- 改进服务状态检测
- 提升日志文件处理效率
- 美化菜单和列表显示

### 修复
- 修复特定系统下防火墙配置问题
- 修复部分命令执行权限问题
- 修复白名单IP格式验证

## [1.2.0] - 2023-08-30

### 新增
- 添加通知脚本支持
- 添加解封IP确认提示
- 添加站点配置备份功能

### 改进
- 优化配置文件处理逻辑
- 提升防火墙规则管理效率
- 改进错误处理和日志记录

### 修复
- 修复在某些发行版上安装依赖的问题
- 修复白名单格式处理问题
- 修复防火墙规则冲突问题

## [1.3.0] - 2023-10-20

### 新增
- 添加系统资源监控
- 添加自动更新功能
- 支持更多Linux发行版

### 改进
- 优化UI界面
- 提升配置读写效率
- 改进站点检测算法

### 修复
- 修复服务重启失败问题
- 修复日志路径检测问题
- 修复IP解封后未同步到防火墙的问题 