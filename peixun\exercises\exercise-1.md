# 练习1：搭建第一个网站

## 🎯 练习目标
学会安装 Nginx 并创建一个简单的个人网站

## 📋 练习步骤

### 步骤1：安装 Nginx

**Ubuntu/Debian 系统：**
```bash
# 更新软件包列表
sudo apt update

# 安装 Nginx
sudo apt install nginx

# 启动 Nginx
sudo systemctl start nginx

# 设置开机自启动
sudo systemctl enable nginx
```

**CentOS/RHEL 系统：**
```bash
# 安装 Nginx
sudo yum install nginx

# 启动 Nginx
sudo systemctl start nginx

# 设置开机自启动
sudo systemctl enable nginx
```

### 步骤2：验证安装

1. 打开浏览器
2. 输入你的服务器IP地址（如果是本地安装，输入 `localhost` 或 `127.0.0.1`）
3. 应该看到 Nginx 的欢迎页面

### 步骤3：创建个人网站

1. **创建网站目录：**
```bash
sudo mkdir -p /var/www/mysite
```

2. **创建首页文件：**
```bash
sudo nano /var/www/mysite/index.html
```

3. **编写网页内容：**
```html
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>我的第一个网站</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
        }
        .info {
            background: #e3f2fd;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎉 欢迎来到我的网站！</h1>
        
        <div class="info">
            <h2>关于我</h2>
            <p>这是我用 Nginx 搭建的第一个网站。</p>
            <p>我正在学习 Web 服务器技术！</p>
        </div>
        
        <div class="info">
            <h2>技术栈</h2>
            <ul>
                <li>Web 服务器：Nginx</li>
                <li>前端：HTML + CSS</li>
                <li>系统：Linux</li>
            </ul>
        </div>
        
        <div class="info">
            <h2>联系方式</h2>
            <p>邮箱：<EMAIL></p>
            <p>网站创建时间：<span id="datetime"></span></p>
        </div>
    </div>
    
    <script>
        // 显示当前时间
        document.getElementById('datetime').textContent = new Date().toLocaleString('zh-CN');
    </script>
</body>
</html>
```

### 步骤4：配置 Nginx

1. **创建网站配置文件：**
```bash
sudo nano /etc/nginx/sites-available/mysite
```

2. **编写配置内容：**
```nginx
server {
    listen 80;
    server_name localhost;  # 如果有域名，替换为你的域名
    
    root /var/www/mysite;
    index index.html;
    
    location / {
        try_files $uri $uri/ =404;
    }
    
    # 访问日志
    access_log /var/log/nginx/mysite.access.log;
    error_log /var/log/nginx/mysite.error.log;
}
```

3. **启用网站配置：**
```bash
# 创建符号链接
sudo ln -s /etc/nginx/sites-available/mysite /etc/nginx/sites-enabled/

# 测试配置
sudo nginx -t

# 重新加载配置
sudo systemctl reload nginx
```

### 步骤5：测试网站

1. 打开浏览器
2. 访问你的网站
3. 应该看到你创建的个人网站

## ✅ 检查清单

完成练习后，请检查以下项目：

- [ ] Nginx 已成功安装
- [ ] Nginx 服务正在运行
- [ ] 可以访问默认的 Nginx 欢迎页面
- [ ] 创建了个人网站目录和文件
- [ ] 配置了 Nginx 虚拟主机
- [ ] 可以正常访问个人网站
- [ ] 网站显示正确的内容和样式

## 🚨 常见问题

### 问题1：无法访问网站
**可能原因：**
- 防火墙阻止了80端口
- Nginx 没有启动
- 配置文件有错误

**解决方法：**
```bash
# 检查 Nginx 状态
sudo systemctl status nginx

# 检查防火墙
sudo ufw status
sudo ufw allow 80

# 检查配置
sudo nginx -t
```

### 问题2：显示403 Forbidden
**可能原因：**
- 文件权限不正确
- 目录权限不正确

**解决方法：**
```bash
# 设置正确的权限
sudo chown -R www-data:www-data /var/www/mysite
sudo chmod -R 755 /var/www/mysite
```

### 问题3：配置修改不生效
**解决方法：**
```bash
# 重新加载配置
sudo systemctl reload nginx

# 如果还不行，重启服务
sudo systemctl restart nginx
```

## 🎉 恭喜！

如果你完成了所有步骤，恭喜你已经成功：
1. 安装了 Nginx
2. 创建了第一个网站
3. 学会了基本的 Nginx 配置

## 🚀 下一步

完成这个练习后，你可以尝试：
1. 添加更多页面（about.html, contact.html）
2. 添加图片和其他静态文件
3. 学习 CSS 美化网站
4. 尝试配置多个网站

继续加油！💪
