# 限制WordPress XMLRPC访问
location = /xmlrpc.php {
    # 如需允许特定IP访问，请按如下格式添加
    # allow ***************;
    deny all;
    access_log off;
    log_not_found off;
}

# 限制WordPress登录页面防止暴力攻击
location = /wp-login.php {
    # 引入访问控制规则 - 白名单IP配置
    include /etc/nginx/rules/wp-admin-access.conf;
    
    # 限制请求速率以防止暴力攻击
    limit_req zone=wordpress burst=3 nodelay;
    fastcgi_pass unix:/var/run/php/php7.4-fpm.sock;
    include fastcgi_params;
    fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
}

# 对wp-admin目录入口额外的保护
location = /wp-admin {
    # 引入访问控制规则 - 白名单IP配置
    include /etc/nginx/rules/wp-admin-access.conf;
    return 301 $scheme://$host$uri/;
}

# 处理WordPress AJAX请求 - 前台AJAX不需要限制IP
location = /wp-admin/admin-ajax.php {
    # AJAX请求无需IP白名单限制，因为前台功能也会使用它
    fastcgi_pass unix:/var/run/php/php7.4-fpm.sock;
    include fastcgi_params;
    fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
}

# 禁止访问WordPress配置文件
location ~* wp-config.php {
    deny all;
}

# 禁止访问上传目录中的PHP文件，但允许访问wp-includes中的必要文件
location ~* /(?:uploads|files|wp-content)/.*\.php$ {
    deny all;
    access_log off;
    log_not_found off;
}

# 保护wp-includes目录，但允许必要的JS/CSS文件
location ~* /wp-includes/.*\.php$ {
    # 允许load-styles.php和load-scripts.php（后台所需）
    location ~* /wp-includes/js/tinymce/wp-tinymce\.php$ { 
        try_files $uri =404;
        fastcgi_split_path_info ^(.+\.php)(/.+)$;
        fastcgi_pass unix:/var/run/php/php7.4-fpm.sock;
        fastcgi_index index.php;
        include fastcgi_params;
        fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
        fastcgi_param PATH_INFO $fastcgi_path_info;
    }
    
    # 禁止其他PHP文件
    deny all;
    access_log off;
    log_not_found off;
}

# 限制请求方法
if ($request_method !~ ^(GET|POST|HEAD)$) {
    return 444;
} 