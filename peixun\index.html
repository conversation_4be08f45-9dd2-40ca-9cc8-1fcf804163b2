<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Nginx 入门培训 - 从零开始学习</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .slide {
            background: white;
            margin: 20px 0;
            padding: 40px;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            min-height: 80vh;
            display: none;
        }
        
        .slide.active {
            display: block;
        }
        
        h1 {
            color: #2c3e50;
            text-align: center;
            margin-bottom: 30px;
            font-size: 2.5em;
        }
        
        h2 {
            color: #3498db;
            margin-bottom: 20px;
            font-size: 2em;
            border-bottom: 3px solid #3498db;
            padding-bottom: 10px;
        }
        
        h3 {
            color: #e74c3c;
            margin: 20px 0 15px 0;
            font-size: 1.5em;
        }
        
        .analogy {
            background: #f8f9fa;
            border-left: 5px solid #28a745;
            padding: 20px;
            margin: 20px 0;
            border-radius: 5px;
        }
        
        .analogy::before {
            content: "💡 生活比喻：";
            font-weight: bold;
            color: #28a745;
            display: block;
            margin-bottom: 10px;
        }
        
        .code-example {
            background: #2d3748;
            color: #e2e8f0;
            padding: 20px;
            border-radius: 8px;
            margin: 15px 0;
            overflow-x: auto;
        }
        
        .navigation {
            position: fixed;
            bottom: 20px;
            right: 20px;
            z-index: 1000;
        }
        
        .nav-btn {
            background: #3498db;
            color: white;
            border: none;
            padding: 10px 20px;
            margin: 5px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
        }
        
        .nav-btn:hover {
            background: #2980b9;
        }
        
        .slide-counter {
            position: fixed;
            top: 20px;
            right: 20px;
            background: rgba(0,0,0,0.7);
            color: white;
            padding: 10px 15px;
            border-radius: 20px;
            z-index: 1000;
        }
        
        ul, ol {
            margin-left: 30px;
            margin-bottom: 20px;
        }
        
        li {
            margin-bottom: 10px;
            font-size: 1.1em;
        }
        
        .highlight {
            background: #fff3cd;
            padding: 15px;
            border-radius: 5px;
            border-left: 4px solid #ffc107;
            margin: 15px 0;
        }
        
        .diagram {
            text-align: center;
            margin: 30px 0;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 10px;
        }
        
        .step {
            background: #e3f2fd;
            margin: 10px 0;
            padding: 15px;
            border-radius: 8px;
            border-left: 4px solid #2196f3;
        }
    </style>
</head>
<body>
    <div class="slide-counter">
        <span id="current-slide">1</span> / <span id="total-slides">8</span>
    </div>
    
    <div class="container">
        <!-- 第1页：标题页 -->
        <div class="slide active">
            <h1>🚀 Nginx 入门培训</h1>
            <div style="text-align: center; margin: 50px 0;">
                <h2 style="border: none; color: #666;">从零开始，轻松掌握网站服务器</h2>
                <p style="font-size: 1.3em; margin: 30px 0; color: #777;">
                    适合完全没有技术基础的朋友
                </p>
                <div style="margin: 50px 0;">
                    <p style="font-size: 1.1em;">🎯 培训目标：让您用最简单的方式理解 Nginx</p>
                    <p style="font-size: 1.1em;">⏰ 培训时长：约 60 分钟</p>
                    <p style="font-size: 1.1em;">👥 适合人群：零基础学员</p>
                </div>
            </div>
        </div>

        <!-- 第2页：什么是Nginx -->
        <div class="slide">
            <h2>🤔 什么是 Nginx？</h2>
            
            <div class="analogy">
                想象一下，Nginx 就像是一个超级聪明的餐厅服务员。当很多客人（用户）同时来到餐厅（网站）时，这个服务员能够：
                <ul>
                    <li>快速接待每一位客人</li>
                    <li>把客人的需求传达给厨房（服务器）</li>
                    <li>把做好的菜（网页内容）端给客人</li>
                    <li>同时处理很多桌客人，效率超高</li>
                </ul>
            </div>
            
            <h3>📝 专业定义</h3>
            <p style="font-size: 1.2em;">Nginx 是一个高性能的 Web 服务器和反向代理服务器。</p>
            
            <h3>🌟 为什么 Nginx 这么受欢迎？</h3>
            <ul>
                <li><strong>速度快</strong>：处理请求的速度非常快</li>
                <li><strong>稳定</strong>：很少出现故障，可以长时间运行</li>
                <li><strong>省资源</strong>：不会占用太多服务器资源</li>
                <li><strong>免费</strong>：开源软件，完全免费使用</li>
            </ul>
        </div>

        <!-- 第3页：Nginx的作用 -->
        <div class="slide">
            <h2>🎯 Nginx 能做什么？</h2>
            
            <h3>1. 🌐 Web 服务器</h3>
            <div class="analogy">
                就像图书馆管理员，当有人要借书（访问网页）时，管理员会找到对应的书（HTML文件）并交给读者（浏览器）。
            </div>
            
            <h3>2. 🔄 反向代理</h3>
            <div class="analogy">
                想象一个大型商场的总服务台。顾客有任何需求都先到服务台，然后服务台的工作人员会帮你联系对应的商店。顾客不需要知道具体是哪个商店在处理，只需要等结果就行。
            </div>
            
            <h3>3. ⚖️ 负载均衡</h3>
            <div class="analogy">
                就像银行的排队系统，当有多个柜台时，系统会自动把客户分配到最空闲的柜台，确保每个柜台的工作量都比较平均。
            </div>
            
            <h3>4. 🗂️ 静态文件服务</h3>
            <div class="analogy">
                像一个自动售货机，当有人需要饮料（图片、CSS、JS文件）时，直接从机器里取出来给客户，不需要人工处理。
            </div>
        </div>

        <!-- 第4页：Nginx的工作原理 -->
        <div class="slide">
            <h2>⚙️ Nginx 是怎么工作的？</h2>
            
            <div class="diagram">
                <h3>🔄 请求处理流程</h3>
                <div style="font-size: 1.2em; line-height: 2;">
                    用户浏览器 → Nginx → 后端服务器 → Nginx → 用户浏览器
                </div>
            </div>
            
            <div class="step">
                <strong>步骤 1：</strong> 用户在浏览器输入网址，发送请求
            </div>
            
            <div class="step">
                <strong>步骤 2：</strong> Nginx 接收到请求，分析用户想要什么
            </div>
            
            <div class="step">
                <strong>步骤 3：</strong> Nginx 决定如何处理：
                <ul style="margin-top: 10px;">
                    <li>如果是静态文件（图片、CSS），直接返回</li>
                    <li>如果需要动态处理，转发给后端服务器</li>
                </ul>
            </div>
            
            <div class="step">
                <strong>步骤 4：</strong> 把结果返回给用户浏览器
            </div>
            
            <div class="highlight">
                <strong>💡 关键优势：</strong> Nginx 可以同时处理成千上万个请求，就像一个超级多任务的服务员！
            </div>
        </div>

        <!-- 第5页：Nginx配置基础 -->
        <div class="slide">
            <h2>📝 Nginx 配置文件</h2>

            <div class="analogy">
                配置文件就像餐厅的操作手册，告诉服务员（Nginx）应该怎么工作：几点开门、怎么接待客人、菜单在哪里等等。
            </div>

            <h3>🗂️ 主要配置文件位置</h3>
            <ul>
                <li><strong>主配置文件：</strong> /etc/nginx/nginx.conf</li>
                <li><strong>网站配置：</strong> /etc/nginx/sites-available/</li>
                <li><strong>日志文件：</strong> /var/log/nginx/</li>
            </ul>

            <h3>📋 基本配置结构</h3>
            <div class="code-example">
# 这是一个简单的配置示例
server {
    listen 80;                    # 监听80端口（网站的门牌号）
    server_name example.com;      # 网站域名

    location / {                  # 当访问根目录时
        root /var/www/html;       # 网站文件存放位置
        index index.html;         # 默认首页文件
    }
}
            </div>

            <div class="highlight">
                <strong>🔧 配置文件就像说明书：</strong>
                <ul>
                    <li>listen：告诉 Nginx 在哪个端口"听门"</li>
                    <li>server_name：网站的"名字"</li>
                    <li>location：不同网址的处理规则</li>
                    <li>root：网站文件的"仓库位置"</li>
                </ul>
            </div>
        </div>

        <!-- 第6页：常见应用场景 -->
        <div class="slide">
            <h2>🏢 Nginx 的常见应用场景</h2>

            <h3>1. 🌐 搭建网站</h3>
            <div class="analogy">
                就像开一家网上商店，Nginx 帮你把商店"开门营业"，让顾客能够访问你的网站。
            </div>

            <h3>2. 🔒 HTTPS 加密</h3>
            <div class="analogy">
                给你的网站加上"防盗门"，确保用户和网站之间的信息传输是安全的，就像银行的加密通道。
            </div>

            <h3>3. 🚀 网站加速</h3>
            <div class="analogy">
                像在网站前面放一个"快递柜"，把常用的文件提前准备好，用户需要时直接取，不用每次都去仓库找。
            </div>

            <h3>4. 🔄 API 网关</h3>
            <div class="analogy">
                像公司的前台接待，所有外来访客都先到前台登记，然后前台决定把访客带到哪个部门。
            </div>

            <div class="highlight">
                <strong>💼 实际应用举例：</strong>
                <ul>
                    <li>淘宝、京东等电商网站</li>
                    <li>新闻网站、博客</li>
                    <li>企业官网</li>
                    <li>手机 APP 的后台服务</li>
                </ul>
            </div>
        </div>

        <!-- 第7页：实践演示 -->
        <div class="slide">
            <h2>🛠️ 动手实践：搭建第一个网站</h2>

            <h3>📋 准备工作</h3>
            <div class="step">
                <strong>1. 安装 Nginx</strong>
                <div class="code-example">
# Ubuntu/Debian 系统
sudo apt update
sudo apt install nginx

# CentOS/RHEL 系统
sudo yum install nginx
                </div>
            </div>

            <div class="step">
                <strong>2. 启动 Nginx</strong>
                <div class="code-example">
sudo systemctl start nginx
sudo systemctl enable nginx  # 开机自启动
                </div>
            </div>

            <div class="step">
                <strong>3. 检查是否成功</strong>
                <p>在浏览器输入服务器IP地址，看到 Nginx 欢迎页面就成功了！</p>
            </div>

            <h3>🎨 自定义网站内容</h3>
            <div class="step">
                <strong>4. 创建自己的网页</strong>
                <div class="code-example">
# 编辑默认网页
sudo nano /var/www/html/index.html

# 写入内容：
&lt;h1&gt;欢迎来到我的网站！&lt;/h1&gt;
&lt;p&gt;这是我用 Nginx 搭建的第一个网站。&lt;/p&gt;
                </div>
            </div>

            <div class="highlight">
                <strong>🎉 恭喜！</strong> 您已经成功搭建了自己的第一个网站！
            </div>
        </div>

        <!-- 第8页：总结和下一步 -->
        <div class="slide">
            <h2>🎓 培训总结</h2>

            <h3>📚 今天我们学到了什么？</h3>
            <ul>
                <li>✅ Nginx 是什么：高性能的网站服务器</li>
                <li>✅ Nginx 的作用：处理网站请求、负载均衡、反向代理</li>
                <li>✅ 工作原理：像聪明的服务员处理客户请求</li>
                <li>✅ 基本配置：配置文件就像操作手册</li>
                <li>✅ 实际应用：从个人博客到大型网站都在用</li>
                <li>✅ 动手实践：搭建了第一个网站</li>
            </ul>

            <h3>🚀 下一步学习建议</h3>
            <div class="step">
                <strong>初级进阶：</strong>
                <ul>
                    <li>学习更多配置选项</li>
                    <li>了解日志分析</li>
                    <li>配置 HTTPS</li>
                </ul>
            </div>

            <div class="step">
                <strong>中级进阶：</strong>
                <ul>
                    <li>负载均衡配置</li>
                    <li>性能优化</li>
                    <li>安全配置</li>
                </ul>
            </div>

            <div class="highlight">
                <strong>💡 记住：</strong> 学习技术就像学开车，理论重要，但更重要的是多练习！
            </div>

            <div style="text-align: center; margin-top: 40px;">
                <h2 style="color: #27ae60; border: none;">🎉 感谢参加培训！</h2>
                <p style="font-size: 1.2em; margin-top: 20px;">有问题随时交流，继续加油！</p>
            </div>
        </div>
    </div>

    <div class="navigation">
        <button class="nav-btn" onclick="previousSlide()">⬅️ 上一页</button>
        <button class="nav-btn" onclick="nextSlide()">下一页 ➡️</button>
    </div>

    <script>
        let currentSlide = 0;
        const slides = document.querySelectorAll('.slide');
        const totalSlides = slides.length;
        
        document.getElementById('total-slides').textContent = totalSlides;
        
        function showSlide(n) {
            slides[currentSlide].classList.remove('active');
            currentSlide = (n + totalSlides) % totalSlides;
            slides[currentSlide].classList.add('active');
            document.getElementById('current-slide').textContent = currentSlide + 1;
        }
        
        function nextSlide() {
            showSlide(currentSlide + 1);
        }
        
        function previousSlide() {
            showSlide(currentSlide - 1);
        }
        
        // 键盘导航
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowRight' || e.key === ' ') {
                nextSlide();
            } else if (e.key === 'ArrowLeft') {
                previousSlide();
            }
        });
    </script>
</body>
</html>
