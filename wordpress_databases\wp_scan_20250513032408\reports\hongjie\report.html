<p>未发现可疑文章内容</p>
    </div>
    
    <div id="suspicious-users" class="tab-content">
        <h3>可疑用户</h3>
<table><thead><tr>
<th>user_login</th>
</tr></thead><tbody>
<tr>
<td>hx_admin</td></tr>
</tbody></table>
    </div>
    
    <div id="suspicious-options" class="tab-content">
        <h3>可疑选项值</h3>
<table><thead><tr>
<th>option_id</th><th>option_name</th><th>autoload</th>
</tr></thead><tbody>
<tr>
<td>132</td><td>hxopt</td><td>yes</td></tr>
<tr>
<td>7374</td><td>ihaf_insert_header</td><td>yes</td></tr>
<tr>
<td>318274</td><td>zendkee_customize</td><td>yes</td></tr>
</tbody></table>
    </div>
    
    <div id="suspicious-comments" class="tab-content">
        <h3>可疑评论</h3>
<p>未发现可疑评论</p>
    </div>
    
    <div id="suspicious-postmeta" class="tab-content">
        <h3>可疑元数据</h3>
<table><thead><tr>
<th>meta_id</th><th>post_id</th><th>meta_key</th><th>meta_value</th>
</tr></thead><tbody>
<tr>
<td>1611</td><td>285</td><td>_oembed_2709b34329f13bf825a30e951cce70e3</td><td><iframe title="Timelapse - Lighthouse (Oct 2012)" src="https://player.vimeo.com/video/51589652?dnt=1&amp;app_id=122963" width="980" height="551"  allow="autoplay; fullscreen" allowfullscreen></iframe></td></tr>
<tr>
<td>1613</td><td>285</td><td>_oembed_5e88384c560aea9702687a364d334fe1</td><td><iframe title="create a product" width="980" height="551" src="https://www.youtube.com/embed/GrE5vzRGvP0?feature=oembed"  allow="accelerometer; autoplay; encrypted-media; gyroscope; picture-in-picture" allowfullscreen></iframe></td></tr>
</tbody></table>
    </div>
    
    <div id="suspicious-dates" class="tab-content">
        <h3>可疑日期</h3>
<p>未发现可疑日期</p>
    </div>
    
    <footer>
        <p>WordPress数据库扫描工具 - 扫描路径: /var/www/hongjie</p>
    </footer>
    
    <script>
        function openTab(evt, tabName) {
            var i, tabcontent, tabbuttons;
            
            // 隐藏所有标签内容
            tabcontent = document.getElementsByClassName("tab-content");
            for (i = 0; i < tabcontent.length; i++) {
                tabcontent[i].className = tabcontent[i].className.replace(" active", "");
            }
            
            // 移除所有标签按钮的active类
            tabbuttons = document.getElementsByClassName("tab-button");
            for (i = 0; i < tabbuttons.length; i++) {
                tabbuttons[i].className = tabbuttons[i].className.replace(" active", "");
            }
            
            // 显示当前标签，并添加active类到按钮
            document.getElementById(tabName).className += " active";
            evt.currentTarget.className += " active";
        }
    </script>
</body>
</html>
