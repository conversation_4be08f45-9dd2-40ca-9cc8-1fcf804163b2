# WordPress管理区域的特殊处理
location ^~ /wp-admin {
    # 引入访问控制规则 - 白名单IP配置
    include /etc/nginx/rules/wp-admin-access.conf;
    
    # 确保只能通过正常PHP处理访问这个区域
    location ~ \.php$ {
        try_files $uri =404;
        fastcgi_split_path_info ^(.+\.php)(/.+)$;
        fastcgi_pass unix:/var/run/php/php7.4-fpm.sock;
        fastcgi_index index.php;
        include fastcgi_params;
        fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
        fastcgi_param PATH_INFO $fastcgi_path_info;
        
        # 安全头
        fastcgi_hide_header X-Powered-By;
        add_header X-Frame-Options "SAMEORIGIN" always;
        add_header X-Content-Type-Options "nosniff" always;
        add_header X-XSS-Protection "1; mode=block" always;
    }
    
    # 非PHP文件的处理
    try_files $uri $uri/ /index.php?$args;
} 