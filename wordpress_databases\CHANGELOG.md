# 更新日志

所有项目的显著变更都将记录在此文件中。

格式基于[Keep a Changelog](https://keepachangelog.com/zh-CN/1.0.0/)。

## [未发布]

### 新增
- 创建项目基础架构
- 初始化README.md文件，描述项目功能和实现步骤
- 初始化CHANGELOG.md文件，记录项目变更
- 添加HTML报告示例
- 添加WordPress站点URL获取方法到README.md
- 添加垃圾信息检测功能（色情、赌博等关键词检测）
- 添加大型报告处理方法（分段生成、分页显示、压缩输出等）
- 创建`wp_scanner_merged.sh`脚本，合并所有功能到单个文件中，简化部署和使用

### 修改
- 更新HTML报告示例，添加站点URL信息表格和相关样式
- 修改HTML报告，移除三个状态卡片（过期插件数、安全风险、已更新网站），添加扫描站点总数卡片
- 更新实现步骤，添加垃圾信息检测功能步骤
- 增强HTML报告功能，添加垃圾信息检测结果部分，实现表格分页、搜索和折叠功能
- 根据更新的SQL查询，完善垃圾信息检测结果部分，增加对wp_options表、wp_users表和未来日期文章的检测
- 根据最新SQL查询调整报告内容，移除综合查询结果部分，更新关键词匹配列表
- 将原有的三个脚本（`wp_scanner.sh`、`report_generator.sh`和`report_template.sh`）合并为一个独立脚本，减少依赖关系
- 优化总报告(index.html)中站点信息表格的分页显示，将每页显示行数从5行增加到30行，提高阅读体验

### 修复
- 修复总报告(index.html)中"站点信息"部分在第五页第二项后重复显示的问题，通过优化HTML结构确保站点信息部分只显示一次
- 修复站点重复扫描问题，限制只在 /var/www/ 和 /www/wwwroot/ 目录下搜索WordPress站点，并添加防重复机制，避免同一站点被多次添加
- 修复报告中文字符显示乱码问题，添加正确的HTTP Content-Type头及字符集声明，增加服务器配置文件(.htaccess和nginx-charset.conf)，并为HTML文件添加合适的中文字体支持

### 移除
- 移除过期插件、安全风险和已更新网站的辨别标准

## 1.0.5 - 2025-05-12
### 修复
- 修复搜索结果显示时的JavaScript错误（`line 1368: 找到包含的结果: command not found`）
- 将模板字符串改为传统字符串连接方式，避免shell脚本解析时的变量冲突

## 1.0.4 - 2025-05-12
### 修复
- 修复总数据库大小在报告中仍显示为0MB的问题
- 优化数值计算逻辑，增加数据库大小相关的调试信息
- 添加awk作为数值计算的备用方案，增强兼容性

## 1.0.3 - 2025-05-12
### 修复
- 修复扫描报告中总数据库大小显示不正确的问题
- 修复可疑内容统计不正确的问题，现在显示所有站点对应选项的总和
- 增加可疑元数据和可疑日期在可疑内容统计部分的显示

## 1.0.2 - 2025-05-12
### 修复
- 修复了数据库大小显示不正确的问题
- 改进了可疑内容统计的显示，现在显示实际数量而不是占位符
- 增强了错误处理和日志记录

## 1.0.1 - 2025-05-11
### 修复
- 修复了报告生成过程中的路径处理问题
- 改进了数据库连接错误处理
- 修复了站点URL获取逻辑

## 1.0.0 - 2025-05-10
### 新增
- 创建主脚本 `wp_scanner.sh` 用于WordPress数据库扫描
- 实现自动检测WordPress站点功能
- 添加从wp-config.php提取数据库凭据的功能
- 实现多种可疑内容扫描功能：
  - 可疑PHP代码（eval、base64等）
  - 可疑用户（创建时间异常）
  - 可疑选项（自动加载选项）
  - 可疑评论（含链接或脚本）
  - 可疑文章元数据
  - 日期异常的文章
- 创建 `report_generator.sh` 脚本用于生成HTML报告
- 创建 `report_template.sh` 脚本用于HTML模板管理
- 添加交互式报告功能，支持分页和搜索
- 实现自动输出功能，可通过Web服务器或打包方式提供报告

### 修复
- 修复站点路径处理问题
- 修复MySQL查询中的转义问题
- 修复日期比较逻辑

### 结构变更
- 整合所有功能到单一脚本 `wp_scanner_merged.sh`
- 添加临时文件清理功能
- 增强日志记录系统

### 结构性变更
- ✅ 将代码组织为函数以提高可维护性
- ✅ 添加配置选项
- ✅ 添加颜色输出支持
- ✅ 创建模板系统用于报告生成 