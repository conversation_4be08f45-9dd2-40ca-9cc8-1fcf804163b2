<?php if (!defined ('ABSPATH')) die (); ?><div class="wrap">
	<?php screen_icon(); ?>

	<h2><?php _e ('Audit', 'audit-trail'); ?></h2>

	<?php $this->submenu (true); ?>

	<div style="clear: both; margin-top: 15px;"></div>
	
	<!-- 搜索表单 -->
	<div class="audit-search-form">
		<h3><?php _e('搜索日志', 'audit-trail'); ?></h3>
		<form method="get" class="audit-search-form">
			<input type="hidden" name="page" value="audit-trail.php" />
			
			<div class="audit-search-box">
				<div class="search-row">
					<label for="search_user"><?php _e('用户名：', 'audit-trail'); ?></label>
					<input type="text" id="search_user" name="search_user" value="<?php echo isset($_GET['search_user']) ? esc_attr($_GET['search_user']) : ''; ?>" />
					
					<label for="search_operation"><?php _e('操作类型：', 'audit-trail'); ?></label>
					<select id="search_operation" name="search_operation">
						<option value=""><?php _e('- 全部 -', 'audit-trail'); ?></option>
						<?php 
						// 获取所有操作类型
						$operations = array(
							'switch_theme'         => __('切换主题', 'audit-trail'),
							'wp_login'             => __('用户登录', 'audit-trail'),
							'wp_logout'            => __('用户退出', 'audit-trail'),
							'wp_login_failed'      => __('登录失败', 'audit-trail'),
							'retrieve_password'    => __('找回密码', 'audit-trail'),
							'delete_user'          => __('删除用户', 'audit-trail'),
							'delete_link'          => __('删除链接', 'audit-trail'),
							'delete_comment'       => __('删除评论', 'audit-trail'),
							'delete_post'          => __('删除文章', 'audit-trail'),
							'private_to_published' => __('私密改为发布', 'audit-trail'),
							'delete_category'      => __('删除分类', 'audit-trail'),
							'delete_attachment'    => __('删除附件', 'audit-trail'),
							'template_redirect'    => __('访问页面', 'audit-trail'),
							'activate_plugin'      => __('激活插件', 'audit-trail'),
							'deactivate_plugin'    => __('停用插件', 'audit-trail'),
							'draft_to_publish'     => __('草稿改为发布', 'audit-trail'),
							'pending_to_publish'   => __('待审改为发布', 'audit-trail'),
							'publish_to_draft'     => __('发布改为草稿', 'audit-trail'),
							'publish_to_private'   => __('发布改为私密', 'audit-trail'),
							'publish_to_pending'   => __('发布改为待审', 'audit-trail'),
							'publish_to_trash'     => __('发布改为回收站', 'audit-trail'),
							'draft_to_pending'     => __('草稿改为待审', 'audit-trail'),
							'draft_to_trash'       => __('草稿改为回收站', 'audit-trail'),
							'pending_to_draft'     => __('待审改为草稿', 'audit-trail'),
							'pending_to_trash'     => __('待审改为回收站', 'audit-trail'),
							'trash_to_publish'     => __('回收站改为发布', 'audit-trail'),
							'trash_to_draft'       => __('回收站改为草稿', 'audit-trail'),
							'trash_to_pending'     => __('回收站改为待审', 'audit-trail'),
							'future_to_publish'    => __('定时发布', 'audit-trail'),
							'profile_update'       => __('更新个人资料', 'audit-trail'),
							'user_register'        => __('注册新用户', 'audit-trail'),
							'add_link'             => __('添加链接', 'audit-trail'),
							'edit_link'            => __('编辑链接', 'audit-trail'),
							'edit_category'        => __('编辑分类', 'audit-trail'),
							'add_category'         => __('添加分类', 'audit-trail'),
							'edit_comment'         => __('编辑评论', 'audit-trail'),
							'save_post'            => __('保存文章/页面', 'audit-trail'),
							'add_attachment'       => __('添加附件', 'audit-trail'),
							'edit_attachment'      => __('编辑附件', 'audit-trail'),
							'bulk_delete_by_operation' => __('批量删除操作', 'audit-trail'),
							'manual_cleanup'       => __('手动清理', 'audit-trail'),
							// Elementor操作
							'elementor/editor/after_save'     => __('Elementor编辑器保存', 'audit-trail'),
							'elementor/document/after_save'   => __('Elementor文档保存', 'audit-trail'),
							'elementor/editor/before_save'    => __('Elementor编辑器保存前', 'audit-trail'),
							// Beaver Builder操作
							'fl_builder_after_save_layout'    => __('Beaver Builder保存', 'audit-trail'),
							'fl_builder_before_save_layout'   => __('Beaver Builder保存前', 'audit-trail'),
							'fl_builder_after_layout_rendered' => __('Beaver Builder渲染后', 'audit-trail'),
							// SiteOrigin操作
							'siteorigin_panels_save_post'     => __('SiteOrigin保存', 'audit-trail'),
							'siteorigin_panels_after_render'  => __('SiteOrigin渲染后', 'audit-trail'),
							// WPBakery操作
							'vc_after_save_post'              => __('WPBakery保存', 'audit-trail'),
							'vc_before_save_post'             => __('WPBakery保存前', 'audit-trail'),
							'vc_after_update'                 => __('WPBakery更新后', 'audit-trail'),
							// Divi操作
							'et_fb_save_layout'               => __('Divi Builder保存', 'audit-trail'),
							'et_fb_ajax_save'                 => __('Divi Builder AJAX保存', 'audit-trail'),
							'et_builder_after_save_layout'    => __('Divi Builder布局保存后', 'audit-trail'),
							// Gutenberg操作
							'blocks_parsed'                   => __('Gutenberg编辑', 'audit-trail'),
							'gutenberg_render_block'          => __('Gutenberg块渲染', 'audit-trail'),
						);
						
						// 根据当前选中的操作类型设置选中状态
						foreach ($operations as $value => $label) {
							$selected = (isset($_GET['search_operation']) && $_GET['search_operation'] == $value) ? ' selected="selected"' : '';
							echo '<option value="'.esc_attr($value).'"'.$selected.'>'.esc_html($label).'</option>';
						}
						?>
					</select>
					
					<label for="search_ip"><?php _e('IP地址：', 'audit-trail'); ?></label>
					<input type="text" id="search_ip" name="search_ip" value="<?php echo isset($_GET['search_ip']) ? esc_attr($_GET['search_ip']) : ''; ?>" />
				</div>
				
				<div class="search-row">
					<label for="search_date_from"><?php _e('开始日期：', 'audit-trail'); ?></label>
					<input type="date" id="search_date_from" name="search_date_from" value="<?php echo isset($_GET['search_date_from']) ? esc_attr($_GET['search_date_from']) : ''; ?>" />
					
					<label for="search_date_to"><?php _e('结束日期：', 'audit-trail'); ?></label>
					<input type="date" id="search_date_to" name="search_date_to" value="<?php echo isset($_GET['search_date_to']) ? esc_attr($_GET['search_date_to']) : ''; ?>" />
					
					<input type="submit" class="button-secondary" value="<?php _e('搜索', 'audit-trail'); ?>" />
					<a href="?page=audit-trail.php" class="button-secondary"><?php _e('重置', 'audit-trail'); ?></a>
				</div>
				
				<div class="search-row">
					<label for="exclude_page_views">
						<input type="checkbox" id="exclude_page_views" name="exclude_page_views" value="1" <?php checked(isset($_GET['exclude_page_views']) && $_GET['exclude_page_views'] == '1'); ?> />
						<?php _e('排除"访问页面"日志', 'audit-trail'); ?>
					</label>
					<span class="description"><?php _e('(选中此项将不显示template_redirect操作类型的日志记录)', 'audit-trail'); ?></span>
				</div>
			</div>
			
			<div class="tablenav top">
				<div class="alignleft actions">
					<?php 
					// 创建包含当前搜索条件的CSV导出链接
					$search_params = array();
					foreach (array('search_user', 'search_operation', 'search_ip', 'search_date_from', 'search_date_to', 'exclude_page_views') as $param) {
						if (isset($_GET[$param]) && !empty($_GET[$param])) {
							$search_params[$param] = $_GET[$param];
						}
					}
					
					// 使用admin-ajax.php作为导出入口，以避免NGINX直接拦截
					$csv_url = wp_nonce_url(
						add_query_arg(
							array_merge(
								$search_params,
								array('action' => 'audit_export_csv')
							),
							admin_url('admin-ajax.php')
						),
						'audit_csv_export',
						'_wpnonce'
					);
					?>
					<a href="<?php echo esc_url($csv_url); ?>" class="button-secondary">
						<img src="<?php echo plugins_url( '/images/csv.png', $this->base_url() ); ?>" width="16" height="16" alt="Csv" style="vertical-align: middle; margin-right: 5px;"/>
						<?php _e('导出搜索结果', 'audit-trail'); ?>
					</a>
				</div>
				<br class="clear" />
			</div>
		</form>
	</div>

	<form method="post">
		<?php $table->display(); ?>
	</form>
</div>

<script type="text/javascript">
	( function($) {
		$( document ).ready( function() {
		var ajaxurl = '<?php echo admin_url( 'admin-ajax.php' ); ?>';

		function clickers() {
			$( 'a.audit-view' ).unbind( 'click' ).click( function() {
				var item = $( this ).parents( 'tr' );
				var original = item.html();
				var itemid = this.href.replace( /.*?#(.*)/, '$1' );
				var nonce  = '<?php echo wp_create_nonce( 'audittrail_view' )?>';

				item.load( ajaxurl, {
					action: 'at_view',
					id: itemid,
					_ajax_nonce: nonce
				}, function() {
					item.data( 'original', original );

					$( item ).find( 'a' ).click( function() {
						item.html( item.data( 'original' ) );
						item.data( 'original', false );

						clickers();
						return false;
					});
				});

				return false;
			});
		}

		clickers();
		} );
	})( jQuery );
</script>

<style>
.audit-search-form {
	background: #fff;
	border: 1px solid #ccd0d4;
	padding: 15px 20px;
	margin: 20px 0;
	border-radius: 4px;
	box-shadow: 0 1px 3px rgba(0,0,0,0.05);
}
.audit-search-form h3 {
	margin-top: 5px;
	margin-bottom: 15px;
	color: #23282d;
	font-size: 16px;
	border-bottom: 1px solid #eee;
	padding-bottom: 10px;
}
.audit-search-form .search-row {
	display: flex;
	flex-wrap: wrap;
	align-items: center;
	margin-bottom: 15px;
	gap: 10px;
}
.audit-search-form .search-row label {
	margin-right: 5px;
	font-weight: 500;
}
.audit-search-form .search-row input[type="text"],
.audit-search-form .search-row input[type="date"],
.audit-search-form .search-row select {
	min-width: 180px;
	height: 32px;
	margin-right: 15px;
}
.audit-search-form .button-secondary {
	height: 32px;
	line-height: 30px;
	padding: 0 12px;
	margin-left: 5px;
}
.audit-search-form .tablenav {
	margin: 5px 0;
	padding-top: 10px;
	border-top: 1px solid #f0f0f0;
}
.audit-search-form .tablenav .button-secondary {
	display: flex;
	align-items: center;
}
.audit-search-form .tablenav img {
	margin-right: 5px;
}
.wp-list-table {
	border: 1px solid #e5e5e5;
	box-shadow: 0 1px 1px rgba(0,0,0,.04);
}
.wp-list-table th {
	background: #f9f9f9;
}
.wp-list-table tr:hover {
	background: #f9f9f9;
}
.audit-view {
	text-decoration: none;
}
.detail-data {
	color: #777;
	font-size: 12px;
}
</style>
