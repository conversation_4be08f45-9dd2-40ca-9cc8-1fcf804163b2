[<PERSON><PERSON>er]
default_browser = chrome
chrome_path = /usr/bin/google-chrome
chrome_driver_path = /usr/local/bin/chromedriver
edge_path = /usr/bin/microsoft-edge
edge_driver_path = /usr/local/bin/msedgedriver
firefox_path = /usr/bin/firefox
firefox_driver_path = /usr/local/bin/geckodriver
brave_path = /usr/bin/brave-browser
brave_driver_path = /usr/local/bin/chromedriver
opera_path = /usr/bin/opera
opera_driver_path = /usr/local/bin/chromedriver
operagx_path = /usr/bin/opera-gx
operagx_driver_path = /usr/local/bin/chromedriver

[Turnstile]
handle_turnstile_time = 2
handle_turnstile_random_time = 1-3

[Timing]
min_random_time = 0.1
max_random_time = 0.8
page_load_wait = 0.1-0.8
input_wait = 0.3-0.8
submit_wait = 0.5-1.5
verification_code_input = 0.1-0.3
verification_success_wait = 2-3
verification_retry_wait = 2-3
email_check_initial_wait = 4-6
email_refresh_wait = 2-4
settings_page_load_wait = 1-2
failed_retry_time = 0.5-1
retry_interval = 8-12
max_timeout = 160

[Utils]
enabled_update_check = True
enabled_force_update = False
enabled_account_info = True

[OAuth]
show_selection_alert = False
timeout = 120
max_attempts = 3

[Token]
refresh_server = https://token.cursorpro.com.cn
enable_refresh = True

[LinuxPaths]
storage_path = /home/<USER>/.config/Cursor/User/globalStorage/storage.json
sqlite_path = /home/<USER>/.config/Cursor/User/globalStorage/state.vscdb
machine_id_path = /home/<USER>/.config/Cursor/machineid
cursor_path = /opt/Cursor/resources/app
updater_path = /home/<USER>/.config/cursor-updater
update_yml_path = /home/<USER>/.config/Cursor/resources/app-update.yml
product_json_path = /home/<USER>/.config/Cursor/resources/app/product.json

[Language]
current_language = 
fallback_language = en
auto_update_languages = True
language_cache_dir = /home/<USER>/Desktop/cursor/.cursor-free-vip/language_cache

