{"log": {"level": "warn", "timestamp": true}, "dns": {"servers": [{"tag": "remote", "address": "tcp://*******", "strategy": "prefer_ipv4", "detour": "proxy"}, {"tag": "local", "address": "*********", "strategy": "prefer_ipv4", "detour": "direct"}, {"tag": "block", "address": "rcode://success"}, {"tag": "local_local", "address": "*********", "detour": "direct"}], "rules": [{"server": "local_local", "domain": ["c21s3.portablesubmarines.com"]}, {"server": "remote", "clash_mode": "Global"}, {"server": "local_local", "clash_mode": "Direct"}, {"server": "local", "rule_set": ["geosite-cn", "geosite-geolocation-cn"]}], "final": "remote"}, "inbounds": [{"type": "tun", "tag": "tun-in", "interface_name": "singbox_tun", "address": ["**********/30"], "mtu": 9000, "auto_route": true, "strict_route": true, "stack": "gvisor", "sniff": true}], "outbounds": [{"type": "socks", "tag": "proxy", "server": "127.0.0.1", "server_port": 10808, "version": "5"}, {"type": "direct", "tag": "direct"}, {"type": "block", "tag": "block"}, {"type": "dns", "tag": "dns_out"}], "route": {"auto_detect_interface": true, "rules": [{"outbound": "proxy", "clash_mode": "Global"}, {"outbound": "direct", "clash_mode": "Direct"}, {"outbound": "dns_out", "protocol": ["dns"]}, {"outbound": "dns_out", "port": [53], "process_name": ["v2ray.exe", "v2ray.exe", "xray.exe", "mihomo-windows-amd64-compatible.exe", "mihomo-windows-amd64.exe", "mihomo-linux-amd64.exe", "clash.exe", "mihomo.exe", "hysteria.exe", "naive.exe", "naiveproxy.exe", "tuic-client.exe", "tuic.exe", "juicity-client.exe", "juicity.exe", "hysteria-windows-amd64.exe", "hysteria-linux-amd64.exe", "hysteria.exe", "brook_windows_amd64.exe", "brook_linux_amd64.exe", "brook.exe", "overtls-bin.exe", "overtls.exe"]}, {"outbound": "direct", "process_name": ["v2ray.exe", "v2ray.exe", "xray.exe", "mihomo-windows-amd64-compatible.exe", "mihomo-windows-amd64.exe", "mihomo-linux-amd64.exe", "clash.exe", "mihomo.exe", "hysteria.exe", "naive.exe", "naiveproxy.exe", "tuic-client.exe", "tuic.exe", "sing-box-client.exe", "sing-box.exe", "juicity-client.exe", "juicity.exe", "hysteria-windows-amd64.exe", "hysteria-linux-amd64.exe", "hysteria.exe", "brook_windows_amd64.exe", "brook_linux_amd64.exe", "brook.exe", "overtls-bin.exe", "overtls.exe"]}, {"outbound": "proxy", "domain": ["googleapis.cn", "gstatic.com"], "domain_suffix": [".googleapis.cn", ".gstatic.com"]}, {"outbound": "block", "network": ["udp"], "port": [443]}, {"outbound": "direct", "ip_is_private": true}, {"outbound": "direct", "rule_set": ["geosite-private"]}, {"outbound": "direct", "ip_cidr": ["*********", "*********", "2400:3200::1", "2400:3200:baba::1", "************", "**********", "************", "2402:4e00::", "2402:4e00:1::", "************", "2400:da00::6666", "***************", "***************", "***************", "***************", "***************", "***************", "***********", "***********", "***********", "************", "************", "*************", "*******", "*********", "***********", "************", "2400:7fc0:849e:200::4", "2404:c2c0:85d8:901::4", "************", "***********", "2400:7fc0:849e:200::8", "2404:c2c0:85d8:901::8", "************", "***********"]}, {"outbound": "direct", "domain": ["alidns.com", "doh.pub", "dot.pub", "360.cn", "onedns.net"], "domain_suffix": [".alidns.com", ".doh.pub", ".dot.pub", ".360.cn", ".onedns.net"]}, {"outbound": "direct", "rule_set": ["geoip-cn"]}, {"outbound": "direct", "rule_set": ["geosite-cn"]}], "rule_set": [{"tag": "geosite-private", "type": "local", "format": "binary", "path": "C:\\Users\\<USER>\\Desktop\\v2rayN-windows-64\\bin\\srss\\geosite-private.srs"}, {"tag": "geosite-cn", "type": "local", "format": "binary", "path": "C:\\Users\\<USER>\\Desktop\\v2rayN-windows-64\\bin\\srss\\geosite-cn.srs"}, {"tag": "geoip-cn", "type": "local", "format": "binary", "path": "C:\\Users\\<USER>\\Desktop\\v2rayN-windows-64\\bin\\srss\\geoip-cn.srs"}, {"tag": "geosite-geolocation-cn", "type": "local", "format": "binary", "path": "C:\\Users\\<USER>\\Desktop\\v2rayN-windows-64\\bin\\srss\\geosite-geolocation-cn.srs"}]}, "experimental": {"cache_file": {"enabled": true, "path": "C:\\Users\\<USER>\\Desktop\\v2rayN-windows-64\\bin\\cache.db"}, "clash_api": {"external_controller": "127.0.0.1:10814"}}}