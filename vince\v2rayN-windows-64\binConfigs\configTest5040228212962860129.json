{"log": {"loglevel": "warning"}, "inbounds": [{"tag": "socks10829", "port": 10829, "listen": "127.0.0.1", "protocol": "socks"}, {"tag": "socks10830", "port": 10830, "listen": "127.0.0.1", "protocol": "socks"}, {"tag": "socks10831", "port": 10831, "listen": "127.0.0.1", "protocol": "socks"}, {"tag": "socks10832", "port": 10832, "listen": "127.0.0.1", "protocol": "socks"}, {"tag": "socks10833", "port": 10833, "listen": "127.0.0.1", "protocol": "socks"}, {"tag": "socks10834", "port": 10834, "listen": "127.0.0.1", "protocol": "socks"}, {"tag": "socks10835", "port": 10835, "listen": "127.0.0.1", "protocol": "socks"}, {"tag": "socks10836", "port": 10836, "listen": "127.0.0.1", "protocol": "socks"}, {"tag": "socks10837", "port": 10837, "listen": "127.0.0.1", "protocol": "socks"}, {"tag": "socks10838", "port": 10838, "listen": "127.0.0.1", "protocol": "socks"}, {"tag": "socks10839", "port": 10839, "listen": "127.0.0.1", "protocol": "socks"}, {"tag": "socks10840", "port": 10840, "listen": "127.0.0.1", "protocol": "socks"}, {"tag": "socks10841", "port": 10841, "listen": "127.0.0.1", "protocol": "socks"}, {"tag": "socks10842", "port": 10842, "listen": "127.0.0.1", "protocol": "socks"}, {"tag": "socks10843", "port": 10843, "listen": "127.0.0.1", "protocol": "socks"}, {"tag": "socks10844", "port": 10844, "listen": "127.0.0.1", "protocol": "socks"}, {"tag": "socks10845", "port": 10845, "listen": "127.0.0.1", "protocol": "socks"}, {"tag": "socks10846", "port": 10846, "listen": "127.0.0.1", "protocol": "socks"}, {"tag": "socks10847", "port": 10847, "listen": "127.0.0.1", "protocol": "socks"}, {"tag": "socks10848", "port": 10848, "listen": "127.0.0.1", "protocol": "socks"}, {"tag": "socks10849", "port": 10849, "listen": "127.0.0.1", "protocol": "socks"}, {"tag": "socks10850", "port": 10850, "listen": "127.0.0.1", "protocol": "socks"}, {"tag": "socks10851", "port": 10851, "listen": "127.0.0.1", "protocol": "socks"}, {"tag": "socks10852", "port": 10852, "listen": "127.0.0.1", "protocol": "socks"}, {"tag": "socks10853", "port": 10853, "listen": "127.0.0.1", "protocol": "socks"}, {"tag": "socks10854", "port": 10854, "listen": "127.0.0.1", "protocol": "socks"}, {"tag": "socks10855", "port": 10855, "listen": "127.0.0.1", "protocol": "socks"}], "outbounds": [{"tag": "proxy10829", "protocol": "shadowsocks", "settings": {"servers": [{"address": "cm1.d-h-h.in", "method": "aes-128-gcm", "ota": false, "password": "05ee387e-5361-4a01-9260-33fd2828dd7b", "port": 51001, "level": 1}]}, "streamSettings": {"network": "tcp"}, "mux": {"enabled": false, "concurrency": -1}}, {"tag": "proxy10830", "protocol": "trojan", "settings": {"servers": [{"address": "cm1.d-h-h.in", "method": "chacha20", "ota": false, "password": "05ee387e-5361-4a01-9260-33fd2828dd7b", "port": 41009, "level": 1}]}, "streamSettings": {"network": "tcp", "security": "tls", "tlsSettings": {"allowInsecure": true, "serverName": "ssl.dhh.ac.cn"}}, "mux": {"enabled": false, "concurrency": -1}}, {"tag": "proxy10831", "protocol": "trojan", "settings": {"servers": [{"address": "cm1.d-h-h.in", "method": "chacha20", "ota": false, "password": "05ee387e-5361-4a01-9260-33fd2828dd7b", "port": 41019, "level": 1}]}, "streamSettings": {"network": "tcp", "security": "tls", "tlsSettings": {"allowInsecure": true, "serverName": "v1-de1.776688.best"}}, "mux": {"enabled": false, "concurrency": -1}}, {"tag": "proxy10832", "protocol": "trojan", "settings": {"servers": [{"address": "cm1.d-h-h.in", "method": "chacha20", "ota": false, "password": "05ee387e-5361-4a01-9260-33fd2828dd7b", "port": 41003, "level": 1}]}, "streamSettings": {"network": "tcp", "security": "tls", "tlsSettings": {"allowInsecure": true, "serverName": "v1-tw1.776688.best"}}, "mux": {"enabled": false, "concurrency": -1}}, {"tag": "proxy10833", "protocol": "trojan", "settings": {"servers": [{"address": "cm1.d-h-h.in", "method": "chacha20", "ota": false, "password": "05ee387e-5361-4a01-9260-33fd2828dd7b", "port": 41010, "level": 1}]}, "streamSettings": {"network": "tcp", "security": "tls", "tlsSettings": {"allowInsecure": true, "serverName": "ssl.dhh.ac.cn"}}, "mux": {"enabled": false, "concurrency": -1}}, {"tag": "proxy10834", "protocol": "trojan", "settings": {"servers": [{"address": "cm1.d-h-h.in", "method": "chacha20", "ota": false, "password": "05ee387e-5361-4a01-9260-33fd2828dd7b", "port": 41012, "level": 1}]}, "streamSettings": {"network": "tcp", "security": "tls", "tlsSettings": {"allowInsecure": true, "serverName": "v1-my1.776688.best"}}, "mux": {"enabled": false, "concurrency": -1}}, {"tag": "proxy10835", "protocol": "trojan", "settings": {"servers": [{"address": "eepl1.dhh114514.christmas", "method": "chacha20", "ota": false, "password": "05ee387e-5361-4a01-9260-33fd2828dd7b", "port": 42008, "level": 1}]}, "streamSettings": {"network": "tcp", "security": "tls", "tlsSettings": {"allowInsecure": true, "serverName": "v3-tr1.776688.best"}}, "mux": {"enabled": false, "concurrency": -1}}, {"tag": "proxy10836", "protocol": "shadowsocks", "settings": {"servers": [{"address": "eepl1.dhh114514.christmas", "method": "aes-256-gcm", "ota": false, "password": "05ee387e-5361-4a01-9260-33fd2828dd7b", "port": 42003, "level": 1}]}, "streamSettings": {"network": "tcp"}, "mux": {"enabled": false, "concurrency": -1}}, {"tag": "proxy10837", "protocol": "shadowsocks", "settings": {"servers": [{"address": "eepl1.dhh114514.christmas", "method": "chacha20-ietf-poly1305", "ota": false, "password": "05ee387e-5361-4a01-9260-33fd2828dd7b", "port": 42015, "level": 1}]}, "streamSettings": {"network": "tcp"}, "mux": {"enabled": false, "concurrency": -1}}, {"tag": "proxy10838", "protocol": "shadowsocks", "settings": {"servers": [{"address": "eepl2.d-h-h.de", "method": "chacha20-ietf-poly1305", "ota": false, "password": "05ee387e-5361-4a01-9260-33fd2828dd7b", "port": 42015, "level": 1}]}, "streamSettings": {"network": "tcp"}, "mux": {"enabled": false, "concurrency": -1}}, {"tag": "proxy10839", "protocol": "shadowsocks", "settings": {"servers": [{"address": "eepl1.dhh114514.christmas", "method": "chacha20-ietf-poly1305", "ota": false, "password": "05ee387e-5361-4a01-9260-33fd2828dd7b", "port": 41001, "level": 1}]}, "streamSettings": {"network": "tcp"}, "mux": {"enabled": false, "concurrency": -1}}, {"tag": "proxy10840", "protocol": "shadowsocks", "settings": {"servers": [{"address": "eepl2.d-h-h.de", "method": "chacha20-ietf-poly1305", "ota": false, "password": "05ee387e-5361-4a01-9260-33fd2828dd7b", "port": 41001, "level": 1}]}, "streamSettings": {"network": "tcp"}, "mux": {"enabled": false, "concurrency": -1}}, {"tag": "proxy10841", "protocol": "shadowsocks", "settings": {"servers": [{"address": "eepl1.dhh114514.christmas", "method": "chacha20-ietf-poly1305", "ota": false, "password": "05ee387e-5361-4a01-9260-33fd2828dd7b", "port": 42022, "level": 1}]}, "streamSettings": {"network": "tcp"}, "mux": {"enabled": false, "concurrency": -1}}, {"tag": "proxy10842", "protocol": "shadowsocks", "settings": {"servers": [{"address": "eepl1.dhh114514.christmas", "method": "chacha20-ietf-poly1305", "ota": false, "password": "05ee387e-5361-4a01-9260-33fd2828dd7b", "port": 42023, "level": 1}]}, "streamSettings": {"network": "tcp"}, "mux": {"enabled": false, "concurrency": -1}}, {"tag": "proxy10843", "protocol": "shadowsocks", "settings": {"servers": [{"address": "eepl2.d-h-h.de", "method": "aes-128-gcm", "ota": false, "password": "05ee387e-5361-4a01-9260-33fd2828dd7b", "port": 42009, "level": 1}]}, "streamSettings": {"network": "tcp"}, "mux": {"enabled": false, "concurrency": -1}}, {"tag": "proxy10844", "protocol": "shadowsocks", "settings": {"servers": [{"address": "eepl1.dhh114514.christmas", "method": "chacha20-ietf-poly1305", "ota": false, "password": "05ee387e-5361-4a01-9260-33fd2828dd7b", "port": 42028, "level": 1}]}, "streamSettings": {"network": "tcp"}, "mux": {"enabled": false, "concurrency": -1}}, {"tag": "proxy10845", "protocol": "shadowsocks", "settings": {"servers": [{"address": "eepl2.d-h-h.de", "method": "chacha20-ietf-poly1305", "ota": false, "password": "05ee387e-5361-4a01-9260-33fd2828dd7b", "port": 42028, "level": 1}]}, "streamSettings": {"network": "tcp"}, "mux": {"enabled": false, "concurrency": -1}}, {"tag": "proxy10846", "protocol": "shadowsocks", "settings": {"servers": [{"address": "eepl1.dhh114514.christmas", "method": "chacha20-ietf-poly1305", "ota": false, "password": "05ee387e-5361-4a01-9260-33fd2828dd7b", "port": 42031, "level": 1}]}, "streamSettings": {"network": "tcp"}, "mux": {"enabled": false, "concurrency": -1}}, {"tag": "proxy10847", "protocol": "shadowsocks", "settings": {"servers": [{"address": "eepl2.d-h-h.de", "method": "chacha20-ietf-poly1305", "ota": false, "password": "05ee387e-5361-4a01-9260-33fd2828dd7b", "port": 42031, "level": 1}]}, "streamSettings": {"network": "tcp"}, "mux": {"enabled": false, "concurrency": -1}}, {"tag": "proxy10848", "protocol": "shadowsocks", "settings": {"servers": [{"address": "eepl1.dhh114514.christmas", "method": "chacha20-ietf-poly1305", "ota": false, "password": "05ee387e-5361-4a01-9260-33fd2828dd7b", "port": 42010, "level": 1}]}, "streamSettings": {"network": "tcp"}, "mux": {"enabled": false, "concurrency": -1}}, {"tag": "proxy10849", "protocol": "shadowsocks", "settings": {"servers": [{"address": "eepl2.d-h-h.de", "method": "chacha20-ietf-poly1305", "ota": false, "password": "05ee387e-5361-4a01-9260-33fd2828dd7b", "port": 42010, "level": 1}]}, "streamSettings": {"network": "tcp"}, "mux": {"enabled": false, "concurrency": -1}}, {"tag": "proxy10850", "protocol": "shadowsocks", "settings": {"servers": [{"address": "eepl1.dhh114514.christmas", "method": "chacha20-ietf-poly1305", "ota": false, "password": "05ee387e-5361-4a01-9260-33fd2828dd7b", "port": 42029, "level": 1}]}, "streamSettings": {"network": "tcp"}, "mux": {"enabled": false, "concurrency": -1}}, {"tag": "proxy10851", "protocol": "shadowsocks", "settings": {"servers": [{"address": "eepl2.d-h-h.de", "method": "chacha20-ietf-poly1305", "ota": false, "password": "05ee387e-5361-4a01-9260-33fd2828dd7b", "port": 42029, "level": 1}]}, "streamSettings": {"network": "tcp"}, "mux": {"enabled": false, "concurrency": -1}}, {"tag": "proxy10852", "protocol": "shadowsocks", "settings": {"servers": [{"address": "eepl1.dhh114514.christmas", "method": "chacha20-ietf-poly1305", "ota": false, "password": "05ee387e-5361-4a01-9260-33fd2828dd7b", "port": 42099, "level": 1}]}, "streamSettings": {"network": "tcp"}, "mux": {"enabled": false, "concurrency": -1}}, {"tag": "proxy10853", "protocol": "shadowsocks", "settings": {"servers": [{"address": "eepl2.d-h-h.de", "method": "aes-256-gcm", "ota": false, "password": "05ee387e-5361-4a01-9260-33fd2828dd7b", "port": 42097, "level": 1}]}, "streamSettings": {"network": "tcp"}, "mux": {"enabled": false, "concurrency": -1}}, {"tag": "proxy10854", "protocol": "shadowsocks", "settings": {"servers": [{"address": "v6-us1.776688.best", "method": "aes-256-gcm", "ota": false, "password": "05ee387e-5361-4a01-9260-33fd2828dd7b", "port": 61002, "level": 1}]}, "streamSettings": {"network": "tcp"}, "mux": {"enabled": false, "concurrency": -1}}, {"tag": "proxy10855", "protocol": "shadowsocks", "settings": {"servers": [{"address": "v6-sg1.776688.best", "method": "chacha20-ietf-poly1305", "ota": false, "password": "05ee387e-5361-4a01-9260-33fd2828dd7b", "port": 61003, "level": 1}]}, "streamSettings": {"network": "tcp"}, "mux": {"enabled": false, "concurrency": -1}}], "routing": {"domainStrategy": "IPIfNonMatch", "rules": [{"type": "field", "inboundTag": ["socks10829"], "outboundTag": "proxy10829"}, {"type": "field", "inboundTag": ["socks10830"], "outboundTag": "proxy10830"}, {"type": "field", "inboundTag": ["socks10831"], "outboundTag": "proxy10831"}, {"type": "field", "inboundTag": ["socks10832"], "outboundTag": "proxy10832"}, {"type": "field", "inboundTag": ["socks10833"], "outboundTag": "proxy10833"}, {"type": "field", "inboundTag": ["socks10834"], "outboundTag": "proxy10834"}, {"type": "field", "inboundTag": ["socks10835"], "outboundTag": "proxy10835"}, {"type": "field", "inboundTag": ["socks10836"], "outboundTag": "proxy10836"}, {"type": "field", "inboundTag": ["socks10837"], "outboundTag": "proxy10837"}, {"type": "field", "inboundTag": ["socks10838"], "outboundTag": "proxy10838"}, {"type": "field", "inboundTag": ["socks10839"], "outboundTag": "proxy10839"}, {"type": "field", "inboundTag": ["socks10840"], "outboundTag": "proxy10840"}, {"type": "field", "inboundTag": ["socks10841"], "outboundTag": "proxy10841"}, {"type": "field", "inboundTag": ["socks10842"], "outboundTag": "proxy10842"}, {"type": "field", "inboundTag": ["socks10843"], "outboundTag": "proxy10843"}, {"type": "field", "inboundTag": ["socks10844"], "outboundTag": "proxy10844"}, {"type": "field", "inboundTag": ["socks10845"], "outboundTag": "proxy10845"}, {"type": "field", "inboundTag": ["socks10846"], "outboundTag": "proxy10846"}, {"type": "field", "inboundTag": ["socks10847"], "outboundTag": "proxy10847"}, {"type": "field", "inboundTag": ["socks10848"], "outboundTag": "proxy10848"}, {"type": "field", "inboundTag": ["socks10849"], "outboundTag": "proxy10849"}, {"type": "field", "inboundTag": ["socks10850"], "outboundTag": "proxy10850"}, {"type": "field", "inboundTag": ["socks10851"], "outboundTag": "proxy10851"}, {"type": "field", "inboundTag": ["socks10852"], "outboundTag": "proxy10852"}, {"type": "field", "inboundTag": ["socks10853"], "outboundTag": "proxy10853"}, {"type": "field", "inboundTag": ["socks10854"], "outboundTag": "proxy10854"}, {"type": "field", "inboundTag": ["socks10855"], "outboundTag": "proxy10855"}]}}