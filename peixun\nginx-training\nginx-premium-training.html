<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Nginx高级培训 - 专业版</title>
    <!-- Font Awesome 图标库 -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=JetBrains+Mono:wght@400;500&display=swap" rel="stylesheet">
    
    <style>
        :root {
            --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            --secondary-gradient: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            --success-gradient: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            --warning-gradient: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
            --dark-gradient: linear-gradient(135deg, #434343 0%, #000000 100%);
            
            --text-primary: #2d3748;
            --text-secondary: #4a5568;
            --text-light: #718096;
            --bg-light: #f7fafc;
            --white: #ffffff;
            --shadow-soft: 0 10px 25px rgba(0,0,0,0.1);
            --shadow-medium: 0 20px 40px rgba(0,0,0,0.15);
            --border-radius: 16px;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            background: var(--primary-gradient);
            min-height: 100vh;
            overflow: hidden;
        }

        .presentation-container {
            width: 100vw;
            height: 100vh;
            position: relative;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .slide {
            width: 90vw;
            max-width: 1200px;
            height: 85vh;
            background: var(--white);
            border-radius: var(--border-radius);
            box-shadow: var(--shadow-medium);
            padding: 60px;
            display: none;
            flex-direction: column;
            position: relative;
            overflow: hidden;
        }

        .slide.active {
            display: flex;
            animation: slideIn 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94);
        }

        @keyframes slideIn {
            from {
                opacity: 0;
                transform: translateY(30px) scale(0.95);
            }
            to {
                opacity: 1;
                transform: translateY(0) scale(1);
            }
        }

        .slide-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 40px;
            padding-bottom: 20px;
            border-bottom: 3px solid transparent;
            background: var(--primary-gradient);
            background-clip: border-box;
            -webkit-background-clip: border-box;
            border-image: var(--primary-gradient) 1;
        }

        .slide-number {
            background: var(--primary-gradient);
            color: var(--white);
            padding: 10px 18px;
            border-radius: 25px;
            font-weight: 600;
            font-size: 14px;
            box-shadow: var(--shadow-soft);
        }

        .company-logo {
            background: var(--secondary-gradient);
            color: var(--white);
            padding: 12px 24px;
            border-radius: 12px;
            font-weight: 700;
            font-size: 18px;
            box-shadow: var(--shadow-soft);
        }

        .slide-title {
            font-size: 3.5em;
            font-weight: 700;
            background: var(--primary-gradient);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 20px;
            line-height: 1.2;
        }

        .slide-subtitle {
            font-size: 1.5em;
            color: var(--text-secondary);
            margin-bottom: 40px;
            font-weight: 400;
        }

        .slide-content {
            flex: 1;
            display: flex;
            flex-direction: column;
            justify-content: center;
        }

        /* 专业图标样式 */
        .icon-pro {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            width: 60px;
            height: 60px;
            border-radius: 50%;
            background: var(--success-gradient);
            color: var(--white);
            font-size: 24px;
            margin-right: 20px;
            box-shadow: var(--shadow-soft);
            transition: all 0.3s ease;
        }

        .icon-pro:hover {
            transform: scale(1.1) rotate(5deg);
            box-shadow: var(--shadow-medium);
        }

        .icon-large {
            width: 120px;
            height: 120px;
            font-size: 48px;
            margin: 20px auto;
        }

        .bullet-points {
            list-style: none;
            padding: 0;
        }

        .bullet-points li {
            font-size: 1.4em;
            margin: 20px 0;
            padding: 25px;
            background: var(--bg-light);
            border-radius: var(--border-radius);
            border-left: 5px solid transparent;
            border-image: var(--primary-gradient) 1;
            display: flex;
            align-items: center;
            transition: all 0.3s ease;
            box-shadow: var(--shadow-soft);
        }

        .bullet-points li:hover {
            transform: translateX(10px);
            box-shadow: var(--shadow-medium);
        }

        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 30px;
            margin: 30px 0;
        }

        .feature-card {
            background: var(--white);
            border-radius: var(--border-radius);
            padding: 30px;
            text-align: center;
            box-shadow: var(--shadow-soft);
            transition: all 0.3s ease;
            border: 2px solid transparent;
        }

        .feature-card:hover {
            transform: translateY(-10px);
            box-shadow: var(--shadow-medium);
            border-image: var(--primary-gradient) 1;
        }

        .feature-card .icon-pro {
            margin: 0 auto 20px;
        }

        .comparison-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
            background: var(--white);
            border-radius: var(--border-radius);
            overflow: hidden;
            box-shadow: var(--shadow-soft);
        }

        .comparison-table th {
            background: var(--primary-gradient);
            color: var(--white);
            padding: 20px;
            font-weight: 600;
            font-size: 1.2em;
        }

        .comparison-table td {
            padding: 20px;
            border-bottom: 1px solid #e2e8f0;
            font-size: 1.1em;
        }

        .comparison-table tr:nth-child(even) {
            background: var(--bg-light);
        }

        .highlight-box {
            background: var(--success-gradient);
            color: var(--white);
            padding: 30px;
            border-radius: var(--border-radius);
            margin: 20px 0;
            text-align: center;
            box-shadow: var(--shadow-soft);
        }

        .warning-box {
            background: var(--secondary-gradient);
            color: var(--white);
            padding: 30px;
            border-radius: var(--border-radius);
            margin: 20px 0;
            text-align: center;
            box-shadow: var(--shadow-soft);
        }

        .flow-diagram {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin: 30px 0;
            flex-wrap: wrap;
            gap: 20px;
        }

        .flow-step {
            background: var(--primary-gradient);
            color: var(--white);
            padding: 20px;
            border-radius: var(--border-radius);
            flex: 1;
            min-width: 150px;
            text-align: center;
            font-weight: 600;
            position: relative;
            transition: all 0.3s ease;
            box-shadow: var(--shadow-soft);
        }

        .flow-step:hover {
            transform: scale(1.05);
            box-shadow: var(--shadow-medium);
        }

        .flow-step::after {
            content: "\f061";
            font-family: "Font Awesome 6 Free";
            font-weight: 900;
            position: absolute;
            right: -25px;
            top: 50%;
            transform: translateY(-50%);
            font-size: 1.5em;
            color: var(--text-secondary);
        }

        .flow-step:last-child::after {
            display: none;
        }

        .navigation {
            position: fixed;
            bottom: 30px;
            left: 50%;
            transform: translateX(-50%);
            display: flex;
            gap: 15px;
            z-index: 1000;
        }

        .nav-btn {
            background: var(--white);
            border: 2px solid transparent;
            background-clip: padding-box;
            color: var(--text-primary);
            padding: 12px 24px;
            border-radius: 25px;
            cursor: pointer;
            font-weight: 600;
            transition: all 0.3s ease;
            box-shadow: var(--shadow-soft);
        }

        .nav-btn:hover {
            background: var(--primary-gradient);
            color: var(--white);
            transform: translateY(-2px);
            box-shadow: var(--shadow-medium);
        }

        .nav-btn.active {
            background: var(--primary-gradient);
            color: var(--white);
        }

        .slide-counter {
            position: fixed;
            top: 30px;
            right: 30px;
            background: rgba(255,255,255,0.95);
            backdrop-filter: blur(10px);
            padding: 12px 24px;
            border-radius: 25px;
            font-weight: 600;
            color: var(--text-primary);
            z-index: 1000;
            box-shadow: var(--shadow-soft);
        }

        .progress-bar {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 4px;
            background: rgba(255,255,255,0.3);
            z-index: 1000;
        }

        .progress-fill {
            height: 100%;
            background: var(--secondary-gradient);
            transition: width 0.3s ease;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .slide {
                width: 95vw;
                height: 90vh;
                padding: 30px;
            }
            
            .slide-title {
                font-size: 2.5em;
            }
            
            .feature-grid {
                grid-template-columns: 1fr;
                gap: 20px;
            }
            
            .flow-diagram {
                flex-direction: column;
            }
            
            .flow-step::after {
                content: "\f063";
                right: 50%;
                top: 100%;
                transform: translateX(50%);
            }
        }
    </style>
</head>
<body>
    <!-- 进度条 -->
    <div class="progress-bar">
        <div class="progress-fill" id="progress-fill"></div>
    </div>

    <!-- 幻灯片计数器 -->
    <div class="slide-counter">
        <i class="fas fa-presentation"></i>
        <span id="current-slide">1</span> / <span id="total-slides">8</span>
    </div>

    <div class="presentation-container">

        <!-- 幻灯片 1: 封面 -->
        <div class="slide active" id="slide-1">
            <div class="slide-header">
                <div class="slide-number">01</div>
                <div class="company-logo">
                    <i class="fas fa-server"></i> NGINX PRO
                </div>
            </div>
            <div class="slide-content">
                <h1 class="slide-title">
                    <i class="fas fa-rocket"></i> Nginx高级培训
                </h1>
                <p class="slide-subtitle">企业级Web服务器与反向代理解决方案</p>
                <div style="text-align: center; margin-top: 60px;">
                    <div class="icon-pro icon-large" style="background: var(--warning-gradient);">
                        <i class="fas fa-bolt"></i>
                    </div>
                    <p style="font-size: 1.3em; color: var(--text-secondary); margin-top: 20px;">
                        <i class="fas fa-chart-line"></i> 提升性能 ·
                        <i class="fas fa-shield-alt"></i> 增强安全 ·
                        <i class="fas fa-cogs"></i> 优化架构
                    </p>
                </div>
            </div>
        </div>

        <!-- 幻灯片 2: Nginx核心优势 -->
        <div class="slide" id="slide-2">
            <div class="slide-header">
                <div class="slide-number">02</div>
                <div class="company-logo">
                    <i class="fas fa-server"></i> NGINX PRO
                </div>
            </div>
            <div class="slide-content">
                <h1 class="slide-title">
                    <i class="fas fa-star"></i> 核心优势
                </h1>
                <div class="feature-grid">
                    <div class="feature-card">
                        <div class="icon-pro" style="background: var(--success-gradient);">
                            <i class="fas fa-tachometer-alt"></i>
                        </div>
                        <h3>极致性能</h3>
                        <p>单机支持50,000+并发连接<br>内存占用仅为Apache的1/10</p>
                    </div>
                    <div class="feature-card">
                        <div class="icon-pro" style="background: var(--warning-gradient);">
                            <i class="fas fa-shield-alt"></i>
                        </div>
                        <h3>企业安全</h3>
                        <p>DDoS防护、WAF集成<br>SSL/TLS优化、访问控制</p>
                    </div>
                    <div class="feature-card">
                        <div class="icon-pro" style="background: var(--secondary-gradient);">
                            <i class="fas fa-expand-arrows-alt"></i>
                        </div>
                        <h3>弹性扩展</h3>
                        <p>动态负载均衡<br>无缝水平扩展</p>
                    </div>
                    <div class="feature-card">
                        <div class="icon-pro" style="background: var(--dark-gradient);">
                            <i class="fas fa-tools"></i>
                        </div>
                        <h3>运维友好</h3>
                        <p>热重载配置<br>详细监控指标</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- 幻灯片 3: 架构设计 -->
        <div class="slide" id="slide-3">
            <div class="slide-header">
                <div class="slide-number">03</div>
                <div class="company-logo">
                    <i class="fas fa-server"></i> NGINX PRO
                </div>
            </div>
            <div class="slide-content">
                <h1 class="slide-title">
                    <i class="fas fa-sitemap"></i> 现代Web架构
                </h1>
                <div class="flow-diagram">
                    <div class="flow-step">
                        <i class="fas fa-users"></i><br>
                        用户层<br>
                        <small>Web/Mobile</small>
                    </div>
                    <div class="flow-step">
                        <i class="fas fa-network-wired"></i><br>
                        CDN层<br>
                        <small>全球加速</small>
                    </div>
                    <div class="flow-step" style="background: var(--secondary-gradient);">
                        <i class="fas fa-server"></i><br>
                        Nginx层<br>
                        <small>负载均衡</small>
                    </div>
                    <div class="flow-step">
                        <i class="fas fa-microchip"></i><br>
                        应用层<br>
                        <small>业务逻辑</small>
                    </div>
                    <div class="flow-step">
                        <i class="fas fa-database"></i><br>
                        数据层<br>
                        <small>持久化存储</small>
                    </div>
                </div>
                <div class="highlight-box">
                    <h3><i class="fas fa-lightbulb"></i> 架构优势</h3>
                    <p>通过分层设计实现高可用、高性能、易扩展的企业级Web服务</p>
                </div>
            </div>
        </div>

        <!-- 幻灯片 4: 负载均衡策略 -->
        <div class="slide" id="slide-4">
            <div class="slide-header">
                <div class="slide-number">04</div>
                <div class="company-logo">
                    <i class="fas fa-server"></i> NGINX PRO
                </div>
            </div>
            <div class="slide-content">
                <h1 class="slide-title">
                    <i class="fas fa-balance-scale"></i> 负载均衡策略
                </h1>
                <div class="feature-grid">
                    <div class="feature-card">
                        <div class="icon-pro" style="background: var(--success-gradient);">
                            <i class="fas fa-sync-alt"></i>
                        </div>
                        <h3>轮询算法</h3>
                        <p>Round Robin<br>平均分配请求</p>
                    </div>
                    <div class="feature-card">
                        <div class="icon-pro" style="background: var(--warning-gradient);">
                            <i class="fas fa-weight-hanging"></i>
                        </div>
                        <h3>加权轮询</h3>
                        <p>Weighted Round Robin<br>按权重分配</p>
                    </div>
                    <div class="feature-card">
                        <div class="icon-pro" style="background: var(--secondary-gradient);">
                            <i class="fas fa-chart-line"></i>
                        </div>
                        <h3>最少连接</h3>
                        <p>Least Connections<br>智能负载分配</p>
                    </div>
                    <div class="feature-card">
                        <div class="icon-pro" style="background: var(--dark-gradient);">
                            <i class="fas fa-fingerprint"></i>
                        </div>
                        <h3>IP哈希</h3>
                        <p>IP Hash<br>会话保持</p>
                    </div>
                </div>
                <table class="comparison-table">
                    <tr>
                        <th><i class="fas fa-cog"></i> 策略</th>
                        <th><i class="fas fa-chart-bar"></i> 适用场景</th>
                        <th><i class="fas fa-star"></i> 优势</th>
                    </tr>
                    <tr>
                        <td>轮询</td>
                        <td>无状态应用</td>
                        <td>简单高效</td>
                    </tr>
                    <tr>
                        <td>加权轮询</td>
                        <td>服务器性能不同</td>
                        <td>资源优化</td>
                    </tr>
                    <tr>
                        <td>最少连接</td>
                        <td>长连接应用</td>
                        <td>动态平衡</td>
                    </tr>
                    <tr>
                        <td>IP哈希</td>
                        <td>有状态应用</td>
                        <td>会话一致性</td>
                    </tr>
                </table>
            </div>
        </div>

        <!-- 幻灯片 5: 安全防护 -->
        <div class="slide" id="slide-5">
            <div class="slide-header">
                <div class="slide-number">05</div>
                <div class="company-logo">
                    <i class="fas fa-server"></i> NGINX PRO
                </div>
            </div>
            <div class="slide-content">
                <h1 class="slide-title">
                    <i class="fas fa-shield-alt"></i> 企业级安全防护
                </h1>
                <ul class="bullet-points">
                    <li>
                        <div class="icon-pro" style="background: var(--secondary-gradient);">
                            <i class="fas fa-ban"></i>
                        </div>
                        <div>
                            <strong>DDoS防护</strong><br>
                            智能识别和阻断恶意流量，保护后端服务器
                        </div>
                    </li>
                    <li>
                        <div class="icon-pro" style="background: var(--warning-gradient);">
                            <i class="fas fa-lock"></i>
                        </div>
                        <div>
                            <strong>SSL/TLS优化</strong><br>
                            支持TLS 1.3，OCSP装订，完美前向保密
                        </div>
                    </li>
                    <li>
                        <div class="icon-pro" style="background: var(--success-gradient);">
                            <i class="fas fa-user-shield"></i>
                        </div>
                        <div>
                            <strong>访问控制</strong><br>
                            基于IP、地理位置、用户代理的精细化控制
                        </div>
                    </li>
                    <li>
                        <div class="icon-pro" style="background: var(--dark-gradient);">
                            <i class="fas fa-eye"></i>
                        </div>
                        <div>
                            <strong>实时监控</strong><br>
                            详细的访问日志和性能指标监控
                        </div>
                    </li>
                </ul>
            </div>
        </div>

        <!-- 幻灯片 6: 性能优化 -->
        <div class="slide" id="slide-6">
            <div class="slide-header">
                <div class="slide-number">06</div>
                <div class="company-logo">
                    <i class="fas fa-server"></i> NGINX PRO
                </div>
            </div>
            <div class="slide-content">
                <h1 class="slide-title">
                    <i class="fas fa-rocket"></i> 性能优化技术
                </h1>
                <div class="feature-grid">
                    <div class="feature-card">
                        <div class="icon-pro" style="background: var(--success-gradient);">
                            <i class="fas fa-memory"></i>
                        </div>
                        <h3>智能缓存</h3>
                        <p>多层缓存策略<br>缓存命中率>95%</p>
                    </div>
                    <div class="feature-card">
                        <div class="icon-pro" style="background: var(--warning-gradient);">
                            <i class="fas fa-compress-alt"></i>
                        </div>
                        <h3>内容压缩</h3>
                        <p>Gzip/Brotli压缩<br>带宽节省60%+</p>
                    </div>
                    <div class="feature-card">
                        <div class="icon-pro" style="background: var(--secondary-gradient);">
                            <i class="fas fa-link"></i>
                        </div>
                        <h3>连接复用</h3>
                        <p>HTTP/2 Server Push<br>Keep-Alive优化</p>
                    </div>
                    <div class="feature-card">
                        <div class="icon-pro" style="background: var(--dark-gradient);">
                            <i class="fas fa-globe"></i>
                        </div>
                        <h3>CDN集成</h3>
                        <p>全球节点分发<br>就近访问加速</p>
                    </div>
                </div>
                <div class="warning-box">
                    <h3><i class="fas fa-chart-line"></i> 性能提升数据</h3>
                    <p>响应时间减少70% · 并发能力提升10倍 · 服务器资源节省50%</p>
                </div>
            </div>
        </div>

        <!-- 幻灯片 7: 企业应用案例 -->
        <div class="slide" id="slide-7">
            <div class="slide-header">
                <div class="slide-number">07</div>
                <div class="company-logo">
                    <i class="fas fa-server"></i> NGINX PRO
                </div>
            </div>
            <div class="slide-content">
                <h1 class="slide-title">
                    <i class="fas fa-building"></i> 企业应用案例
                </h1>
                <div class="feature-grid">
                    <div class="feature-card">
                        <div class="icon-pro" style="background: var(--success-gradient);">
                            <i class="fas fa-shopping-cart"></i>
                        </div>
                        <h3>电商平台</h3>
                        <p>双11峰值：1000万QPS<br>99.99%可用性保障</p>
                    </div>
                    <div class="feature-card">
                        <div class="icon-pro" style="background: var(--warning-gradient);">
                            <i class="fas fa-video"></i>
                        </div>
                        <h3>视频流媒体</h3>
                        <p>全球CDN分发<br>4K视频无缓冲播放</p>
                    </div>
                    <div class="feature-card">
                        <div class="icon-pro" style="background: var(--secondary-gradient);">
                            <i class="fas fa-university"></i>
                        </div>
                        <h3>金融服务</h3>
                        <p>毫秒级响应<br>银行级安全防护</p>
                    </div>
                    <div class="feature-card">
                        <div class="icon-pro" style="background: var(--dark-gradient);">
                            <i class="fas fa-gamepad"></i>
                        </div>
                        <h3>游戏平台</h3>
                        <p>低延迟连接<br>百万在线用户支持</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- 幻灯片 8: 总结与展望 -->
        <div class="slide" id="slide-8">
            <div class="slide-header">
                <div class="slide-number">08</div>
                <div class="company-logo">
                    <i class="fas fa-server"></i> NGINX PRO
                </div>
            </div>
            <div class="slide-content">
                <h1 class="slide-title">
                    <i class="fas fa-flag-checkered"></i> 总结与展望
                </h1>
                <div style="text-align: center; margin: 40px 0;">
                    <div class="highlight-box">
                        <h2><i class="fas fa-trophy"></i> Nginx企业价值</h2>
                        <div style="display: grid; grid-template-columns: repeat(3, 1fr); gap: 30px; margin-top: 30px;">
                            <div>
                                <div class="icon-pro icon-large" style="background: var(--success-gradient);">
                                    <i class="fas fa-bolt"></i>
                                </div>
                                <h3>极致性能</h3>
                                <p>10倍性能提升</p>
                            </div>
                            <div>
                                <div class="icon-pro icon-large" style="background: var(--warning-gradient);">
                                    <i class="fas fa-shield-alt"></i>
                                </div>
                                <h3>企业安全</h3>
                                <p>多层防护体系</p>
                            </div>
                            <div>
                                <div class="icon-pro icon-large" style="background: var(--secondary-gradient);">
                                    <i class="fas fa-chart-line"></i>
                                </div>
                                <h3>业务增长</h3>
                                <p>支撑业务扩展</p>
                            </div>
                        </div>
                    </div>
                </div>
                <div style="text-align: center; margin-top: 60px;">
                    <h2 style="background: var(--primary-gradient); -webkit-background-clip: text; -webkit-text-fill-color: transparent; margin-bottom: 20px;">
                        <i class="fas fa-handshake"></i> 感谢聆听！
                    </h2>
                    <p style="font-size: 1.3em; color: var(--text-secondary);">
                        <i class="fas fa-comments"></i> 欢迎交流讨论 ·
                        <i class="fas fa-envelope"></i> 技术支持
                    </p>
                </div>
            </div>
        </div>
    </div>

    <!-- 导航控制 -->
    <div class="navigation">
        <button class="nav-btn" onclick="previousSlide()">
            <i class="fas fa-chevron-left"></i> 上一页
        </button>
        <button class="nav-btn" onclick="nextSlide()">
            下一页 <i class="fas fa-chevron-right"></i>
        </button>
        <button class="nav-btn" onclick="toggleFullscreen()">
            <i class="fas fa-expand"></i> 全屏
        </button>
    </div>

    <script>
        let currentSlide = 1;
        const totalSlides = 8;

        // 更新幻灯片显示
        function showSlide(slideNumber) {
            document.querySelectorAll('.slide').forEach(slide => {
                slide.classList.remove('active');
            });

            document.getElementById(`slide-${slideNumber}`).classList.add('active');
            document.getElementById('current-slide').textContent = slideNumber;

            const progress = (slideNumber / totalSlides) * 100;
            document.getElementById('progress-fill').style.width = progress + '%';

            currentSlide = slideNumber;
        }

        function nextSlide() {
            if (currentSlide < totalSlides) {
                showSlide(currentSlide + 1);
            }
        }

        function previousSlide() {
            if (currentSlide > 1) {
                showSlide(currentSlide - 1);
            }
        }

        function toggleFullscreen() {
            if (!document.fullscreenElement) {
                document.documentElement.requestFullscreen();
            } else {
                document.exitFullscreen();
            }
        }

        // 键盘事件监听
        document.addEventListener('keydown', function(e) {
            switch(e.key) {
                case 'ArrowRight':
                case ' ':
                    e.preventDefault();
                    nextSlide();
                    break;
                case 'ArrowLeft':
                    e.preventDefault();
                    previousSlide();
                    break;
                case 'F11':
                    e.preventDefault();
                    toggleFullscreen();
                    break;
            }
        });

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            document.getElementById('total-slides').textContent = totalSlides;
            showSlide(1);

            // 添加动画效果
            setInterval(() => {
                const icons = document.querySelectorAll('.icon-pro');
                icons.forEach(icon => {
                    if (Math.random() < 0.05) {
                        icon.style.transform = 'scale(1.1) rotate(5deg)';
                        setTimeout(() => {
                            icon.style.transform = 'scale(1) rotate(0deg)';
                        }, 300);
                    }
                });
            }, 3000);
        });
    </script>
</body>
</html>
