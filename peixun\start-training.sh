#!/bin/bash

# Nginx 培训启动脚本
# 用于快速启动培训演示

echo "🚀 欢迎使用 Web服务器培训方案！"
echo "=================================="
echo ""

# 检查是否在正确的目录
if [ ! -f "index.html" ]; then
    echo "❌ 错误：请在培训目录中运行此脚本"
    exit 1
fi

echo "📋 可用的培训模块："
echo ""
echo "1. 🔧 Nginx 入门培训"
echo "2. 🚀 网站测速跑分培训"
echo "3. ❓ 显示帮助信息"
echo "4. 🚪 退出"
echo ""

while true; do
    read -p "请选择一个培训模块 (1-4): " choice

    case $choice in
        1)
            echo "🔧 Nginx 入门培训"
            echo "=================="
            echo ""
            echo "📋 培训选项："
            echo "   a) 🎨 图文并茂介绍（零基础推荐）"
            echo "   b) 📺 打开网页演示（培训用）"
            echo "   c) 📖 查看培训讲义"
            echo "   d) 🛠️ 开始实践练习"
            echo "   e) 📁 查看演示配置"
            echo "   f) 返回主菜单"
            read -p "请选择选项 (a/b/c/d/e/f): " nginx_option

            case $nginx_option in
                a)
                    echo "🎨 正在打开 Nginx 图文介绍..."
                    if command -v xdg-open > /dev/null; then
                        xdg-open nginx-visual-guide.html
                    elif command -v open > /dev/null; then
                        open nginx-visual-guide.html
                    elif command -v start > /dev/null; then
                        start nginx-visual-guide.html
                    else
                        echo "请手动打开 nginx-visual-guide.html 文件"
                    fi
                    echo "✅ Nginx 图文介绍已打开，适合完全零基础的朋友"
                    ;;
                b)
                    echo "🌐 正在打开 Nginx 培训演示..."
                    if command -v xdg-open > /dev/null; then
                        xdg-open index.html
                    elif command -v open > /dev/null; then
                        open index.html
                    elif command -v start > /dev/null; then
                        start index.html
                    else
                        echo "请手动打开 index.html 文件"
                    fi
                    echo "✅ Nginx 培训演示已打开"
                    ;;
                c)
                    echo "📖 正在打开 Nginx 培训讲义..."
                    if command -v less > /dev/null; then
                        less nginx-basics.md
                    elif command -v more > /dev/null; then
                        more nginx-basics.md
                    else
                        cat nginx-basics.md
                    fi
                    ;;
                d)
                    echo "🛠️ Nginx 实践练习："
                    echo "   1) 练习1：搭建第一个网站"
                    echo "   2) 练习2：配置多个网站"
                    echo "   3) 返回上级菜单"
                    read -p "请选择练习 (1/2/3): " exercise

                    case $exercise in
                        1)
                            if command -v less > /dev/null; then
                                less exercises/exercise-1.md
                            else
                                cat exercises/exercise-1.md
                            fi
                            ;;
                        2)
                            if command -v less > /dev/null; then
                                less exercises/exercise-2.md
                            else
                                cat exercises/exercise-2.md
                            fi
                            ;;
                        3)
                            continue
                            ;;
                        *)
                            echo "❌ 无效选择"
                            ;;
                    esac
                    ;;
                e)
                    echo "📁 Nginx 演示配置文件："
                    echo "   1) 基础配置示例"
                    echo "   2) 负载均衡配置示例"
                    echo "   3) 返回上级菜单"
                    read -p "请选择配置文件 (1/2/3): " config

                    case $config in
                        1)
                            echo "📄 基础配置示例："
                            echo "=================="
                            cat demo/basic-config.conf
                            ;;
                        2)
                            echo "📄 负载均衡配置示例："
                            echo "===================="
                            cat demo/load-balancer.conf
                            ;;
                        3)
                            continue
                            ;;
                        *)
                            echo "❌ 无效选择"
                            ;;
                    esac
                    ;;
                f)
                    continue
                    ;;
                *)
                    echo "❌ 无效选择"
                    ;;
            esac
            ;;
        2)
            echo "🚀 网站测速跑分培训"
            echo "==================="
            echo ""
            echo "📋 培训选项："
            echo "   a) 📺 打开网页演示（推荐）"
            echo "   b) 📖 查看培训讲义"
            echo "   c) 🛠️ 开始实践练习"
            echo "   d) 🔧 查看优化工具"
            echo "   e) 返回主菜单"
            read -p "请选择选项 (a/b/c/d/e): " speed_option

            case $speed_option in
                a)
                    echo "🌐 正在打开网站测速培训演示..."
                    if command -v xdg-open > /dev/null; then
                        xdg-open speed-testing/index.html
                    elif command -v open > /dev/null; then
                        open speed-testing/index.html
                    elif command -v start > /dev/null; then
                        start speed-testing/index.html
                    else
                        echo "请手动打开 speed-testing/index.html 文件"
                    fi
                    echo "✅ 网站测速培训演示已打开"
                    ;;
                b)
                    echo "📖 正在打开网站测速培训讲义..."
                    if command -v less > /dev/null; then
                        less speed-testing/speed-testing-guide.md
                    elif command -v more > /dev/null; then
                        more speed-testing/speed-testing-guide.md
                    else
                        cat speed-testing/speed-testing-guide.md
                    fi
                    ;;
                c)
                    echo "🛠️ 网站测速实践练习："
                    echo "   1) 练习1：基础测速操作"
                    echo "   2) 练习2：优化方案设计"
                    echo "   3) 返回上级菜单"
                    read -p "请选择练习 (1/2/3): " speed_exercise

                    case $speed_exercise in
                        1)
                            if command -v less > /dev/null; then
                                less speed-testing/exercises/exercise-1-basic-testing.md
                            else
                                cat speed-testing/exercises/exercise-1-basic-testing.md
                            fi
                            ;;
                        2)
                            if command -v less > /dev/null; then
                                less speed-testing/exercises/exercise-2-optimization.md
                            else
                                cat speed-testing/exercises/exercise-2-optimization.md
                            fi
                            ;;
                        3)
                            continue
                            ;;
                        *)
                            echo "❌ 无效选择"
                            ;;
                    esac
                    ;;
                d)
                    echo "🔧 网站优化工具："
                    echo "   1) Nginx优化配置"
                    echo "   2) 测速检查清单"
                    echo "   3) 返回上级菜单"
                    read -p "请选择工具 (1/2/3): " tool

                    case $tool in
                        1)
                            echo "📄 Nginx优化配置："
                            echo "=================="
                            cat speed-testing/tools/nginx-optimization.conf
                            ;;
                        2)
                            echo "📄 测速检查清单："
                            echo "=================="
                            if command -v less > /dev/null; then
                                less speed-testing/tools/speed-test-checklist.md
                            else
                                cat speed-testing/tools/speed-test-checklist.md
                            fi
                            ;;
                        3)
                            continue
                            ;;
                        *)
                            echo "❌ 无效选择"
                            ;;
                    esac
                    ;;
                e)
                    continue
                    ;;
                *)
                    echo "❌ 无效选择"
                    ;;
            esac
            ;;
        3)
            echo "❓ 帮助信息"
            echo "==========="
            echo ""
            echo "📚 培训模块："
            echo "  🔧 Nginx 入门培训："
            echo "    - 适合零基础学员"
            echo "    - 学习 Nginx 基本概念和使用"
            echo "    - 培训时长：60分钟"
            echo ""
            echo "  🚀 网站测速跑分培训："
            echo "    - 适合技术支持、运维人员"
            echo "    - 解决网站访问慢的问题"
            echo "    - 培训时长：90分钟"
            echo ""
            echo "🎯 学习建议："
            echo "  1. 先看网页演示了解概念"
            echo "  2. 阅读详细讲义加深理解"
            echo "  3. 完成实践练习巩固知识"
            echo ""
            echo "🚨 遇到问题："
            echo "  - 查看各模块的 README.md"
            echo "  - 参考讲义中的故障排除部分"
            echo ""
            ;;
        4)
            echo "👋 感谢使用 Web服务器培训方案！"
            echo "祝您学习愉快！🎉"
            exit 0
            ;;
        *)
            echo "❌ 无效选择，请输入 1-4"
            ;;
    esac
    
    echo ""
    echo "按 Enter 键继续..."
    read
    echo ""
done
