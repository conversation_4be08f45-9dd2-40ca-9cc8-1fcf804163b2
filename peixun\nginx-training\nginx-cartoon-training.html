<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Nginx超级英雄学院 🦸‍♂️</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Comic+Neue:wght@300;400;700&display=swap');
        
        :root {
            --hero-blue: #4A90E2;
            --hero-red: #E74C3C;
            --hero-green: #2ECC71;
            --hero-yellow: #F1C40F;
            --hero-purple: #9B59B6;
            --comic-bg: #FFF8DC;
            --speech-bubble: #FFFFFF;
            --shadow-color: rgba(0,0,0,0.2);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Comic Neue', cursive;
            background: linear-gradient(45deg, #FFE5B4 0%, #FFCC5C 50%, #FF6B6B 100%);
            min-height: 100vh;
            overflow-x: hidden;
        }

        .comic-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .comic-header {
            text-align: center;
            background: var(--speech-bubble);
            border: 4px solid #333;
            border-radius: 20px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 8px 8px 0px var(--shadow-color);
            position: relative;
        }

        .comic-header::before {
            content: "💥";
            position: absolute;
            top: -20px;
            left: 20px;
            font-size: 3em;
            background: var(--hero-yellow);
            border-radius: 50%;
            width: 60px;
            height: 60px;
            display: flex;
            align-items: center;
            justify-content: center;
            border: 3px solid #333;
        }

        .comic-title {
            font-size: 3em;
            font-weight: 700;
            color: var(--hero-blue);
            text-shadow: 3px 3px 0px #333;
            margin-bottom: 10px;
            transform: rotate(-2deg);
        }

        .comic-subtitle {
            font-size: 1.5em;
            color: var(--hero-red);
            font-weight: 400;
        }

        .character-selector {
            display: flex;
            justify-content: center;
            gap: 20px;
            margin: 30px 0;
            flex-wrap: wrap;
        }

        .character-card {
            background: var(--speech-bubble);
            border: 3px solid #333;
            border-radius: 15px;
            padding: 20px;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 4px 4px 0px var(--shadow-color);
            min-width: 150px;
            text-align: center;
        }

        .character-card:hover {
            transform: translateY(-5px) rotate(2deg);
            box-shadow: 6px 6px 0px var(--shadow-color);
        }

        .character-card.selected {
            background: var(--hero-yellow);
            transform: scale(1.1);
        }

        .character-avatar {
            font-size: 3em;
            margin-bottom: 10px;
        }

        .character-name {
            font-weight: 700;
            color: #333;
            margin-bottom: 5px;
        }

        .character-role {
            font-size: 0.9em;
            color: #666;
        }

        .story-panel {
            background: var(--speech-bubble);
            border: 4px solid #333;
            border-radius: 20px;
            margin: 20px 0;
            padding: 30px;
            box-shadow: 6px 6px 0px var(--shadow-color);
            position: relative;
            display: none;
        }

        .story-panel.active {
            display: block;
            animation: slideIn 0.5s ease-out;
        }

        @keyframes slideIn {
            from {
                opacity: 0;
                transform: translateX(-50px);
            }
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }

        .panel-header {
            display: flex;
            align-items: center;
            margin-bottom: 20px;
        }

        .panel-number {
            background: var(--hero-red);
            color: white;
            border-radius: 50%;
            width: 50px;
            height: 50px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 700;
            font-size: 1.2em;
            border: 3px solid #333;
            margin-right: 15px;
        }

        .panel-title {
            font-size: 2em;
            font-weight: 700;
            color: var(--hero-blue);
            text-shadow: 2px 2px 0px #333;
        }

        .speech-bubble {
            background: var(--speech-bubble);
            border: 3px solid #333;
            border-radius: 20px;
            padding: 20px;
            margin: 15px 0;
            position: relative;
            box-shadow: 3px 3px 0px var(--shadow-color);
        }

        .speech-bubble::before {
            content: "";
            position: absolute;
            bottom: -15px;
            left: 30px;
            width: 0;
            height: 0;
            border-left: 15px solid transparent;
            border-right: 15px solid transparent;
            border-top: 15px solid #333;
        }

        .speech-bubble::after {
            content: "";
            position: absolute;
            bottom: -12px;
            left: 32px;
            width: 0;
            height: 0;
            border-left: 13px solid transparent;
            border-right: 13px solid transparent;
            border-top: 13px solid var(--speech-bubble);
        }

        .character-speech {
            display: flex;
            align-items: flex-start;
            gap: 15px;
            margin: 20px 0;
        }

        .character-avatar-small {
            font-size: 2em;
            background: var(--hero-yellow);
            border-radius: 50%;
            width: 60px;
            height: 60px;
            display: flex;
            align-items: center;
            justify-content: center;
            border: 3px solid #333;
            flex-shrink: 0;
        }

        .thought-bubble {
            background: linear-gradient(135deg, #E8F4FD 0%, #B3D9FF 100%);
            border: 3px dashed var(--hero-blue);
            border-radius: 20px;
            padding: 20px;
            margin: 15px 0;
            position: relative;
        }

        .thought-bubble::before {
            content: "💭";
            position: absolute;
            top: -15px;
            right: 20px;
            font-size: 2em;
            background: var(--speech-bubble);
            border-radius: 50%;
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border: 2px solid var(--hero-blue);
        }

        .action-scene {
            background: linear-gradient(135deg, #FFE5E5 0%, #FFB3B3 100%);
            border: 4px solid var(--hero-red);
            border-radius: 15px;
            padding: 25px;
            margin: 20px 0;
            position: relative;
        }

        .action-scene::before {
            content: "⚡";
            position: absolute;
            top: -20px;
            left: 20px;
            font-size: 2.5em;
            background: var(--hero-yellow);
            border-radius: 50%;
            width: 50px;
            height: 50px;
            display: flex;
            align-items: center;
            justify-content: center;
            border: 3px solid #333;
        }

        .comic-diagram {
            background: var(--comic-bg);
            border: 3px solid #333;
            border-radius: 15px;
            padding: 20px;
            margin: 20px 0;
            text-align: center;
        }

        .flow-comic {
            display: flex;
            align-items: center;
            justify-content: space-between;
            flex-wrap: wrap;
            gap: 15px;
        }

        .flow-step-comic {
            background: var(--hero-green);
            color: white;
            border: 3px solid #333;
            border-radius: 15px;
            padding: 15px;
            font-weight: 700;
            flex: 1;
            min-width: 120px;
            position: relative;
            box-shadow: 3px 3px 0px var(--shadow-color);
        }

        .flow-step-comic::after {
            content: "➤";
            position: absolute;
            right: -20px;
            top: 50%;
            transform: translateY(-50%);
            font-size: 1.5em;
            color: #333;
            font-weight: 700;
        }

        .flow-step-comic:last-child::after {
            display: none;
        }

        .navigation-comic {
            display: flex;
            justify-content: center;
            gap: 15px;
            margin: 30px 0;
            flex-wrap: wrap;
        }

        .nav-button-comic {
            background: var(--hero-blue);
            color: white;
            border: 3px solid #333;
            border-radius: 25px;
            padding: 12px 25px;
            font-family: 'Comic Neue', cursive;
            font-weight: 700;
            font-size: 1.1em;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 4px 4px 0px var(--shadow-color);
        }

        .nav-button-comic:hover {
            transform: translateY(-3px);
            box-shadow: 6px 6px 0px var(--shadow-color);
        }

        .nav-button-comic.active {
            background: var(--hero-red);
            transform: scale(1.1);
        }

        .interactive-demo-comic {
            background: linear-gradient(135deg, #E8F8E8 0%, #B3FFB3 100%);
            border: 3px solid var(--hero-green);
            border-radius: 20px;
            padding: 25px;
            margin: 20px 0;
            text-align: center;
        }

        .demo-button-comic {
            background: var(--hero-purple);
            color: white;
            border: 3px solid #333;
            border-radius: 15px;
            padding: 10px 20px;
            margin: 10px;
            font-family: 'Comic Neue', cursive;
            font-weight: 700;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 3px 3px 0px var(--shadow-color);
        }

        .demo-button-comic:hover {
            transform: translateY(-2px) rotate(2deg);
            box-shadow: 5px 5px 0px var(--shadow-color);
        }

        .result-display {
            margin-top: 20px;
            padding: 20px;
            background: var(--speech-bubble);
            border: 3px solid #333;
            border-radius: 15px;
            box-shadow: 3px 3px 0px var(--shadow-color);
        }

        .progress-comic {
            background: var(--speech-bubble);
            border: 3px solid #333;
            border-radius: 25px;
            height: 20px;
            margin: 20px 0;
            overflow: hidden;
            position: relative;
        }

        .progress-fill-comic {
            height: 100%;
            background: linear-gradient(90deg, var(--hero-green), var(--hero-blue));
            border-radius: 22px;
            transition: width 0.5s ease;
            position: relative;
        }

        .progress-fill-comic::after {
            content: "🚀";
            position: absolute;
            right: -10px;
            top: 50%;
            transform: translateY(-50%);
            font-size: 1.2em;
        }

        @media (max-width: 768px) {
            .comic-title {
                font-size: 2em;
            }
            
            .character-selector {
                gap: 10px;
            }
            
            .character-card {
                min-width: 120px;
                padding: 15px;
            }
            
            .flow-comic {
                flex-direction: column;
            }
            
            .flow-step-comic::after {
                content: "⬇";
                right: 50%;
                top: 100%;
                transform: translateX(50%);
            }
        }
    </style>
</head>
<body>
    <div class="comic-container">
        <!-- 漫画头部 -->
        <div class="comic-header">
            <h1 class="comic-title">Nginx超级英雄学院</h1>
            <p class="comic-subtitle">🦸‍♂️ 拯救网站世界的冒险之旅 🌍</p>
        </div>

        <!-- 角色选择 -->
        <div class="character-selector">
            <div class="character-card selected" data-character="nginx">
                <div class="character-avatar">⚡</div>
                <div class="character-name">Nginx队长</div>
                <div class="character-role">超级服务器英雄</div>
            </div>
            <div class="character-card" data-character="frontend">
                <div class="character-avatar">💻</div>
                <div class="character-name">前端小姐</div>
                <div class="character-role">用户界面专家</div>
            </div>
            <div class="character-card" data-character="backend">
                <div class="character-avatar">🔧</div>
                <div class="character-name">后端大叔</div>
                <div class="character-role">数据处理高手</div>
            </div>
            <div class="character-card" data-character="database">
                <div class="character-avatar">💾</div>
                <div class="character-name">数据库博士</div>
                <div class="character-role">信息存储专家</div>
            </div>
        </div>

        <!-- 进度条 -->
        <div class="progress-comic">
            <div class="progress-fill-comic" id="progress-fill-comic" style="width: 20%"></div>
        </div>

        <!-- 导航 -->
        <div class="navigation-comic">
            <button class="nav-button-comic active" onclick="showStory(0)">🏠 英雄起源</button>
            <button class="nav-button-comic" onclick="showStory(1)">🤝 团队组建</button>
            <button class="nav-button-comic" onclick="showStory(2)">📮 通信秘籍</button>
            <button class="nav-button-comic" onclick="showStory(3)">🚦 指挥艺术</button>
            <button class="nav-button-comic" onclick="showStory(4)">🛡️ 防护技能</button>
        </div>

        <!-- 第一话：英雄起源 -->
        <div class="story-panel active" id="story-0">
            <div class="panel-header">
                <div class="panel-number">1</div>
                <h2 class="panel-title">🏠 英雄起源：Nginx队长的诞生</h2>
            </div>

            <div class="character-speech">
                <div class="character-avatar-small">🦸‍♂️</div>
                <div class="speech-bubble">
                    <strong>Nginx队长：</strong>"大家好！我是Nginx队长，网站世界的守护者！让我告诉你们我是如何成为超级英雄的..."
                </div>
            </div>

            <div class="action-scene">
                <h3>🌆 网站城市的危机</h3>
                <p>很久很久以前，网站城市里住着很多普通的服务器居民。他们每天都要处理来自用户王国的各种请求...</p>
                <br>
                <p><strong>问题：</strong>普通服务器一次只能帮助一个用户，当很多用户同时来访问时，就会排起长长的队伍！</p>
            </div>

            <div class="character-speech">
                <div class="character-avatar-small">😰</div>
                <div class="speech-bubble">
                    <strong>普通服务器：</strong>"救命啊！用户太多了，我处理不过来了！网站要崩溃了！"
                </div>
            </div>

            <div class="thought-bubble">
                <strong>这时候，Nginx队长出现了！</strong><br>
                他拥有神奇的超能力：<br>
                ⚡ <strong>超级多任务</strong> - 同时处理成千上万个请求<br>
                💪 <strong>超低消耗</strong> - 只需要很少的能量<br>
                🛡️ <strong>永不疲倦</strong> - 可以24小时不停工作
            </div>

            <div class="comic-diagram">
                <h4>🔄 Nginx队长的超能力演示</h4>
                <div class="flow-comic">
                    <div class="flow-step-comic">1000个用户请求</div>
                    <div class="flow-step-comic">Nginx队长接收</div>
                    <div class="flow-step-comic">智能分配处理</div>
                    <div class="flow-step-comic">所有用户满意</div>
                </div>
            </div>

            <div class="interactive-demo-comic">
                <h4>🎮 互动体验：对比普通服务器和Nginx队长</h4>
                <button class="demo-button-comic" onclick="showHeroDemo('normal')">普通服务器的一天</button>
                <button class="demo-button-comic" onclick="showHeroDemo('nginx')">Nginx队长的一天</button>
                <div id="hero-demo-result" class="result-display" style="display: none;"></div>
            </div>

            <div class="character-speech">
                <div class="character-avatar-small">🦸‍♂️</div>
                <div class="speech-bubble">
                    <strong>Nginx队长：</strong>"从那天起，我就决定要保护所有的网站，让用户们都能快速访问到他们想要的内容！"
                </div>
            </div>
        </div>

        <!-- 第二话：团队组建 -->
        <div class="story-panel" id="story-1">
            <div class="panel-header">
                <div class="panel-number">2</div>
                <h2 class="panel-title">🤝 团队组建：超级英雄联盟</h2>
            </div>

            <div class="character-speech">
                <div class="character-avatar-small">🦸‍♂️</div>
                <div class="speech-bubble">
                    <strong>Nginx队长：</strong>"一个人的力量是有限的，我需要组建一个超级英雄团队！"
                </div>
            </div>

            <div class="action-scene">
                <h3>🏢 超级英雄总部的建立</h3>
                <p>Nginx队长建立了一个神奇的总部，就像一个超级智能的指挥中心...</p>
            </div>

            <div class="comic-diagram">
                <h4>🏗️ 英雄总部架构图</h4>
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin: 20px 0;">
                    <div style="background: #FFE5E5; border: 3px solid #E74C3C; border-radius: 15px; padding: 20px; text-align: center;">
                        <div style="font-size: 2em;">👩‍💻</div>
                        <h4>前端小姐</h4>
                        <p><strong>职责：</strong>接待用户</p>
                        <p><strong>技能：</strong>美丽界面设计</p>
                        <p><strong>位置：</strong>总部大厅</p>
                    </div>
                    <div style="background: #E5F5E5; border: 3px solid #2ECC71; border-radius: 15px; padding: 20px; text-align: center;">
                        <div style="font-size: 2em;">👨‍🔧</div>
                        <h4>后端大叔</h4>
                        <p><strong>职责：</strong>处理业务逻辑</p>
                        <p><strong>技能：</strong>数据计算处理</p>
                        <p><strong>位置：</strong>总部工作室</p>
                    </div>
                    <div style="background: #E5E5FF; border: 3px solid #9B59B6; border-radius: 15px; padding: 20px; text-align: center;">
                        <div style="font-size: 2em;">🗄️</div>
                        <h4>数据库博士</h4>
                        <p><strong>职责：</strong>存储管理信息</p>
                        <p><strong>技能：</strong>超级记忆力</p>
                        <p><strong>位置：</strong>总部地下室</p>
                    </div>
                </div>
            </div>

            <div class="character-speech">
                <div class="character-avatar-small">👩‍💻</div>
                <div class="speech-bubble">
                    <strong>前端小姐：</strong>"我负责让用户看到漂亮的界面，就像商店的橱窗展示！"
                </div>
            </div>

            <div class="character-speech">
                <div class="character-avatar-small">👨‍🔧</div>
                <div class="speech-bubble">
                    <strong>后端大叔：</strong>"我在后台处理各种复杂的计算，就像工厂里的生产线！"
                </div>
            </div>

            <div class="character-speech">
                <div class="character-avatar-small">🗄️</div>
                <div class="speech-bubble">
                    <strong>数据库博士：</strong>"我记住所有的信息，需要什么数据随时可以找到！"
                </div>
            </div>

            <div class="thought-bubble">
                <strong>团队合作的魔法：</strong><br>
                当用户来访问网站时，就像客人来到超级英雄总部：<br>
                1. 前端小姐热情接待，展示美丽界面<br>
                2. 后端大叔在后台处理用户的请求<br>
                3. 数据库博士提供需要的信息<br>
                4. Nginx队长协调整个过程，确保一切顺利
            </div>

            <div class="interactive-demo-comic">
                <h4>🎭 角色扮演：一次完整的用户访问</h4>
                <button class="demo-button-comic" onclick="showTeamDemo('login')">用户登录过程</button>
                <button class="demo-button-comic" onclick="showTeamDemo('shopping')">在线购物过程</button>
                <button class="demo-button-comic" onclick="showTeamDemo('video')">观看视频过程</button>
                <div id="team-demo-result" class="result-display" style="display: none;"></div>
            </div>
        </div>

        <!-- 第三话：通信秘籍 -->
        <div class="story-panel" id="story-2">
            <div class="panel-header">
                <div class="panel-number">3</div>
                <h2 class="panel-title">📮 通信秘籍：HTTP魔法邮局</h2>
            </div>

            <div class="character-speech">
                <div class="character-avatar-small">🦸‍♂️</div>
                <div class="speech-bubble">
                    <strong>Nginx队长：</strong>"要成为优秀的网站守护者，必须掌握HTTP通信魔法！"
                </div>
            </div>

            <div class="action-scene">
                <h3>🏰 魔法邮局的秘密</h3>
                <p>在网站世界里，有一个神奇的邮局，专门负责传递用户和网站之间的消息...</p>
                <p>这个邮局使用一种叫做"HTTP"的魔法语言！</p>
            </div>

            <div class="comic-diagram">
                <h4>📬 HTTP魔法咒语大全</h4>
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 15px;">
                    <div style="background: #E8F4FD; border: 3px solid #4A90E2; border-radius: 15px; padding: 15px;">
                        <div style="font-size: 1.5em;">📖 GET咒语</div>
                        <p><strong>魔法效果：</strong>获取信息</p>
                        <p><strong>生活比喻：</strong>去图书馆借书</p>
                        <p><strong>使用场景：</strong>打开网页、查看图片</p>
                    </div>
                    <div style="background: #E8F8E8; border: 3px solid #2ECC71; border-radius: 15px; padding: 15px;">
                        <div style="font-size: 1.5em;">💌 POST咒语</div>
                        <p><strong>魔法效果：</strong>发送信息</p>
                        <p><strong>生活比喻：</strong>寄信给朋友</p>
                        <p><strong>使用场景：</strong>登录、注册、发评论</p>
                    </div>
                    <div style="background: #FFF0E8; border: 3px solid #F39C12; border-radius: 15px; padding: 15px;">
                        <div style="font-size: 1.5em;">✏️ PUT咒语</div>
                        <p><strong>魔法效果：</strong>更新信息</p>
                        <p><strong>生活比喻：</strong>修改通讯录</p>
                        <p><strong>使用场景：</strong>修改个人资料</p>
                    </div>
                    <div style="background: #FFE8E8; border: 3px solid #E74C3C; border-radius: 15px; padding: 15px;">
                        <div style="font-size: 1.5em;">🗑️ DELETE咒语</div>
                        <p><strong>魔法效果：</strong>删除信息</p>
                        <p><strong>生活比喻：</strong>扔掉旧东西</p>
                        <p><strong>使用场景：</strong>删除文章、注销账户</p>
                    </div>
                </div>
            </div>

            <div class="character-speech">
                <div class="character-avatar-small">📮</div>
                <div class="speech-bubble">
                    <strong>邮局局长：</strong>"每个HTTP消息都有一个神奇的状态码，告诉你任务完成得怎么样！"
                </div>
            </div>

            <div class="thought-bubble">
                <strong>🎯 HTTP状态码解密：</strong><br>
                <strong>200 OK</strong> ✅ = "任务完美完成！"<br>
                <strong>404 Not Found</strong> ❌ = "找不到你要的东西！"<br>
                <strong>500 Error</strong> 💥 = "服务器出故障了！"<br>
                <strong>301 Moved</strong> 🔄 = "东西搬家了，新地址在这里！"
            </div>

            <div class="interactive-demo-comic">
                <h4>🎪 HTTP魔法表演</h4>
                <button class="demo-button-comic" onclick="showHttpDemo('get')">GET魔法演示</button>
                <button class="demo-button-comic" onclick="showHttpDemo('post')">POST魔法演示</button>
                <button class="demo-button-comic" onclick="showHttpDemo('error')">错误处理演示</button>
                <div id="http-demo-result" class="result-display" style="display: none;"></div>
            </div>
        </div>

        <!-- 第四话：指挥艺术 -->
        <div class="story-panel" id="story-3">
            <div class="panel-header">
                <div class="panel-number">4</div>
                <h2 class="panel-title">🚦 指挥艺术：反向代理的智慧</h2>
            </div>

            <div class="character-speech">
                <div class="character-avatar-small">🦸‍♂️</div>
                <div class="speech-bubble">
                    <strong>Nginx队长：</strong>"我最厉害的技能就是反向代理！让我展示给你看这个神奇的能力！"
                </div>
            </div>

            <div class="action-scene">
                <h3>🎭 超级英雄的分身术</h3>
                <p>当很多用户同时来访问网站时，Nginx队长会使用他的终极技能——反向代理分身术！</p>
                <p>他可以把用户的请求智能地分配给不同的后端英雄处理！</p>
            </div>

            <div class="comic-diagram">
                <h4>⚡ 反向代理分身术演示</h4>
                <div style="text-align: center; margin: 20px 0;">
                    <div style="background: #FFE5B4; border: 3px solid #F39C12; border-radius: 15px; padding: 20px; margin-bottom: 20px;">
                        <div style="font-size: 2em;">👥👥👥</div>
                        <p><strong>1000个用户同时访问</strong></p>
                    </div>
                    <div style="font-size: 2em; margin: 10px 0;">⬇️</div>
                    <div style="background: #E8F4FD; border: 3px solid #4A90E2; border-radius: 15px; padding: 20px; margin-bottom: 20px;">
                        <div style="font-size: 2em;">🦸‍♂️</div>
                        <p><strong>Nginx队长智能分发</strong></p>
                    </div>
                    <div style="font-size: 2em; margin: 10px 0;">⬇️</div>
                    <div style="display: flex; justify-content: space-around; flex-wrap: wrap; gap: 10px;">
                        <div style="background: #E8F8E8; border: 3px solid #2ECC71; border-radius: 10px; padding: 15px; flex: 1; min-width: 150px;">
                            <div>🖥️ 服务器1</div>
                            <div>处理300个请求</div>
                        </div>
                        <div style="background: #E8F8E8; border: 3px solid #2ECC71; border-radius: 10px; padding: 15px; flex: 1; min-width: 150px;">
                            <div>🖥️ 服务器2</div>
                            <div>处理350个请求</div>
                        </div>
                        <div style="background: #E8F8E8; border: 3px solid #2ECC71; border-radius: 10px; padding: 15px; flex: 1; min-width: 150px;">
                            <div>🖥️ 服务器3</div>
                            <div>处理350个请求</div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="character-speech">
                <div class="character-avatar-small">🦸‍♂️</div>
                <div class="speech-bubble">
                    <strong>Nginx队长：</strong>"我的反向代理技能有四大绝招！"
                </div>
            </div>

            <div class="thought-bubble">
                <strong>🎯 反向代理四大绝招：</strong><br>
                <strong>1. 负载均衡术 ⚖️</strong> - 合理分配工作，没人累坏<br>
                <strong>2. 故障转移术 🔄</strong> - 有人生病了，立即找替补<br>
                <strong>3. 缓存加速术 ⚡</strong> - 常用的东西提前准备好<br>
                <strong>4. 安全防护术 🛡️</strong> - 阻挡坏人的攻击
            </div>

            <div class="interactive-demo-comic">
                <h4>🎮 反向代理技能演示</h4>
                <button class="demo-button-comic" onclick="showProxyDemo('balance')">负载均衡演示</button>
                <button class="demo-button-comic" onclick="showProxyDemo('failover')">故障转移演示</button>
                <button class="demo-button-comic" onclick="showProxyDemo('cache')">缓存加速演示</button>
                <div id="proxy-demo-result" class="result-display" style="display: none;"></div>
            </div>
        </div>

        <!-- 第五话：防护技能 -->
        <div class="story-panel" id="story-4">
            <div class="panel-header">
                <div class="panel-number">5</div>
                <h2 class="panel-title">🛡️ 防护技能：网站安全守护</h2>
            </div>

            <div class="character-speech">
                <div class="character-avatar-small">🦸‍♂️</div>
                <div class="speech-bubble">
                    <strong>Nginx队长：</strong>"作为网站守护者，我必须保护网站免受各种威胁！让我展示我的防护技能！"
                </div>
            </div>

            <div class="action-scene">
                <h3>👹 网站世界的反派们</h3>
                <p>在网站世界里，有很多坏蛋想要破坏网站的正常运行...</p>
                <div style="display: flex; justify-content: space-around; flex-wrap: wrap; gap: 15px; margin: 15px 0;">
                    <div style="background: #FFE8E8; border: 3px solid #E74C3C; border-radius: 10px; padding: 15px; text-align: center; flex: 1; min-width: 150px;">
                        <div style="font-size: 2em;">🤖</div>
                        <strong>恶意机器人</strong>
                        <p>疯狂发送请求</p>
                    </div>
                    <div style="background: #FFE8E8; border: 3px solid #E74C3C; border-radius: 10px; padding: 15px; text-align: center; flex: 1; min-width: 150px;">
                        <div style="font-size: 2em;">🕷️</div>
                        <strong>恶意爬虫</strong>
                        <p>偷取网站内容</p>
                    </div>
                    <div style="background: #FFE8E8; border: 3px solid #E74C3C; border-radius: 10px; padding: 15px; text-align: center; flex: 1; min-width: 150px;">
                        <div style="font-size: 2em;">💥</div>
                        <strong>DDoS攻击者</strong>
                        <p>组织大规模攻击</p>
                    </div>
                </div>
            </div>

            <div class="character-speech">
                <div class="character-avatar-small">🦸‍♂️</div>
                <div class="speech-bubble">
                    <strong>Nginx队长：</strong>"不用担心！我有强大的防护技能来对付这些坏蛋！"
                </div>
            </div>

            <div class="comic-diagram">
                <h4>🛡️ Nginx队长的防护技能</h4>
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 15px;">
                    <div style="background: #E8F4FD; border: 3px solid #4A90E2; border-radius: 15px; padding: 20px;">
                        <div style="font-size: 1.5em;">🚪 门禁控制术</div>
                        <p><strong>技能效果：</strong>只允许好人进入</p>
                        <p><strong>防护对象：</strong>恶意IP地址</p>
                        <p><strong>比喻：</strong>小区门卫检查身份证</p>
                    </div>
                    <div style="background: #E8F8E8; border: 3px solid #2ECC71; border-radius: 15px; padding: 20px;">
                        <div style="font-size: 1.5em;">⚡ 限速控制术</div>
                        <p><strong>技能效果：</strong>控制访问频率</p>
                        <p><strong>防护对象：</strong>疯狂请求攻击</p>
                        <p><strong>比喻：</strong>银行ATM使用限制</p>
                    </div>
                    <div style="background: #FFF0E8; border: 3px solid #F39C12; border-radius: 15px; padding: 20px;">
                        <div style="font-size: 1.5em;">🔍 智能识别术</div>
                        <p><strong>技能效果：</strong>识别机器人行为</p>
                        <p><strong>防护对象：</strong>恶意爬虫</p>
                        <p><strong>比喻：</strong>火眼金睛识别坏人</p>
                    </div>
                    <div style="background: #FFE8E8; border: 3px solid #E74C3C; border-radius: 15px; padding: 20px;">
                        <div style="font-size: 1.5em;">🛡️ 群体防护术</div>
                        <p><strong>技能效果：</strong>抵御大规模攻击</p>
                        <p><strong>防护对象：</strong>DDoS攻击</p>
                        <p><strong>比喻：</strong>城墙抵御敌军</p>
                    </div>
                </div>
            </div>

            <div class="thought-bubble">
                <strong>🎯 VIP保护计划：</strong><br>
                不同的用户享受不同级别的服务：<br>
                <strong>👑 VIP用户</strong> - 无限制访问，最高优先级<br>
                <strong>👤 注册用户</strong> - 正常访问限制<br>
                <strong>👻 游客</strong> - 基础访问限制<br>
                <strong>🤖 可疑用户</strong> - 严格限制或拒绝访问
            </div>

            <div class="interactive-demo-comic">
                <h4>⚔️ 防护技能实战演示</h4>
                <button class="demo-button-comic" onclick="showSecurityDemo('attack')">模拟攻击防护</button>
                <button class="demo-button-comic" onclick="showSecurityDemo('limit')">访问限制演示</button>
                <button class="demo-button-comic" onclick="showSecurityDemo('stats')">防护效果统计</button>
                <div id="security-demo-result" class="result-display" style="display: none;"></div>
            </div>

            <div class="character-speech">
                <div class="character-avatar-small">🦸‍♂️</div>
                <div class="speech-bubble">
                    <strong>Nginx队长：</strong>"通过这些防护技能，我成功保护了无数网站的安全！现在你也了解了我的全部能力！"
                </div>
            </div>

            <div class="action-scene">
                <h3>🎓 毕业典礼</h3>
                <p><strong>恭喜你完成了Nginx超级英雄学院的全部课程！</strong></p>
                <p>现在你已经了解了：</p>
                <ul style="margin: 10px 0; padding-left: 20px;">
                    <li>🏠 Nginx队长的超能力起源</li>
                    <li>🤝 超级英雄团队的协作方式</li>
                    <li>📮 HTTP魔法通信的秘密</li>
                    <li>🚦 反向代理的智慧指挥</li>
                    <li>🛡️ 网站安全的防护技能</li>
                </ul>
                <p><strong>你现在也是网站世界的守护者了！</strong></p>
            </div>
        </div>

        <!-- 漫画结尾 -->
        <div style="background: var(--speech-bubble); border: 4px solid #333; border-radius: 20px; padding: 30px; margin: 30px 0; text-align: center; box-shadow: 6px 6px 0px var(--shadow-color);">
            <h2 style="color: var(--hero-blue); font-size: 2em; margin-bottom: 20px;">🎉 故事完结 🎉</h2>
            <p style="font-size: 1.2em; margin-bottom: 15px;">感谢您阅读《Nginx超级英雄学院》！</p>
            <p>希望通过这个有趣的故事，您已经理解了Nginx的核心概念。</p>
            <div style="margin-top: 20px; font-size: 2em;">
                🦸‍♂️ 👩‍💻 👨‍🔧 🗄️
            </div>
            <p style="margin-top: 15px; font-style: italic; color: #666;">
                "技术让世界更美好，学习让我们更强大！"
            </p>
        </div>
    </div>

    <script>
        // 当前故事章节
        let currentStory = 0;
        const totalStories = 5;

        // 显示指定故事
        function showStory(storyIndex) {
            // 隐藏所有故事面板
            const panels = document.querySelectorAll('.story-panel');
            panels.forEach(panel => panel.classList.remove('active'));

            // 显示指定故事
            document.getElementById(`story-${storyIndex}`).classList.add('active');

            // 更新导航按钮状态
            const navBtns = document.querySelectorAll('.nav-button-comic');
            navBtns.forEach(btn => btn.classList.remove('active'));
            navBtns[storyIndex].classList.add('active');

            // 更新进度条
            currentStory = storyIndex;
            updateProgress();

            // 滚动到顶部
            window.scrollTo({ top: 0, behavior: 'smooth' });
        }

        // 更新进度条
        function updateProgress() {
            const progress = ((currentStory + 1) / totalStories) * 100;
            document.getElementById('progress-fill-comic').style.width = progress + '%';
        }

        // 角色选择功能
        function setupCharacterSelection() {
            const characterCards = document.querySelectorAll('.character-card');
            characterCards.forEach(card => {
                card.addEventListener('click', function() {
                    // 移除所有选中状态
                    characterCards.forEach(c => c.classList.remove('selected'));
                    // 添加选中状态
                    this.classList.add('selected');

                    // 获取角色信息
                    const character = this.dataset.character;
                    showCharacterInfo(character);
                });
            });
        }

        // 显示角色信息
        function showCharacterInfo(character) {
            const characterInfo = {
                'nginx': {
                    name: 'Nginx队长',
                    description: '网站世界的超级英雄，拥有高性能、低消耗、高稳定性的超能力！',
                    powers: ['同时处理万个请求', '超低资源消耗', '24小时不间断工作', '智能负载均衡']
                },
                'frontend': {
                    name: '前端小姐',
                    description: '负责用户界面设计的美丽英雄，让用户看到漂亮的网页！',
                    powers: ['美丽界面设计', '用户体验优化', '交互功能实现', '响应式布局']
                },
                'backend': {
                    name: '后端大叔',
                    description: '在后台默默工作的技术专家，处理各种复杂的业务逻辑！',
                    powers: ['业务逻辑处理', '数据计算分析', '安全验证', 'API接口服务']
                },
                'database': {
                    name: '数据库博士',
                    description: '拥有超级记忆力的信息管理专家，存储和管理所有数据！',
                    powers: ['海量数据存储', '快速信息检索', '数据安全保护', '备份恢复']
                }
            };

            const info = characterInfo[character];
            if (info) {
                alert(`🦸‍♂️ ${info.name}\n\n${info.description}\n\n超能力：\n${info.powers.map(power => `• ${power}`).join('\n')}`);
            }
        }

        // 英雄演示功能
        function showHeroDemo(type) {
            const resultDiv = document.getElementById('hero-demo-result');
            resultDiv.style.display = 'block';

            if (type === 'normal') {
                resultDiv.innerHTML = `
                    <div style="background: #FFE8E8; border: 3px solid #E74C3C; border-radius: 15px; padding: 20px;">
                        <h4>😰 普通服务器的一天</h4>
                        <div style="margin: 15px 0;">
                            <div>⏰ 早上9点：第一个用户来了，开始处理...</div>
                            <div>⏰ 早上9:01：第二个用户来了，排队等待...</div>
                            <div>⏰ 早上9:02：第三个用户来了，继续排队...</div>
                            <div>⏰ 早上10点：队伍越来越长，用户开始抱怨...</div>
                            <div>⏰ 中午12点：服务器累坏了，网站崩溃！💥</div>
                        </div>
                        <p style="color: #E74C3C; font-weight: bold;">结果：用户不满意，网站不稳定！</p>
                    </div>
                `;
            } else {
                resultDiv.innerHTML = `
                    <div style="background: #E8F8E8; border: 3px solid #2ECC71; border-radius: 15px; padding: 20px;">
                        <h4>🦸‍♂️ Nginx队长的一天</h4>
                        <div style="margin: 15px 0;">
                            <div>⏰ 早上9点：1000个用户同时来了，没问题！</div>
                            <div>⏰ 早上9:01：又来了2000个用户，轻松应对！</div>
                            <div>⏰ 早上9:02：智能分配，每个用户都很快得到响应！</div>
                            <div>⏰ 全天24小时：持续高效工作，从不疲倦！</div>
                            <div>⏰ 晚上12点：所有用户都满意，网站稳定运行！✨</div>
                        </div>
                        <p style="color: #2ECC71; font-weight: bold;">结果：用户满意，网站高效稳定！</p>
                    </div>
                `;
            }
        }

        // 团队演示功能
        function showTeamDemo(scenario) {
            const resultDiv = document.getElementById('team-demo-result');
            resultDiv.style.display = 'block';

            const scenarios = {
                'login': {
                    title: '🔐 用户登录冒险',
                    steps: [
                        '👩‍💻 前端小姐：展示漂亮的登录界面',
                        '👨‍🔧 后端大叔：验证用户名和密码',
                        '🗄️ 数据库博士：查找用户信息',
                        '🦸‍♂️ Nginx队长：协调整个过程',
                        '✅ 用户成功登录，皆大欢喜！'
                    ]
                },
                'shopping': {
                    title: '🛒 在线购物大冒险',
                    steps: [
                        '👩‍💻 前端小姐：展示商品目录和购物车',
                        '👨‍🔧 后端大叔：计算价格和库存',
                        '🗄️ 数据库博士：更新订单和库存信息',
                        '🦸‍♂️ Nginx队长：确保支付安全',
                        '🎉 购买成功，商品准备发货！'
                    ]
                },
                'video': {
                    title: '🎬 观看视频大冒险',
                    steps: [
                        '👩‍💻 前端小姐：显示视频播放器界面',
                        '👨‍🔧 后端大叔：处理视频流传输',
                        '🗄️ 数据库博士：记录观看历史',
                        '🦸‍♂️ Nginx队长：优化视频加载速度',
                        '🍿 用户愉快观看，没有卡顿！'
                    ]
                }
            };

            const selected = scenarios[scenario];
            resultDiv.innerHTML = `
                <div style="background: linear-gradient(135deg, #E8F4FD 0%, #B3D9FF 100%); border: 3px solid #4A90E2; border-radius: 15px; padding: 20px;">
                    <h4>${selected.title}</h4>
                    <div style="margin: 15px 0;">
                        ${selected.steps.map((step, index) => `
                            <div style="margin: 8px 0; padding: 8px; background: white; border-radius: 8px; border-left: 4px solid #4A90E2;">
                                <strong>第${index + 1}步：</strong> ${step}
                            </div>
                        `).join('')}
                    </div>
                </div>
            `;
        }

        // HTTP演示功能
        function showHttpDemo(type) {
            const resultDiv = document.getElementById('http-demo-result');
            resultDiv.style.display = 'block';

            const demos = {
                'get': {
                    title: '📖 GET魔法演示',
                    content: `
                        <div style="background: #E8F4FD; padding: 15px; border-radius: 10px; margin: 10px 0;">
                            <strong>用户：</strong>"我想看看首页有什么内容"<br>
                            <strong>浏览器：</strong>"GET /index.html HTTP/1.1"<br>
                            <strong>服务器：</strong>"200 OK，这是首页内容！"<br>
                            <strong>结果：</strong>用户看到了漂亮的首页 ✨
                        </div>
                    `
                },
                'post': {
                    title: '💌 POST魔法演示',
                    content: `
                        <div style="background: #E8F8E8; padding: 15px; border-radius: 10px; margin: 10px 0;">
                            <strong>用户：</strong>"我要登录我的账户"<br>
                            <strong>浏览器：</strong>"POST /login，用户名：小明，密码：****"<br>
                            <strong>服务器：</strong>"200 OK，登录成功！"<br>
                            <strong>结果：</strong>用户成功进入个人中心 🎉
                        </div>
                    `
                },
                'error': {
                    title: '❌ 错误处理演示',
                    content: `
                        <div style="background: #FFE8E8; padding: 15px; border-radius: 10px; margin: 10px 0;">
                            <strong>用户：</strong>"我要访问一个不存在的页面"<br>
                            <strong>浏览器：</strong>"GET /nonexistent.html"<br>
                            <strong>服务器：</strong>"404 Not Found，页面不存在！"<br>
                            <strong>结果：</strong>显示友好的404错误页面 🤷‍♂️
                        </div>
                    `
                }
            };

            const selected = demos[type];
            resultDiv.innerHTML = `
                <div style="border: 3px solid #333; border-radius: 15px; padding: 20px;">
                    <h4>${selected.title}</h4>
                    ${selected.content}
                </div>
            `;
        }

        // 反向代理演示功能
        function showProxyDemo(type) {
            const resultDiv = document.getElementById('proxy-demo-result');
            resultDiv.style.display = 'block';

            const demos = {
                'balance': {
                    title: '⚖️ 负载均衡演示',
                    content: `
                        <div style="background: #E8F8E8; border: 3px solid #2ECC71; border-radius: 15px; padding: 20px;">
                            <h4>🦸‍♂️ Nginx队长的智能分配</h4>
                            <div style="margin: 15px 0;">
                                <div style="background: #B3FFB3; padding: 10px; border-radius: 8px; margin: 5px 0;">
                                    📊 服务器1：处理300个请求 (30%) - 运行良好 ✅
                                </div>
                                <div style="background: #B3FFB3; padding: 10px; border-radius: 8px; margin: 5px 0;">
                                    📊 服务器2：处理350个请求 (35%) - 运行良好 ✅
                                </div>
                                <div style="background: #B3FFB3; padding: 10px; border-radius: 8px; margin: 5px 0;">
                                    📊 服务器3：处理350个请求 (35%) - 运行良好 ✅
                                </div>
                            </div>
                            <p style="color: #2ECC71; font-weight: bold;">结果：所有服务器负载均衡，用户体验极佳！</p>
                        </div>
                    `
                },
                'failover': {
                    title: '🔄 故障转移演示',
                    content: `
                        <div style="background: #FFF0E8; border: 3px solid #F39C12; border-radius: 15px; padding: 20px;">
                            <h4>🚨 紧急情况处理</h4>
                            <div style="margin: 15px 0;">
                                <div style="background: #FFE8E8; padding: 10px; border-radius: 8px; margin: 5px 0;">
                                    💥 服务器1：突然故障，无法响应！
                                </div>
                                <div style="background: #E8F4FD; padding: 10px; border-radius: 8px; margin: 5px 0;">
                                    🦸‍♂️ Nginx队长：立即检测到故障！
                                </div>
                                <div style="background: #E8F8E8; padding: 10px; border-radius: 8px; margin: 5px 0;">
                                    🔄 自动转移：所有请求转到服务器2和3
                                </div>
                                <div style="background: #B3FFB3; padding: 10px; border-radius: 8px; margin: 5px 0;">
                                    ✅ 用户完全没有感觉到异常！
                                </div>
                            </div>
                            <p style="color: #F39C12; font-weight: bold;">结果：故障自动处理，服务不中断！</p>
                        </div>
                    `
                },
                'cache': {
                    title: '⚡ 缓存加速演示',
                    content: `
                        <div style="background: #E8F4FD; border: 3px solid #4A90E2; border-radius: 15px; padding: 20px;">
                            <h4>🚀 超级加速技能</h4>
                            <div style="margin: 15px 0;">
                                <div style="background: #B3D9FF; padding: 10px; border-radius: 8px; margin: 5px 0;">
                                    👤 用户A：请求热门图片 → 0.1秒返回 (缓存命中)
                                </div>
                                <div style="background: #B3D9FF; padding: 10px; border-radius: 8px; margin: 5px 0;">
                                    👤 用户B：请求同样图片 → 0.1秒返回 (缓存命中)
                                </div>
                                <div style="background: #FFE5B4; padding: 10px; border-radius: 8px; margin: 5px 0;">
                                    👤 用户C：请求新图片 → 1秒返回 (首次加载并缓存)
                                </div>
                                <div style="background: #B3D9FF; padding: 10px; border-radius: 8px; margin: 5px 0;">
                                    👤 用户D：请求刚才的新图片 → 0.1秒返回 (缓存命中)
                                </div>
                            </div>
                            <p style="color: #4A90E2; font-weight: bold;">结果：响应速度提升10倍，用户体验飞跃！</p>
                        </div>
                    `
                }
            };

            const selected = demos[type];
            resultDiv.innerHTML = selected.content;
        }

        // 安全演示功能
        function showSecurityDemo(type) {
            const resultDiv = document.getElementById('security-demo-result');
            resultDiv.style.display = 'block';

            const demos = {
                'attack': {
                    title: '⚔️ 攻击防护演示',
                    content: `
                        <div style="background: #FFE8E8; border: 3px solid #E74C3C; border-radius: 15px; padding: 20px;">
                            <h4>🛡️ Nginx队长 vs 恶意攻击者</h4>
                            <div style="margin: 15px 0;">
                                <div style="background: #FF6B6B; color: white; padding: 10px; border-radius: 8px; margin: 5px 0;">
                                    👹 恶意攻击者：发起DDoS攻击，每秒10000个请求！
                                </div>
                                <div style="background: #4A90E2; color: white; padding: 10px; border-radius: 8px; margin: 5px 0;">
                                    🦸‍♂️ Nginx队长：检测到异常流量模式！
                                </div>
                                <div style="background: #F39C12; color: white; padding: 10px; border-radius: 8px; margin: 5px 0;">
                                    🛡️ 启动防护：限制可疑IP，每秒最多10个请求
                                </div>
                                <div style="background: #2ECC71; color: white; padding: 10px; border-radius: 8px; margin: 5px 0;">
                                    ✅ 正常用户：完全不受影响，正常访问网站
                                </div>
                            </div>
                            <p style="color: #E74C3C; font-weight: bold;">结果：攻击被成功阻挡，网站安全运行！</p>
                        </div>
                    `
                },
                'limit': {
                    title: '🚦 访问限制演示',
                    content: `
                        <div style="background: #E8F8E8; border: 3px solid #2ECC71; border-radius: 15px; padding: 20px;">
                            <h4>🎯 智能限制策略</h4>
                            <div style="margin: 15px 0;">
                                <div style="background: #FFD700; padding: 10px; border-radius: 8px; margin: 5px 0;">
                                    👑 VIP用户：无限制访问，享受最高优先级
                                </div>
                                <div style="background: #87CEEB; padding: 10px; border-radius: 8px; margin: 5px 0;">
                                    👤 注册用户：每分钟60次请求，正常使用
                                </div>
                                <div style="background: #DDA0DD; padding: 10px; border-radius: 8px; margin: 5px 0;">
                                    👻 游客：每分钟10次请求，基础体验
                                </div>
                                <div style="background: #FFB6C1; padding: 10px; border-radius: 8px; margin: 5px 0;">
                                    🤖 可疑用户：严格限制或直接拒绝
                                </div>
                            </div>
                            <p style="color: #2ECC71; font-weight: bold;">结果：资源合理分配，用户体验分级优化！</p>
                        </div>
                    `
                },
                'stats': {
                    title: '📊 防护效果统计',
                    content: `
                        <div style="background: #2C3E50; color: white; border: 3px solid #34495E; border-radius: 15px; padding: 20px;">
                            <h4>🏆 今日安全战报</h4>
                            <div style="display: flex; justify-content: space-around; flex-wrap: wrap; gap: 15px; margin: 15px 0;">
                                <div style="text-align: center; background: #27AE60; padding: 15px; border-radius: 10px; flex: 1; min-width: 100px;">
                                    <div style="font-size: 2em;">98%</div>
                                    <div>正常请求</div>
                                </div>
                                <div style="text-align: center; background: #F39C12; padding: 15px; border-radius: 10px; flex: 1; min-width: 100px;">
                                    <div style="font-size: 2em;">1.5%</div>
                                    <div>被限制请求</div>
                                </div>
                                <div style="text-align: center; background: #E74C3C; padding: 15px; border-radius: 10px; flex: 1; min-width: 100px;">
                                    <div style="font-size: 2em;">0.5%</div>
                                    <div>被拒绝请求</div>
                                </div>
                            </div>
                            <div style="margin-top: 15px; padding-top: 15px; border-top: 1px solid #34495E;">
                                <p><strong>🚀 性能提升：</strong></p>
                                <p>• 服务器负载降低 40%</p>
                                <p>• 响应速度提升 60%</p>
                                <p>• 安全事件减少 95%</p>
                                <p>• 用户满意度提升 80%</p>
                            </div>
                        </div>
                    `
                }
            };

            const selected = demos[type];
            resultDiv.innerHTML = selected.content;
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            setupCharacterSelection();
            updateProgress();

            // 添加键盘导航
            document.addEventListener('keydown', function(e) {
                if (e.key === 'ArrowLeft' && currentStory > 0) {
                    showStory(currentStory - 1);
                } else if (e.key === 'ArrowRight' && currentStory < totalStories - 1) {
                    showStory(currentStory + 1);
                }
            });

            // 添加随机动画效果
            setInterval(() => {
                const emojis = document.querySelectorAll('.character-avatar, .character-avatar-small');
                emojis.forEach(emoji => {
                    if (Math.random() < 0.1) { // 10%概率
                        emoji.style.transform = 'scale(1.2) rotate(10deg)';
                        setTimeout(() => {
                            emoji.style.transform = 'scale(1) rotate(0deg)';
                        }, 300);
                    }
                });
            }, 2000);
        });
    </script>
</body>
</html>
