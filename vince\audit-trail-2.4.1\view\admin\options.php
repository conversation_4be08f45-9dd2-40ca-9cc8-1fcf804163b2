<?php if (!defined ('ABSPATH')) die (); ?><div class="wrap">
	<?php screen_icon(); ?>
	<h2><?php _e ('Audit Options', 'audit-trail'); ?></h2>

	<?php $this->submenu (true); ?>

	<?php if (isset($saved) && $saved): ?>
	<div class="notice notice-success">
		<p><?php _e('选项已保存。', 'audit-trail'); ?></p>
	</div>
	<?php endif; ?>

	<form action="" method="post" accept-charset="utf-8" style="clear: both" class="audit-options-form">
		<?php wp_nonce_field ('audittrail-update_options'); ?>

		<div class="audit-options-section">
			<h3><?php _e('Actions to monitor', 'audit-trail')?></h3>

			<?php if (count ($methods) > 0) : ?>
				<ul class="options">
					<?php foreach ($methods AS $key => $name) : ?>
					<li><label><input type="checkbox" name="methods[]" value="<?php echo $key ?>"<?php if (in_array ($key, $current)) echo ' checked="checked"' ?>/>
						 <?php echo esc_html( $name ) ?></label>
					</li>
					<?php endforeach; ?>
				</ul>

			<?php else : ?>
			<p><?php _e ('There are no actions to monitor', 'audit-trail'); ?></p>
			<?php endif; ?>
		</div>

		<div class="audit-options-section">
			<h3><?php _e( 'Other Options', 'audit-trail' ); ?></h3>

			<table class="form-table">
				<tr>
					<th><?php _e ('Auto-expire', 'audit-trail'); ?></th>
					<td><input size="5" type="text" name="expiry" value="<?php echo esc_attr( $expiry ) ?>" id="expire"/> <?php _e ('days (0 for no expiry)', 'audit-trail'); ?></td>
				</tr>
				<tr>
					<th><?php _e ('Ignore users', 'audit-trail');?></th>
					<td><input type="text" name="ignore_users" value="<?php echo esc_attr( $ignore_users ) ?>"/> <span class="description"><?php _e( 'separate user IDs with a comma, use 0 to ignore everyone', 'audit-trail'); ?></span></td>
				</tr>
				<tr>
					<th><?php _e ('禁止用户', 'audit-trail');?></th>
					<td>
						<input type="text" name="forbidden_users" id="forbidden_users" value="<?php echo esc_attr( $forbidden_users ) ?>"/> 
						<button type="button" id="refresh_users" class="button"><?php _e('Select All Users', 'audit-trail'); ?></button>
						<p class="description"><?php _e( 'users who cannot use this plugin, separate IDs with a comma', 'audit-trail'); ?></p>
						<div id="user_selection" style="display:none;">
							<select id="available_users" multiple></select>
							<button type="button" id="add_selected_users" class="button"><?php _e('Add Selected Users', 'audit-trail'); ?></button>
						</div>
						<script>
						jQuery(document).ready(function($) {
							$('#refresh_users').click(function() {
								var button = $(this);
								button.prop('disabled', true);
								button.text('<?php _e('Loading...', 'audit-trail'); ?>');
								
								$.ajax({
									url: ajaxurl,
									type: 'POST',
									dataType: 'json',
									data: {
										action: 'at_get_users',
										nonce: '<?php echo wp_create_nonce('audit-trail-get-users'); ?>'
									},
									success: function(response) {
										if (response.success) {
											$('#available_users').empty();
											$.each(response.data, function(index, user) {
												$('#available_users').append('<option value="' + user.ID + '">' + user.user_login + ' (' + user.display_name + ')</option>');
											});
											$('#user_selection').show();
										} else {
											alert('<?php _e('Error loading users', 'audit-trail'); ?>');
										}
										button.prop('disabled', false);
										button.text('<?php _e('Select All Users', 'audit-trail'); ?>');
									},
									error: function() {
										alert('<?php _e('Error loading users', 'audit-trail'); ?>');
										button.prop('disabled', false);
										button.text('<?php _e('Select All Users', 'audit-trail'); ?>');
									}
								});
							});
							
							$('#add_selected_users').click(function() {
								var selectedUsers = $('#available_users').val();
								if (selectedUsers) {
									var currentUsers = $('#forbidden_users').val();
									var userArray = currentUsers ? currentUsers.split(',') : [];
									
									// Add new selected users
									$.each(selectedUsers, function(index, userId) {
										if ($.inArray(userId, userArray) === -1) {
											userArray.push(userId);
										}
									});
									
									// Update the input field
									$('#forbidden_users').val(userArray.join(','));
								}
							});
						});
						</script>
					</td>
				</tr>
				<tr>
					<th><?php _e('登录失败记录日志', 'audit-trail'); ?></th>
					<td>
						<label class="checkbox-label">
							<input type="checkbox" name="error_log"<?php if ($error_log) echo ' checked="checked"' ?>/>
							<?php _e('调用 <code>error_log( \'WordPress Login Failure: &lt;name&gt; from &lt;ip&gt;\' )</code> 如果启用了"用户配置文件&登录"监控，并且登录失败', 'audit-trail'); ?>
						</label>
						<p class="description"><?php _e('实验性功能：可与<a href="http://www.fail2ban.org/">fail2ban</a>结合使用来封禁IP地址', 'audit-trail'); ?></p>
					</td>
				</tr>
			</table>
		</div>

		<p><input class="button-primary" type="submit" name="save" value="<?php esc_attr_e( 'Save Options', 'audit-trail' ); ?>"/></p>
	</form>
</div>
