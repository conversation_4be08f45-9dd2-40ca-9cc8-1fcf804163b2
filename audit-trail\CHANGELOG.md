# 审计插件(Audit Trail)更新日志

## 2.5.15 (2025-05-20)
### 修复
- 修复插件激活时的数据库创建错误
- 修复"Call to undefined method Audit_Trail::ensure_tables_exist()"错误
- 修复过多日志输出到错误日志的问题
- 增强数据库表创建的错误处理和日志记录
- 添加多重备份表创建机制，确保在各种环境下都能成功激活
- 优化内存使用，提高激活成功率

### 新功能
- 添加数据库诊断工具，提供详细的数据库状态报告
- 支持在插件界面中直接检查、修复和优化数据库表
- 添加表结构和索引自动检查功能

### 优化
- 减少不必要的数据库检查，提高页面加载性能
- 减少错误日志输出，只保留关键错误信息
- 改进错误处理和日志记录，便于诊断问题
- 增加内存限制至512MB，确保有足够资源完成激活
- 添加详细的数据库错误信息记录
- 更符合WordPress数据库API标准的表创建过程

## 2.5.14 (2025-05-20)
### 修复
- 修复直接安装新版插件无法激活的问题
- 增强插件激活钩子的错误处理能力
- 优化数据库表创建流程，确保激活时表一定被创建
- 添加额外的数据库存在检查机制

### 优化
- 改进插件初始化流程，减少对数据库的依赖
- 更合理的错误处理和日志记录
- 精简激活过程中不必要的函数调用
- 增强在不同环境下的兼容性

## 2.5.13 (2025-05-20)
### 修复
- 修复"Call to a member function switch_to_locale() on null"错误导致的500错误
- 重构语言设置逻辑，确保WordPress完全加载后再切换语言
- 优化插件初始化流程，避免过早调用WordPress核心函数

### 优化
- 添加对$GLOBALS['wp_locale_switcher']的检查
- 改进语言文件的加载方式
- 将语言加载代码移至plugins_loaded钩子执行
- 增加错误检测和详细日志记录

## 2.5.12 (2025-05-20)
### 修复
- 添加紧急调试模式，精确捕获和记录500错误原因
- 增加紧急恢复模式，通过URL参数debug=1可激活简化界面
- 优化错误处理和输出，提供友好的错误页面而非白屏
- 添加详细的系统信息记录，便于诊断问题

### 优化
- 添加输出缓冲处理，捕获并处理致命错误
- 提供数据库表维护选项，支持用户手动修复数据库问题
- 增加异常详细记录，包括堆栈跟踪信息
- 使用AUDIT_TRAIL_DEBUG常量控制调试模式

## 2.5.11 (2025-05-20)
### 修复
- 修复插件页面500错误问题
- 添加自动修复数据库表结构功能
- 增强插件页面加载的错误处理能力
- 添加详细的错误诊断和报告机制

### 优化
- 优化admin_screen和screen_trail方法，添加异常捕获
- 添加数据库表检查和修复功能
- 改进表格加载过程，提供更友好的错误提示
- 增加数据库索引检查和修复功能

## 2.5.10 (2025-05-20)
### 优化
- 限制错误处理范围，只捕获审计插件自身的错误
- 调整错误处理级别，只记录严重错误而非通知和警告
- 优化错误日志，减少不必要的日志记录，提高系统性能
- 添加插件目录路径常量，用于精确识别插件自身错误

## 2.5.9 (2025-05-20)
### 修复
- 修复`shutdown`钩子回调函数错误，解决`call_user_func_array() expects parameter 1 to be a valid callback`问题
- 修复类名引用错误，`AuditTrailBatchLogger`类无法找到的问题
- 增强插件的错误处理能力，减少对其他插件的影响
- 优化初始化流程，确保插件在各种环境下都能正常加载

## 2.5.8 (2023-10-30)
### 修复
- 修复安装插件后打开插件页面报500错误的问题
- 添加缺失的`install_tables()`方法
- 优化插件初始化流程，增强错误处理能力
- 改进数据库表结构检查和创建逻辑
- 添加内存使用优化，提高插件稳定性

### 优化
- 优化批量日志处理机制，提高高流量站点的性能
- 添加详细的错误日志记录
- 增强对PHP 7.2-8.4版本的兼容性

## 2.5.7 - 2025-04-28
* **安全性增强**: [新增]
  * 增加防爬虫保护机制，自动识别并阻止爬虫访问 [完成]
  * 强化XSS跨站防护，添加安全头部和内容安全策略 [完成]
  * 改进SQL注入防护，全面采用预处理语句 [完成]
  * 增强敏感数据处理，自动识别并掩盖信用卡号、社会安全号等信息 [完成]
  * 添加AJAX请求安全头，防止XSS攻击和响应内容嗅探 [完成]
  * 实现事务处理，确保数据库操作的完整性 [完成]
  * 改进错误处理和日志记录机制 [完成]

## 2.5.6 - 2025-04-27
* **默认配置优化**: [新增]
  * 修改默认监控选项，除"用户页访问"外其余默认勾选 [完成]
  * 默认启用自动清理功能，保留期30天 [完成]
  * 默认启用高流量模式，采样率设为10% [完成]
  * 优化初始安装体验，减少初始配置步骤 [完成]
* **兼容性维护**: [完成]
  * 确保与WordPress 6.4完全兼容 [完成]
  * 优化页面构建器钩子处理机制 [完成]

## 2.5.5 - 2025-04-27
* **PHP兼容性优化**: 增强与PHP 7.4-8.4的兼容性 [完成]
  * 优化Elementor相关钩子处理，添加安全检查防止在Elementor未激活时报错 [完成]
  * 改进JSON数据编码，防止特殊字符导致的JSON编码错误 [完成]
  * 使用try/catch捕获可能的异常，确保插件功能稳定 [完成]
  * 完整支持PHP 7.4到PHP 8.4的各个版本 [完成]
* **数据处理优化**: [完成]
  * 完善敏感数据处理机制，防止数据泄露 [完成]
  * 增强页面构建器集成的错误处理 [完成]
  * 优化数据库操作，提高插件稳定性 [完成]
* **其他改进**: [完成]
  * 更新作者信息和版本号 [完成]
  * 统一数组声明语法，提高代码可读性 [完成]
  * 修复潜在的类型转换问题 [完成]

## 2.5.4 - 2025-04-27 [完成]
* **多站点优化**: 改进当多个WordPress站点使用此插件时的资源使用情况 [完成]
  * 默认启用高流量模式，页面访问日志采样率设为1% [完成]
  * 优化计划任务执行，防止多站点同时运行 [完成]
  * 使用INSERT IGNORE优化数据库插入操作 [完成]
  * 改进静态资源检查逻辑，提升性能 [完成]
* **功能修复**: [完成]
  * 修复按操作类型批量删除功能 [完成]
  * 同步操作类型列表 [完成]
  * 修复SQL查询参数错误 [完成]
  * 增加删除操作的日志记录 [完成]
  * 优化表单处理 [完成]
* **代码优化**: [完成]
  * 改进删除方法 [完成]
  * 分离手动清理功能 [完成]
  * 更新版本号保持一致 [完成]
  * 移除重复的表单处理逻辑，避免冲突 [完成]

## 2.5.3 - 2025-04-26 [完成]
* 新增功能: 支持记录Elementor、Beaver Builder、Divi等页面构建器操作 [完成]
* 优化页面性能: 添加高流量模式，避免页面访问记录过多导致数据库过大 [完成]
* 自动清理机制: 增加自动清理功能，定期清理旧日志 [完成]
* 安全性改进: 加强SQL查询安全性，防止SQL注入 [完成]
* 用户体验优化: 改进搜索和过滤功能 [完成]
* 更新中文翻译文件 [完成]

## 2.5.2 - 2025-04-26 [完成]
* 功能改进: 按操作类型批量删除功能 [完成]
* 优化: 减少不必要的数据库查询 [完成]
* 修复: 日志详情显示问题 [完成]
* 改进: 日志采样算法，降低对高流量站点的负载 [完成]
* 安全性: 增强数据处理安全性 [完成]

## 2.5.1 - 2025-04-25 [完成]
* 修复: 与WordPress 6.3兼容性问题 [完成]
* 添加: 支持新的WordPress钩子 [完成]
* 改进: 日志条目清理流程 [完成]

## 2.5.0 - 2025-04-24 [完成]
* 重要更新: 完全重写内部架构，提高性能 [完成]
* 添加: 全面的中文支持 [完成]
* 新增: 高级过滤系统，便于管理大量日志 [完成]
* 改进: 批量操作功能，简化日志管理 [完成]
* 优化: 前端界面，更清晰的日志显示 [完成]
* 添加: 支持记录页面构建器操作 [完成]

## 2.4.0 - 2015-08-10 [完成]
* 更新: 列表表格以匹配WP 4.3样式 [完成]

## 2.3.1 - 2015-02-22 [完成]
* 修复: 保存文章的错误 [完成]

## 2.3.0 - 2014-05-30 [完成]
* 修复: 使插件兼容WP 3.9 [完成]
* 移除: 对jQuery UI Dialog的依赖 [完成]

## 2.2.1 - 2013-12-15 [完成]
* 修复: 表格中某些项目显示的问题 [完成]

## 2.2.0 - 2013-10-07 [完成]
* 添加: 显示条目相对于当前时间发生的时间 [完成]
* 更改: 外观匹配最新的WP样式 [完成]
* 修复: 日期排序问题 [完成]

## 2.1.0 - 2012-05-15 [完成]
* 添加: 搜索功能 [完成]
* 删除: 对WP < 2.8的支持 [完成]
* 添加: 记录自身操作的功能 [完成]

## 2.0.4 - 2011-08-20 [完成]
* 修复: 由于未定义函数导致的激活错误 [完成]
* 更新: 插件使用WP2.8表格 [完成]

## 2.0.3 - 2011-03-10 [完成]
* 修复: 登录跟踪 [完成]
* 修复: 自动过期选项 [完成]
* 修复: IE7中的选项对话框 [完成]

## 2.0.2 - 2010-11-05 [完成]
* 添加: 清除所有条目的按钮 [完成]

## 2.0.1 - 2010-07-18 [完成]
* 改进: 'happened_at'值的格式 [完成]
* 修复: 返回审核页面时记住屏幕选项 [完成]

## 2.0.0 - 2010-04-02 [完成]
* 完全重写: 使用自定义管理表格类(需要WP 2.5) [完成]
* 修复: 在WP 2.7中的显示问题 [完成]
* 改进: 列宽的计算 [完成]

## 1.0.8 - 2009-12-10 [完成]
* 修复: 在WP 2.7中的显示问题 [完成]

## 1.0.7 - 2009-08-15 [完成]
* 修复: 批量升级的问题 [完成]

## 1.0.6 - 2009-03-20 [完成]
* 更新: 支持WP 2.5 [完成]

## 1.0.5 - 2008-11-05 [完成]
* 修复: getUserName中的错误 [完成]
* 添加: WP-Cron的钩子 [完成]

## 1.0.4 - 2008-07-22 [完成]
* 添加: 指定用户ID的过滤器 [完成]
* 添加: 更多监控函数 [完成]
* 添加: 审核项目查看时间的记录 [完成]

## 1.0.3 - 2008-04-15 [完成]
* 添加: 翻译支持 [完成]
* 更改: 从美式格式改为国际时间显示 [完成]
* 添加: 'post-status-changed'事件 [完成]

## 1.0.2 - 2008-02-10 [完成]
* 移除: 一些调试代码 [完成]
* 修复: 几个过滤器 [完成]

## 1.0.1 - 2008-01-20 [完成]
* 替换: 使用json替代serialize以便更容易互操作 [完成]
* 改进: 显示消息的过滤 [完成]
* 添加: 记住显示过滤器 [完成]
* 显示: 登录用户信息 [完成]

## 1.0.0 - 2007-12-15 [完成]
* 重写: 数据库审核功能 [完成] 