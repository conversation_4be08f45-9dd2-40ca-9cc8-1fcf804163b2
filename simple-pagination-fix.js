/**
 * 自动新闻分页系统 - 自动获取已发布新闻并进行真实分页
 */

jQuery(document).ready(function($) {
    // 等待页面完全加载
    setTimeout(function() {
        initAutoNewsPagination();
    }, 1000);

    function initAutoNewsPagination() {
        // 查找现有的分页容器
        const paginationContainer = $('.aobailei-news-blog .pagination, .fusion-blog-pagination, .page-numbers');
        const newsContainer = $('.aobailei-news-blog');

        if (newsContainer.length > 0) {
            // 隐藏原有分页
            paginationContainer.hide();

            // 获取当前页面信息并初始化
            loadNewsData(1); // 从第一页开始加载
        }
    }

    // 加载新闻数据
    function loadNewsData(page) {
        const newsContainer = $('.aobailei-news-blog');

        // 显示加载指示器
        showLoadingIndicator();

        // 发送AJAX请求获取新闻数据
        $.ajax({
            url: ajaxurl || '/wp-admin/admin-ajax.php',
            type: 'POST',
            data: {
                action: 'get_published_news',
                page: page,
                posts_per_page: 6,
                category: 'news', // 可以根据需要修改
                nonce: $('#news_nonce').val() || 'default_nonce'
            },
            success: function(response) {
                if (response.success) {
                    // 更新新闻内容
                    updateNewsContent(response.data.posts);

                    // 创建并显示分页
                    createAndShowPagination(page, response.data.total_pages, response.data.total_posts);
                } else {
                    // 如果AJAX失败，使用静态分页
                    createStaticPagination();
                }
            },
            error: function() {
                // 如果AJAX失败，使用静态分页
                createStaticPagination();
            },
            complete: function() {
                hideLoadingIndicator();
            }
        });
    }

    // 更新新闻内容
    function updateNewsContent(posts) {
        const newsContainer = $('.aobailei-news-blog .news-grid-container, .aobailei-news-blog');

        if (posts && posts.length > 0) {
            let html = '';
            posts.forEach(function(post) {
                html += createNewsItemHTML(post);
            });

            // 如果有网格容器，更新网格容器，否则更新主容器
            if (newsContainer.find('.news-grid-container').length > 0) {
                newsContainer.find('.news-grid-container').html(html);
            } else {
                // 创建网格容器
                newsContainer.html('<div class="news-grid-container">' + html + '</div>');
            }
        } else {
            newsContainer.html('<p class="no-news-found">暂无新闻文章</p>');
        }
    }

    // 创建新闻项目HTML
    function createNewsItemHTML(post) {
        const thumbnail = post.thumbnail || '';
        const excerpt = post.excerpt || '';
        const date = post.date || '';
        const author = post.author || '';

        return `
            <div class="news-item">
                ${thumbnail ? `
                    <div class="news-thumbnail">
                        <a href="${post.permalink}">
                            ${thumbnail}
                        </a>
                    </div>
                ` : ''}

                <div class="news-content">
                    <h3 class="news-title">
                        <a href="${post.permalink}">${post.title}</a>
                    </h3>

                    <div class="news-meta">
                        <span class="news-date">${date}</span>
                        ${author ? `<span class="news-author">作者: ${author}</span>` : ''}
                    </div>

                    ${excerpt ? `
                        <div class="news-excerpt">
                            ${excerpt}
                        </div>
                    ` : ''}

                    <div class="news-read-more">
                        <a href="${post.permalink}" class="read-more-btn">阅读更多</a>
                    </div>
                </div>
            </div>
        `;
    }

    // 创建并显示分页
    function createAndShowPagination(currentPage, totalPages, totalPosts) {
        const newsContainer = $('.aobailei-news-blog');

        // 移除现有的自定义分页
        $('.custom-pagination-wrapper').remove();

        if (totalPages > 1) {
            const paginationHTML = createCustomPagination(currentPage, totalPages, totalPosts);
            newsContainer.after(paginationHTML);

            // 绑定分页事件
            bindPaginationEvents();
        }
    }

    // 创建静态分页（当AJAX失败时使用）
    function createStaticPagination() {
        const currentPage = getCurrentPage();
        const totalPosts = 180; // 默认值
        const totalPages = Math.ceil(totalPosts / 6);

        createAndShowPagination(currentPage, totalPages, totalPosts);
    }

    function createCustomPagination(currentPage, totalPages, totalPosts) {
        let html = `
            <div class="custom-pagination-wrapper" style="
                display: flex;
                align-items: center;
                justify-content: center;
                gap: 15px;
                margin: 40px 0;
                padding: 20px;
                background: #f8f9fa;
                border-radius: 8px;
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            ">
                <div class="pagination-info" style="
                    color: #666;
                    font-size: 14px;
                    margin-right: 20px;
                ">
                    总共 <span class="total-posts-count">${totalPosts}</span> 条记录
                </div>

                <div class="pagination-buttons" style="
                    display: flex;
                    align-items: center;
                    gap: 5px;
                ">
        `;

        // 上一页按钮
        const prevDisabled = currentPage <= 1;
        html += `
            <a href="#" data-page="${Math.max(1, currentPage - 1)}"
               class="pagination-btn prev-btn ${prevDisabled ? 'disabled' : ''}"
               style="
                   display: inline-flex;
                   align-items: center;
                   justify-content: center;
                   min-width: 32px;
                   height: 32px;
                   padding: 0 8px;
                   border: 1px solid #ddd;
                   background: ${prevDisabled ? '#f5f5f5' : 'white'};
                   color: ${prevDisabled ? '#ccc' : '#333'};
                   text-decoration: none;
                   border-radius: 4px;
                   font-size: 14px;
                   cursor: ${prevDisabled ? 'not-allowed' : 'pointer'};
               ">‹</a>
        `;

        // 页码按钮
        const pageNumbers = getVisiblePageNumbers(currentPage, totalPages);
        pageNumbers.forEach(page => {
            if (page === '...') {
                html += `<span style="padding: 0 8px; color: #999; font-size: 14px;">...</span>`;
            } else {
                const isCurrent = page === currentPage;
                html += `
                    <a href="#" data-page="${page}"
                       class="pagination-btn ${isCurrent ? 'current' : ''}"
                       style="
                           display: inline-flex;
                           align-items: center;
                           justify-content: center;
                           min-width: 32px;
                           height: 32px;
                           padding: 0 8px;
                           border: 1px solid ${isCurrent ? '#007cba' : '#ddd'};
                           background: ${isCurrent ? '#007cba' : 'white'};
                           color: ${isCurrent ? 'white' : '#333'};
                           text-decoration: none;
                           border-radius: 4px;
                           font-size: 14px;
                           cursor: pointer;
                       ">${page}</a>
                `;
            }
        });

        // 下一页按钮
        const nextDisabled = currentPage >= totalPages;
        html += `
            <a href="#" data-page="${Math.min(totalPages, currentPage + 1)}"
               class="pagination-btn next-btn ${nextDisabled ? 'disabled' : ''}"
               style="
                   display: inline-flex;
                   align-items: center;
                   justify-content: center;
                   min-width: 32px;
                   height: 32px;
                   padding: 0 8px;
                   border: 1px solid #ddd;
                   background: ${nextDisabled ? '#f5f5f5' : 'white'};
                   color: ${nextDisabled ? '#ccc' : '#333'};
                   text-decoration: none;
                   border-radius: 4px;
                   font-size: 14px;
                   cursor: ${nextDisabled ? 'not-allowed' : 'pointer'};
               ">›</a>
        `;

        html += `
                </div>

                <div class="goto-page" style="
                    display: flex;
                    align-items: center;
                    gap: 8px;
                    margin-left: 20px;
                ">
                    <label style="color: #666; font-size: 14px;">前往</label>
                    <input type="number" min="1" max="${totalPages}" value="${currentPage}"
                           class="goto-input" style="
                               width: 50px;
                               height: 32px;
                               padding: 0 8px;
                               border: 1px solid #ddd;
                               border-radius: 4px;
                               text-align: center;
                               font-size: 14px;
                           ">
                    <label style="color: #666; font-size: 14px;">页</label>
                    <button class="goto-btn" style="
                        height: 32px;
                        padding: 0 12px;
                        background: #007cba;
                        color: white;
                        border: none;
                        border-radius: 4px;
                        font-size: 14px;
                        cursor: pointer;
                    ">确定</button>
                </div>
            </div>
        `;

        return html;
    }

    // 获取当前页码
    function getCurrentPage() {
        const urlParams = new URLSearchParams(window.location.search);
        return parseInt(urlParams.get('paged')) || 1;
    }

    // 显示加载指示器
    function showLoadingIndicator() {
        const newsContainer = $('.aobailei-news-blog');
        if (!$('.loading-indicator').length) {
            newsContainer.append('<div class="loading-indicator" style="text-align: center; padding: 20px; color: #666;">正在加载...</div>');
        }
        $('.loading-indicator').show();
    }

    // 隐藏加载指示器
    function hideLoadingIndicator() {
        $('.loading-indicator').hide();
    }
    
    function getVisiblePageNumbers(currentPage, totalPages) {
        const pages = [];
        const maxVisible = 7;
        const half = Math.floor(maxVisible / 2);
        
        let start = Math.max(1, currentPage - half);
        let end = Math.min(totalPages, currentPage + half);
        
        // 调整范围
        if (end - start + 1 < maxVisible) {
            if (start === 1) {
                end = Math.min(totalPages, start + maxVisible - 1);
            } else {
                start = Math.max(1, end - maxVisible + 1);
            }
        }
        
        // 添加第一页和省略号
        if (start > 1) {
            pages.push(1);
            if (start > 2) {
                pages.push('...');
            }
        }
        
        // 添加中间页码
        for (let i = start; i <= end; i++) {
            pages.push(i);
        }
        
        // 添加省略号和最后一页
        if (end < totalPages) {
            if (end < totalPages - 1) {
                pages.push('...');
            }
            pages.push(totalPages);
        }
        
        return pages;
    }
    
    function getPageUrl(page) {
        if (page <= 1) {
            // 第一页，移除paged参数
            const url = new URL(window.location);
            url.searchParams.delete('paged');
            return url.toString();
        } else {
            // 其他页面，设置paged参数
            const url = new URL(window.location);
            url.searchParams.set('paged', page);
            return url.toString();
        }
    }
    
    function bindPaginationEvents() {
        // 前往页面按钮事件
        $(document).on('click', '.goto-btn', function() {
            const input = $(this).siblings('.goto-input');
            const page = parseInt(input.val());
            const totalPages = parseInt(input.attr('max'));
            
            if (page >= 1 && page <= totalPages) {
                window.location.href = getPageUrl(page);
            } else {
                alert('请输入有效的页码');
            }
        });
        
        // 回车键支持
        $(document).on('keypress', '.goto-input', function(e) {
            if (e.which === 13) {
                $(this).siblings('.goto-btn').click();
            }
        });
        
        // 悬停效果
        $(document).on('mouseenter', '.pagination-btn:not(.disabled)', function() {
            if (!$(this).hasClass('current')) {
                $(this).css({
                    'background': '#f0f0f0',
                    'border-color': '#ccc'
                });
            }
        }).on('mouseleave', '.pagination-btn:not(.disabled)', function() {
            if (!$(this).hasClass('current')) {
                $(this).css({
                    'background': 'white',
                    'border-color': '#ddd'
                });
            }
        });
        
        $(document).on('mouseenter', '.goto-btn', function() {
            $(this).css('background', '#005a87');
        }).on('mouseleave', '.goto-btn', function() {
            $(this).css('background', '#007cba');
        });
    }
});
