#!/bin/bash
#
# Fail2Ban 管理脚本 - 轻量级版本
# 功能：安装配置fail2ban，管理站点配置，监控IP封禁
#

# 颜色和样式定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
WHITE='\033[1;37m'
BG_RED='\033[41m'
BG_GREEN='\033[42m'
BG_YELLOW='\033[43m'
BG_BLUE='\033[44m'
BOLD='\033[1m'
NC='\033[0m'

# 配置文件
CONFIG_FILE="/etc/fail2ban/jail.local"
FILTER_DIR="/etc/fail2ban/filter.d"
ACTION_DIR="/etc/fail2ban/action.d"
LOG_DIR="/var/log/nginx"
UFW_LOG="/root/ufw.log"

# 系统信息和防火墙类型
OS_TYPE=""
FIREWALL_TYPE=""

# 图标定义
CHECK_ICON=" ✓ "
CROSS_ICON=" ✗ "
ARROW_ICON="→"
WARNING_ICON="⚠"
INFO_ICON="ℹ"
LOCK_ICON=""
UNLOCK_ICON=""
CONFIG_ICON="⚙"
SHIELD_ICON="️"

# 检查是否为root用户
if [ "$EUID" -ne 0 ]; then 
    echo -e "${RED}请使用root权限运行此脚本${NC}"
    exit 1
fi

# 检测系统类型（简化版）
detect_os() {
    # 尝试从os-release获取信息
    if [ -f /etc/os-release ]; then
        . /etc/os-release
        OS_TYPE="${ID,,}" # 转为小写
        OS_VERSION="$VERSION_ID"
        echo -e "${BLUE}检测到系统: $NAME $VERSION${NC}"
        return 0
    fi
    
    # 尝试lsb-release
    if [ -f /etc/lsb-release ]; then
        . /etc/lsb-release
        OS_TYPE="${DISTRIB_ID,,}" # 转为小写
        OS_VERSION="$DISTRIB_RELEASE"
        echo -e "${BLUE}检测到系统: $DISTRIB_DESCRIPTION${NC}"
        return 0
    fi
    
    # 尝试根据发行版特定文件判断
    if [ -f /etc/redhat-release ]; then
        OS_TYPE="rhel"
        echo -e "${BLUE}检测到Red Hat系统${NC}"
        return 0
    fi
    
    # 默认情况
    OS_TYPE="unknown"
    echo -e "${YELLOW}无法确定系统类型，尝试通用配置${NC}"
    return 1
}

# 检测防火墙类型（简化版）
detect_firewall() {
    # 检查各类防火墙
    local firewalls=("ufw" "firewalld" "iptables")
    
    for fw in "${firewalls[@]}"; do
        if command_exists "$fw"; then
            FIREWALL_TYPE="$fw"
            echo -e "${BLUE}检测到防火墙: ${fw^}${NC}" # 首字母大写
            return 0
        fi
    done
    
    # 默认情况
    FIREWALL_TYPE="none"
    echo -e "${YELLOW}未检测到支持的防火墙，部分功能可能受限${NC}"
    return 1
}

# 调整配置基于系统类型（简化版）
adjust_config() {
    # 根据系统类型设置日志路径
    case "$OS_TYPE" in
        ubuntu|debian|kali)
            LOG_DIR="/var/log/nginx"
            ;;
        centos|rhel|fedora|rocky|almalinux)
            LOG_DIR="/var/log/nginx"
            # 检查SELinux
            [ "$(command -v getenforce && getenforce)" = "Enforcing" ] && \
                echo -e "${YELLOW}检测到SELinux启用状态，可能需要额外配置${NC}"
            ;;
        *)
            # 尝试查找nginx日志目录
            for dir in "/var/log/nginx" "/usr/local/nginx/logs"; do
                [ -d "$dir" ] && LOG_DIR="$dir" && break
            done
            ;;
    esac
    
    echo -e "${BLUE}使用日志目录: $LOG_DIR${NC}"
}

# 检查目录存在
check_directories() {
    for dir in "$FILTER_DIR" "$ACTION_DIR" "$LOG_DIR"; do
        if [ ! -d "$dir" ]; then
            mkdir -p "$dir"
            echo -e "${YELLOW}已创建目录: $dir${NC}"
        fi
    done
}

# 判断命令是否存在
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# 检查包是否已安装
package_installed() {
    local pkg=$1
    
    if command_exists apt-get; then
        dpkg -l | grep -q "ii  $pkg " && return 0
    elif command_exists yum || command_exists dnf; then
        rpm -q "$pkg" >/dev/null 2>&1 && return 0
    fi
    
    return 1
}

# 安装软件包
install_packages() {
    local required_packages=("fail2ban")
    local firewall_pkg=""
    
    # 确定合适的防火墙软件包
    case "$FIREWALL_TYPE" in
        "ufw")
            firewall_pkg="ufw"
            required_packages+=("ufw")
            ;;
        "firewalld")
            firewall_pkg="firewalld"
            required_packages+=("firewalld")
            ;;
        "iptables")
            firewall_pkg="iptables"
            required_packages+=("iptables")
            ;;
        "none")
            # 根据系统选择默认防火墙
            case "$OS_TYPE" in
                "ubuntu"|"debian"|"kali")
                    firewall_pkg="ufw"
                    required_packages+=("ufw")
                    ;;
                "centos"|"rhel"|"fedora"|"rocky"|"almalinux")
                    firewall_pkg="firewalld"
                    required_packages+=("firewalld")
                    ;;
                *)
                    firewall_pkg="ufw"
                    required_packages+=("ufw")
                    ;;
            esac
            ;;
    esac
    
    # 添加jq包
    required_packages+=("jq")
    
    # 检查是否已安装所需软件
    local packages_to_install=()
    for pkg in "${required_packages[@]}"; do
        if ! package_installed "$pkg"; then
            packages_to_install+=("$pkg")
        fi
    done
    
    # 如果所有软件都已安装，直接返回
    if [ ${#packages_to_install[@]} -eq 0 ]; then
        echo -e "${GREEN}所有必需的软件包已安装${NC}"
        return 0
    fi
    
    # 根据系统类型安装缺失的软件包
    if command_exists apt-get; then
        echo -e "${GREEN}使用apt安装缺失的软件包: ${packages_to_install[*]}${NC}"
        apt-get update
        apt-get install -y "${packages_to_install[@]}"
    elif command_exists dnf; then
        echo -e "${GREEN}使用dnf安装缺失的软件包: ${packages_to_install[*]}${NC}"
        dnf install -y epel-release
        dnf install -y "${packages_to_install[@]}"
    elif command_exists yum; then
        echo -e "${GREEN}使用yum安装缺失的软件包: ${packages_to_install[*]}${NC}"
        yum install -y epel-release
        yum install -y "${packages_to_install[@]}"
    else
        echo -e "${RED}不支持的系统类型${NC}"
        exit 1
    fi

    # 验证安装
    local install_failed=0
    for pkg in "${packages_to_install[@]}"; do
        if ! package_installed "$pkg"; then
            echo -e "${RED}安装 $pkg 失败${NC}"
            install_failed=1
        fi
    done
    
    if [ $install_failed -eq 1 ]; then
        echo -e "${YELLOW}警告: 某些软件包安装失败，但继续执行脚本${NC}"
    fi
    
    # 更新FIREWALL_TYPE，以防安装了新的防火墙
    detect_firewall
}

# 配置防火墙
configure_firewall() {
    echo -e "${GREEN}配置防火墙: $FIREWALL_TYPE${NC}"
    
    # 检查防火墙是否存在
    case "$FIREWALL_TYPE" in
        "ufw")
            if ! command_exists ufw; then
                echo -e "${RED}UFW未安装，请先安装UFW${NC}"
                return 1
            fi
            ;;
        "firewalld")
            if ! command_exists firewall-cmd; then
                echo -e "${RED}FirewallD未安装，请先安装FirewallD${NC}"
                return 1
            fi
            ;;
        "iptables")
            if ! command_exists iptables; then
                echo -e "${RED}iptables未安装，请先安装iptables${NC}"
                return 1
            fi
            ;;
        "none")
            echo -e "${YELLOW}未检测到支持的防火墙，跳过防火墙配置${NC}"
            return 0
            ;;
    esac
    
    # 获取非本地监听端口
    local ports_tcp ports_udp
    get_listening_ports ports_tcp ports_udp
    
    # 根据防火墙类型执行配置
    case "$FIREWALL_TYPE" in
        "ufw")
            # 重置并启用UFW
            ufw --force reset
            ufw --force enable
            ufw default deny incoming
            ufw default allow outgoing
            
            # 允许SSH
            ufw allow 22/tcp comment 'SSH'
            
            # 添加端口规则
            for port in $ports_tcp; do
                ufw allow $port/tcp comment "Port $port TCP"
            done
            for port in $ports_udp; do
                ufw allow $port/udp comment "Port $port UDP"
            done
            
            # 显示规则
            ufw status numbered
            ;;
        "firewalld")
            # 启动服务
            systemctl start firewalld
            systemctl enable firewalld
            
            # 允许SSH
            firewall-cmd --permanent --add-service=ssh
            
            # 添加端口规则
            for port in $ports_tcp; do
                firewall-cmd --permanent --add-port=$port/tcp
            done
            for port in $ports_udp; do
                firewall-cmd --permanent --add-port=$port/udp
            done
            
            # 应用规则
            firewall-cmd --reload
            firewall-cmd --list-all
            ;;
        "iptables")
            # 设置基本规则
            iptables -F
            iptables -P INPUT DROP
            iptables -P FORWARD DROP
            iptables -P OUTPUT ACCEPT
            iptables -A INPUT -i lo -j ACCEPT
            iptables -A INPUT -m conntrack --ctstate ESTABLISHED,RELATED -j ACCEPT
            iptables -A INPUT -p tcp --dport 22 -j ACCEPT
            
            # 添加端口规则
            for port in $ports_tcp; do
                iptables -A INPUT -p tcp --dport "$port" -j ACCEPT
            done
            for port in $ports_udp; do
                iptables -A INPUT -p udp --dport "$port" -j ACCEPT
            done
            
            # 保存规则
            save_iptables_rules
            
            # 显示规则
            iptables -L -v
            ;;
    esac
    
    return 0
}

# 获取监听端口
get_listening_ports() {
    local ports_tcp_var=$1
    local ports_udp_var=$2
    local cmd="ss"
    
    # 检查命令
    if ! command_exists ss; then
        if command_exists netstat; then
            cmd="netstat"
        else
            echo -e "${RED}未找到网络状态命令(ss/netstat)${NC}"
            return 1
        fi
    fi
    
    # 获取TCP端口
    if [ "$cmd" = "ss" ]; then
        local tcp_ports=$(ss -tuln | grep LISTEN | grep -v "127.0.0.1" | grep -v "::1" | grep "tcp" | awk '{print $5}' | awk -F: '{print $NF}' | sort -u | grep -E '^[0-9]+$')
        local udp_ports=$(ss -tuln | grep -v "127.0.0.1" | grep -v "::1" | grep "udp" | awk '{print $5}' | awk -F: '{print $NF}' | sort -u | grep -E '^[0-9]+$')
    else
        local tcp_ports=$(netstat -tuln | grep LISTEN | grep -v "127.0.0.1" | grep -v "::1" | grep "tcp" | awk '{print $4}' | awk -F: '{print $NF}' | sort -u | grep -E '^[0-9]+$')
        local udp_ports=$(netstat -tuln | grep -v "127.0.0.1" | grep -v "::1" | grep "udp" | awk '{print $4}' | awk -F: '{print $NF}' | sort -u | grep -E '^[0-9]+$')
    fi
    
    # 设置返回值
    eval "$ports_tcp_var=\"$tcp_ports\""
    eval "$ports_udp_var=\"$udp_ports\""
    return 0
}

# 保存iptables规则
save_iptables_rules() {
    if command_exists iptables-save; then
        case "$OS_TYPE" in
            "ubuntu"|"debian"|"kali")
                iptables-save > /etc/iptables/rules.v4
                ;;
            "centos"|"rhel"|"fedora"|"rocky"|"almalinux")
                iptables-save > /etc/sysconfig/iptables
                ;;
            *)
                local iptables_rules="/etc/iptables/rules.v4"
                mkdir -p "$(dirname "$iptables_rules")"
                iptables-save > "$iptables_rules"
                echo -e "${YELLOW}规则已保存到 $iptables_rules，但可能需要额外配置以在启动时加载${NC}"
                ;;
        esac
    else
        echo -e "${YELLOW}无法找到 iptables-save 命令，规则可能在重启后丢失${NC}"
    fi
}

# 创建对应防火墙的action配置
create_firewall_action() {
    case "$FIREWALL_TYPE" in
        "ufw")
            cat > "$ACTION_DIR/ufw-comment.conf" << 'EOF'
[Definition]
actionstart = 
actionstop = 
actioncheck = 

actionban = ufw insert 1 deny from <ip> to any port <port> proto <protocol> comment 'fail2ban: <name> - banned on <datetime>' >> /root/ufw.log 2>&1 && /usr/local/bin/fail2ban-notify.sh <ip> <name> "封禁"
            
actionunban = ufw delete deny from <ip> to any port <port> proto <protocol> >> /root/ufw.log 2>&1  && /usr/local/bin/fail2ban-notify.sh <ip> <name> "解封" 

[Init]
EOF
            ;;
        "firewalld")
            cat > "$ACTION_DIR/firewalld-multiport.conf" << 'EOF'
[Definition]
actionstart = firewall-cmd --direct --add-chain ipv4 filter fail2ban-<name>
              firewall-cmd --direct --add-rule ipv4 filter INPUT_direct 0 -m state --state NEW -j fail2ban-<name>

actionstop = firewall-cmd --direct --remove-rule ipv4 filter INPUT_direct 0 -m state --state NEW -j fail2ban-<name>
             firewall-cmd --direct --remove-chain ipv4 filter fail2ban-<name>

actioncheck = firewall-cmd --direct --get-chains ipv4 filter | grep -q 'fail2ban-<name>'

actionban = firewall-cmd --direct --add-rule ipv4 filter fail2ban-<name> 0 -s <ip> -j REJECT && /usr/local/bin/fail2ban-notify.sh <ip> <name> "封禁"
            
actionunban = firewall-cmd --direct --remove-rule ipv4 filter fail2ban-<name> 0 -s <ip> -j REJECT && /usr/local/bin/fail2ban-notify.sh <ip> <name> "解封"

[Init]
EOF
            ;;
        "iptables")
            cat > "$ACTION_DIR/iptables-multiport.conf" << 'EOF'
[Definition]
actionstart = iptables -N fail2ban-<name>
              iptables -A fail2ban-<name> -j RETURN
              iptables -I INPUT -p <protocol> -m multiport --dports <port> -j fail2ban-<name>

actionstop = iptables -D INPUT -p <protocol> -m multiport --dports <port> -j fail2ban-<name>
             iptables -F fail2ban-<name>
             iptables -X fail2ban-<name>

actioncheck = iptables -n -L fail2ban-<name> >/dev/null 2>&1

actionban = iptables -I fail2ban-<name> 1 -s <ip> -j DROP && /usr/local/bin/fail2ban-notify.sh <ip> <name> "封禁"
            
actionunban = iptables -D fail2ban-<name> -s <ip> -j DROP && /usr/local/bin/fail2ban-notify.sh <ip> <name> "解封"

[Init]
name = default
port = ssh
protocol = tcp
EOF
            ;;
        *)
            echo -e "${YELLOW}未检测到支持的防火墙，使用空操作动作${NC}"
            cat > "$ACTION_DIR/dummy.conf" << 'EOF'
[Definition]
actionstart = 
actionstop = 
actioncheck = 

actionban = echo "封禁 <ip> (<name>) 于 <datetime>" >> /var/log/fail2ban-dummy.log && /usr/local/bin/fail2ban-notify.sh <ip> <name> "封禁"
            
actionunban = echo "解封 <ip> (<name>) 于 $(date '+%%Y-%%m-%%d %%H:%%M:%%S')" >> /var/log/fail2ban-dummy.log && /usr/local/bin/fail2ban-notify.sh <ip> <name> "解封"

[Init]
EOF
            ;;
    esac
}

# 创建fail2ban过滤器配置
create_filters() {
    check_directories
    
    # nginx-req-limit.conf
    cat > "$FILTER_DIR/nginx-req-limit.conf" << 'EOF'
[Definition]
failregex = ^<HOST> .* "(GET|POST|HEAD).*HTTP.*" (?:404|444|403|400|429|502) .*$
           ^<HOST> -.*- .*HTTP/1.* .* .*$
ignoreregex =
EOF

    # 创建对应防火墙的action配置
    create_firewall_action

    # 检查fail2ban-notify.sh是否存在，如果不存在则创建
    local notify_script="/usr/local/bin/fail2ban-notify.sh"
    if [ ! -f "$notify_script" ]; then
        cat > "$notify_script" << 'EOF'
#!/bin/bash
# 封禁/解封通知脚本
# 用法: fail2ban-notify.sh <ip> <jail名称> <动作>

IP="$1"
JAIL="$2"
ACTION="$3"

# 这里可以添加通知逻辑，比如发送邮件、推送通知等
echo "$(date '+%Y-%m-%d %H:%M:%S') - $IP 已被 $JAIL $ACTION" >> /var/log/fail2ban-actions.log
EOF
        chmod +x "$notify_script"
        echo -e "${GREEN}已创建通知脚本: $notify_script${NC}"
    fi
}

# 创建基础jail.local配置
create_base_jail() {
    # 确定默认ban action
    local ban_action="ufw-comment"
    case "$FIREWALL_TYPE" in
        "firewalld")
            ban_action="firewalld-multiport"
            ;;
        "iptables")
            ban_action="iptables-multiport"
            ;;
        "none")
            ban_action="dummy"
            ;;
    esac

    cat > "$CONFIG_FILE" << EOF
[DEFAULT]
ignoreip = *************,*************,*************,************,*************,**************,**************,*************,*************,************,*************,**************,*************,**************,**************,*************,*************,*************,***********,************,*************,************,************,*************,************,**************,***********,***********,**************,************,**************,**************,**************,************,*************,************,*************,**************,*************,*************,*************,*************,*************,*************,**************,************,**************,*************,*************,*************,**************,**************,************,*************,*************,***************,*************,************,*************,************,*************,*************,**************,*************,**************,*************,**************,*************,*************,**************,*************4,103.146.63.42,103.152.118.219,103.152.118.72,103.164.203.163,103.72.163.222,103.75.117.169,104.244.77.37,108.181.1.235,108.61.158.223,108.61.200.94,109.248.43.195,135.148.120.32,136.243.106.228,139.84.230.39,141.164.38.65,145.239.252.65,146.88.239.197,147.78.0.165,147.78.3.13,147.78.3.161,149.28.136.245,149.28.47.113,149.28.85.239,152.53.36.14,152.53.38.14,154.205.144.192,155.138.221.81,156.67.218.140,157.90.154.114,158.51.123.249,162.254.117.80,162.254.118.29,163.182.174.161,163.47.21.168,164.52.202.100,167.71.185.204,167.88.61.211,170.249.218.98,176.9.114.118,178.17.171.177,178.22.124.247,178.22.124.251,178.255.220.12,18.192.146.200,185.116.60.231,185.116.60.232,185.126.237.51,185.186.78.89,185.212.169.91,185.228.26.40,185.231.233.130,185.53.57.40,185.53.57.89,188.172.228.182,188.172.229.113,188.64.184.71,190.92.176.5,191.96.101.140,192.248.156.201,192.248.191.135,192.99.38.117,193.203.191.189,194.36.144.221,195.231.17.141,199.247.28.91,199.59.247.242,201.182.97.70,202.61.226.253,204.10.163.237,209.124.84.191,209.208.26.218,211.23.143.87,213.159.1.75,213.183.48.170,213.184.85.245,216.238.104.48,216.238.71.13,23.160.56.125,23.95.73.167,31.131.4.244,31.22.115.186,31.40.212.152,34.247.229.180,34.249.110.197,38.114.121.40,38.54.30.228,38.54.79.187,38.60.253.237,41.185.29.210,41.223.52.170,45.124.65.86,45.248.77.61,45.32.123.201,45.32.183.112,45.32.210.159,45.32.67.144,45.32.77.223,45.63.67.181,45.76.247.71,45.76.252.131,45.77.148.74,45.77.165.216,45.77.51.171,46.250.220.133,49.12.102.29,5.134.119.103,5.134.119.194,5.188.183.13,51.158.202.109,51.81.186.219,51.81.33.156,54.246.224.74,54.36.103.97,61.219.247.87,61.219.247.90,62.210.88.21,64.176.165.8,64.176.4.251,64.227.16.93,65.108.104.232,65.109.39.175,65.20.76.133,65.21.81.50,65.21.81.51,66.42.124.101,66.42.75.121,67.219.99.102,67.220.94.30,69.50.95.216,79.172.239.249,81.31.156.245,81.31.156.246,86.105.14.231,86.105.14.232,89.147.110.130,89.58.38.4,91.148.135.53,91.201.67.57,91.228.7.67,92.118.205.75,94.75.232.90,95.179.145.87,95.179.245.162,95.216.116.209,173.245.48.0/20,103.21.244.0/22,103.22.200.0/22,103.31.4.0/22,141.101.64.0/18,108.162.192.0/18,190.93.240.0/20,188.114.96.0/20,197.234.240.0/22,198.41.128.0/17,162.158.0.0/15,104.16.0.0/13,104.24.0.0/14,172.64.0.0/13,131.0.72.0/22
bantime = 3600
findtime = 600
maxretry = 15
banaction = $ban_action
banaction_allports = $ban_action
EOF
}

# 通用配置函数，负责创建或更新配置文件
update_config_file() {
    local file=$1
    local content=$2
    local mode=${3:-"a"} # 默认为追加，"w"为覆盖
    
    if [ "$mode" = "w" ]; then
        echo "$content" > "$file"
    else
        echo "$content" >> "$file"
    fi
    
    return $?
}

# 添加站点配置
add_site_config() {
    local site_prefix=$1
    local access_log="$LOG_DIR/${site_prefix}.access.log"
    
    # 检查日志文件
    if [ ! -f "$access_log" ]; then
        echo -e "${RED}找不到站点 ${site_prefix} 的日志文件${NC}"
        return 1
    fi

    # 检查站点是否已配置
    if grep -q "#${site_prefix}_start" "$CONFIG_FILE"; then
        echo -e "${YELLOW}站点 ${site_prefix} 已配置，跳过${NC}"
        return 0
    fi

    # 准备配置内容
    local config_content="
#${site_prefix}_start

[nginx-req-limit-${site_prefix}]
enabled = true
port = 80,443
filter = nginx-req-limit
logpath = $access_log
maxretry = 120
findtime = 60

[nginx-botsearch-${site_prefix}]
enabled = true
port = 80,443
filter = nginx-botsearch
logpath = $access_log
maxretry = 30
findtime = 60
#${site_prefix}_end"

    # 更新配置文件
    update_config_file "$CONFIG_FILE" "$config_content"
    return 0
}

# 添加所有站点
add_all_sites() {
    # 检查配置文件
    if [ ! -f "$CONFIG_FILE" ]; then
        create_base_jail
    else
        # 直接覆盖原有配置
        create_base_jail
        echo -e "${GREEN}已清除所有站点配置${NC}"
    fi

    # 添加所有站点
    local added=0
    echo -e "${GREEN}开始添加所有站点...${NC}"
    
    # 修改find命令，只查找一天内有修改的日志文件
    while IFS= read -r log_file; do
        site_prefix=$(basename "$log_file" .access.log)
        echo -e "${BLUE}处理站点: $site_prefix${NC}"
        
        # 跳过默认日志（如果存在专门的default配置）
        if [ "$site_prefix" = "access" ] && grep -q "#default_logs_start" "$CONFIG_FILE"; then
            echo -e "${YELLOW}跳过默认日志，已有专门配置${NC}"
            continue
        fi
        
        if add_site_config "$site_prefix"; then
            added=$((added + 1))
        fi
    done < <(find "$LOG_DIR" -name "*.access.log" -type f -mtime -1)

    # 处理默认日志
    if ! grep -q "#default_logs_start" "$CONFIG_FILE" && [ -f "$LOG_DIR/access.log" ]; then
        # 检查默认日志是否一天内有更新
        if [ $(find "$LOG_DIR/access.log" -mtime -1 | wc -l) -gt 0 ]; then
            echo -e "${BLUE}添加默认日志监控${NC}"
            add_default_logs
            added=$((added + 1))
        else
            echo -e "${YELLOW}默认日志最近一天内未更新，跳过${NC}"
        fi
    fi

    # 重启服务
    if [ $added -gt 0 ]; then
        echo -e "${GREEN}已添加 $added 个站点/日志配置${NC}"
        reload_or_restart_fail2ban
    else
        echo -e "${YELLOW}没有找到可添加的站点${NC}"
    fi
}

# 添加默认日志监控
add_default_logs() {
    local access_log="$LOG_DIR/access.log"
    
    # 检查日志文件
    if [ ! -f "$access_log" ]; then
        echo -e "${RED}默认访问日志文件 $access_log 不存在${NC}"
        return 1
    fi

    # 检查是否已经在监控中
    if grep -q "#default_logs_start" "$CONFIG_FILE"; then
        echo -e "${YELLOW}默认日志已在监控列表中${NC}"
        return 0
    fi

    # 准备配置内容
    local config_content="
#default_logs_start
[nginx-req-limit-default]
enabled = true
port = 80,443
filter = nginx-req-limit
logpath = $access_log
maxretry = 30
findtime = 60

[nginx-botsearch-default]
enabled = true
port = 80,443
filter = nginx-botsearch
logpath = $access_log
maxretry = 30
findtime = 60
#default_logs_end"

    # 更新配置文件
    update_config_file "$CONFIG_FILE" "$config_content"
    reload_or_restart_fail2ban
    echo -e "${GREEN}已添加默认日志到监控列表并重启服务${NC}"
}

# 删除配置区块
remove_config_block() {
    local file=$1
    local start_pattern=$2
    local end_pattern=$3
    
    # 创建临时文件
    local temp_file=$(mktemp)
    
    # 删除配置区块
    sed "/$start_pattern/,/$end_pattern/d" "$file" > "$temp_file"
    
    # 验证文件
    if [ ! -s "$temp_file" ]; then
        echo -e "${RED}配置文件处理失败${NC}"
        rm "$temp_file"
        return 1
    fi
    
    # 更新文件
    mv "$temp_file" "$file"
    return 0
}

# 删除站点配置
remove_site() {
    local site_prefix=$1
    
    # 检查站点是否已配置
    if ! grep -q "#${site_prefix}_start" "$CONFIG_FILE"; then
        echo -e "${RED}站点 ${site_prefix} 未配置${NC}"
        return 1
    fi
    
    # 删除配置区块
    if remove_config_block "$CONFIG_FILE" "#${site_prefix}_start" "#${site_prefix}_end"; then
        reload_or_restart_fail2ban
        echo -e "${GREEN}站点 ${site_prefix} 已从配置中移除并重启服务${NC}"
        return 0
    fi
    
    return 1
}

# 删除默认日志监控
remove_default_logs() {
    # 检查是否在监控中
    if ! grep -q "#default_logs_start" "$CONFIG_FILE"; then
        echo -e "${YELLOW}默认日志不在监控列表中${NC}"
        return 0
    fi
    
    # 删除配置区块
    if remove_config_block "$CONFIG_FILE" "#default_logs_start" "#default_logs_end"; then
        reload_or_restart_fail2ban
        echo -e "${GREEN}已从监控列表中移除默认日志并重启服务${NC}"
        return 0
    fi
    
    return 1
}

# 重启或重载fail2ban服务
reload_or_restart_fail2ban() {
    echo -e "${BLUE}重新加载fail2ban服务...${NC}"
    local success=0
    
    # 尝试先使用reload
    if command_exists systemctl; then
        # 检查是否支持reload操作
        if systemctl show fail2ban | grep -q "ExecReload="; then
            systemctl reload fail2ban && success=1
            if [ $success -ne 1 ]; then
                echo -e "${YELLOW}reload失败，尝试重启服务...${NC}"
                systemctl restart fail2ban && success=1
            fi
        else
            systemctl restart fail2ban && success=1
        fi
    elif command_exists service; then
        # 尝试reload，失败则restart
        service fail2ban reload >/dev/null 2>&1 && success=1
        if [ $success -ne 1 ]; then
            echo -e "${YELLOW}reload失败，尝试重启服务...${NC}"
            service fail2ban restart && success=1
        fi
    elif [ -f /etc/init.d/fail2ban ]; then
        # 尝试reload，失败则restart
        /etc/init.d/fail2ban reload >/dev/null 2>&1 && success=1
        if [ $success -ne 1 ]; then
            echo -e "${YELLOW}reload失败，尝试重启服务...${NC}"
            /etc/init.d/fail2ban restart && success=1
        fi
    else
        echo -e "${RED}无法识别系统的服务管理器，请手动重启fail2ban服务${NC}"
        return 1
    fi
    
    if [ $success -eq 1 ]; then
        echo -e "${GREEN}服务已成功重新加载${NC}"
        # 等待服务启动
        sleep 1
        check_fail2ban_status
        return $?
    else
        echo -e "${RED}服务重载/重启失败${NC}"
        return 1
    fi
}

# 显示带样式的标题
print_header() {
    local title="$1"
    local char="="
    local width=60
    local padding=$(( (width - ${#title} - 2) / 2 ))
    
    echo
    echo -e "${BOLD}${BLUE}$(printf '%*s' "$width" | tr ' ' "$char")${NC}"
    echo -e "${BOLD}${BLUE}$(printf '%*s' "$padding" '')${WHITE} $title ${BLUE}$(printf '%*s' "$padding" '')${NC}"
    echo -e "${BOLD}${BLUE}$(printf '%*s' "$width" | tr ' ' "$char")${NC}"
    echo
}

# 显示状态消息
print_status() {
    local message="$1"
    local status="$2" # success, info, warning, error
    local icon=""
    
    case "$status" in
        success)
            icon="${GREEN}${CHECK_ICON}${NC}"
            echo -e "${icon} ${GREEN}${message}${NC}"
            ;;
        info)
            icon="${BLUE}${INFO_ICON}${NC}"
            echo -e "${icon} ${BLUE}${message}${NC}"
            ;;
        warning)
            icon="${YELLOW}${WARNING_ICON}${NC}"
            echo -e "${icon} ${YELLOW}${message}${NC}"
            ;;
        error)
            icon="${RED}${CROSS_ICON}${NC}"
            echo -e "${icon} ${RED}${message}${NC}"
            ;;
        *)
            echo -e "${message}"
            ;;
    esac
}

# 显示进度条
show_progress() {
    local message="$1"
    local sleep_time="${2:-0.1}"
    local char="▓"
    local width=30
    
    echo -ne "${message} ["
    for i in $(seq 1 $width); do
        echo -ne "${CYAN}${char}${NC}"
        sleep "$sleep_time"
    done
    echo -e "] ${GREEN}${CHECK_ICON}完成${NC}"
}

# 显示带颜色的选项菜单
show_menu_option() {
    local number="$1"
    local text="$2"
    local highlight="${3:-false}"
    
    if [ "$highlight" = "true" ]; then
        echo -e " ${BOLD}${CYAN}${number}.${NC} ${BOLD}${WHITE}${text}${NC}"
    else
        echo -e " ${CYAN}${number}.${NC} ${text}"
    fi
}

# 显示分隔线
print_divider() {
    local char="${1:--}"
    local width=60
    echo -e "${BLUE}$(printf '%*s' "$width" | tr ' ' "$char")${NC}"
}

# 修改主菜单
show_menu() {
    clear
    print_header "Fail2Ban 管理脚本"
    
    echo -e " ${SHIELD_ICON} ${BOLD}系统信息:${NC} ${OS_TYPE^} | 防火墙: ${FIREWALL_TYPE^}"
    
    # 获取fail2ban状态
    local status="未知"
    local status_color=$RED
    if command_exists fail2ban-client; then
        if check_fail2ban_status; then
            status="运行中"
            status_color=$GREEN
        else
            status="已停止"
            status_color=$RED
        fi
    else
        status="未安装"
        status_color=$YELLOW
    fi
    
    echo -e " ${LOCK_ICON} ${BOLD}Fail2Ban状态:${NC} ${status_color}${status}${NC}"
    
    # 获取封禁IP数量
    local banned_count=0
    if command_exists fail2ban-client && check_fail2ban_status; then
        # 获取所有jail列表
        local status_output=$(fail2ban-client status 2>/dev/null)
        if [ $? -eq 0 ]; then
            # 直接从状态输出中获取总封禁数
            banned_count=$(echo "$status_output" | grep "Total banned:" | head -1 | awk '{print $4}')
            # 如果上面方法失败，则手动计算所有jail的封禁数
            if [ -z "$banned_count" ] || [ "$banned_count" = "0" ]; then
                banned_count=0
                local jails=$(echo "$status_output" | grep "Jail list:" | cut -d':' -f2 | tr ',' ' ')
                for jail in $jails; do
                    jail=$(echo "$jail" | tr -d ' ')
                    [ -z "$jail" ] && continue
                    
                    local jail_status=$(fail2ban-client status "$jail" 2>/dev/null)
                    local jail_banned=$(echo "$jail_status" | grep "Currently banned:" | awk '{print $4}')
                    [ -n "$jail_banned" ] && banned_count=$((banned_count + jail_banned))
                done
            fi
        fi
    fi
    
    echo -e " ${INFO_ICON} ${BOLD}封禁IP总数:${NC} ${YELLOW}${banned_count}${NC}"
    
    print_divider
    echo
    
    local options=(
        "安装和配置 Fail2Ban和防火墙" 
        "添加所有站点" 
        "管理站点配置" 
        "列出封禁的IP" 
        "解除IP封禁" 
        "列出已配置的站点" 
        "修改IP白名单" 
        "显示fail2ban状态"
        "退出"
    )
    
    for i in "${!options[@]}"; do
        if [ $i -eq $((${#options[@]}-1)) ]; then
            show_menu_option "0" "${options[$i]}"
        else
            show_menu_option "$((i+1))" "${options[$i]}"
        fi
    done
    
    echo
    print_divider
    echo -ne "${BOLD}请选择 [0-8]:${NC} "
}

# 列出可追加的站点（美化版）
list_available_sites() {
    local found=0
    local configured_sites=$(grep -o "#.*_start" "$CONFIG_FILE" 2>/dev/null | sed 's/#\(.*\)_start/\1/' || echo "")
    
    print_status "正在扫描可追加的站点..." "info"
    sleep 0.5
    echo
    
    echo -e "${BOLD}${CYAN}可追加的站点:${NC}"
    print_divider "-"
    
    # 修改find命令，只查找一天内有修改的日志文件
    while IFS= read -r log_file; do
        site_prefix=$(basename "$log_file" .access.log)
        # 使用grep -v过滤已配置的站点
        if ! echo "$configured_sites" | grep -q "$site_prefix"; then
            echo -e "${GREEN}${ARROW_ICON}${NC} ${site_prefix}"
            found=1
        fi
    done < <(find "$LOG_DIR" -name "*.access.log" -type f -mtime -1)
    
    print_divider "-"
    
    if [ $found -eq 0 ]; then
        print_status "没有可追加的站点" "warning"
    fi
    
    return $found
}

# 列出已配置的站点（美化版）
list_configured_sites() {
    if [ ! -f "$CONFIG_FILE" ]; then
        print_status "配置文件不存在" "error"
        return 1
    fi

    local sites=$(grep -o "#.*_start" "$CONFIG_FILE" | sed 's/#\(.*\)_start/\1/' | sort)
    
    if [ -z "$sites" ]; then
        print_status "没有已配置的站点" "warning"
        return 1
    fi
    
    echo -e "${BOLD}${CYAN}已配置的站点:${NC}"
    print_divider "-"
    
    local i=1
    while IFS= read -r site; do
        echo -e "${CYAN}${i}.${NC} ${site}"
        i=$((i+1))
    done <<< "$sites"
    
    print_divider "-"
    return 0
}

# 列出封禁的IP（美化版）
list_banned_ips() {
    clear
    print_header "封禁IP列表"
    
    if ! command_exists fail2ban-client; then
        print_status "fail2ban-client 未安装" "error"
        return 1
    fi

    # 获取所有jail
    echo -ne "${BOLD}${CYAN}正在检索Jail列表...${NC}"
    local status_output=$(fail2ban-client status)
    local jails=$(echo "$status_output" | grep "Jail list:" | cut -d':' -f2 | tr ',' ' ')
    echo -e "\r${BOLD}${GREEN}Jail列表检索完成    ${NC}"
    
    if [ -z "$jails" ]; then
        print_status "没有可用的 jail" "warning"
        return 0
    fi

    echo -e "\n${BOLD}${CYAN}当前封禁IP统计:${NC}"
    print_divider "-"
    
    local total_banned=0
    local jail_list=""
    
    # 收集所有封禁信息
    for jail in $jails; do
        jail=$(echo "$jail" | tr -d ' ')
        [ -z "$jail" ] && continue
        
        echo -ne "${CYAN}检索 ${jail} 状态...${NC}\r"
        local jail_status=$(fail2ban-client status "$jail")
        local banned_count=$(echo "$jail_status" | grep "Currently banned:" | awk '{print $4}')
        
        if [ -n "$banned_count" ] && [ "$banned_count" -gt 0 ]; then
            local banned_ips=$(echo "$jail_status" | grep "Banned IP list:" | cut -d':' -f2)
            echo -e "${BOLD}${jail}:${NC} ${BG_YELLOW}${BLACK} $banned_count 个IP ${NC}"
            
            # 显示IP列表，每行一个
            echo "$banned_ips" | tr ',' '\n' | sed 's/^ //g' | while read -r ip; do
                [ -z "$ip" ] && continue
                echo -e "  ${LOCK_ICON} ${YELLOW}$ip${NC}"
            done
            
            echo
            total_banned=$((total_banned + banned_count))
            jail_list="${jail_list} ${jail}"
        fi
    done
    
    print_divider "-"
    
    if [ $total_banned -eq 0 ]; then
        print_status "当前没有封禁的IP" "info"
    else
        echo -e "${BOLD}${WHITE}总计:${NC} ${BG_GREEN}${BLACK} $total_banned 个IP被封禁 ${NC}"
    fi
    
    return 0
}

# 解除IP封禁
unban_ip() {
    local ip=$1
    
    if ! command_exists fail2ban-client; then
        echo -e "${RED}fail2ban-client 未安装${NC}"
        return 1
    fi
    
    # 验证IP格式
    if ! [[ $ip =~ ^[0-9]+\.[0-9]+\.[0-9]+\.[0-9]+$ ]]; then
        echo -e "${RED}无效的IP地址格式: $ip${NC}"
        return 1
    fi
    
    # 获取所有jail
    local jails=$(fail2ban-client status | grep "Jail list:" | cut -d':' -f2 | tr ',' ' ')
    
    local unbanned=0
    for jail in $jails; do
        jail=$(echo "$jail" | tr -d ' ')
        if [ ! -z "$jail" ]; then
            # 检查该jail中是否有这个IP
            if fail2ban-client status "$jail" | grep "Banned IP list:" | grep -q "$ip"; then
                if fail2ban-client set "$jail" unbanip "$ip" 2>/dev/null; then
                    echo -e "${GREEN}已从 $jail 解封IP: $ip${NC}"
                    unbanned=1
                fi
            fi
        fi
    done

    if [ $unbanned -eq 0 ]; then
        echo -e "${RED}未找到被封禁的IP: $ip${NC}"
    else
        echo -e "${GREEN}完成解封操作${NC}"
    fi
}

# 检查服务状态
check_fail2ban_status() {
    # 检查各种服务管理器
    if command_exists systemctl; then
        # systemd
        if systemctl is-active --quiet fail2ban; then
            return 0
        else
            return 1
        fi
    elif command_exists service; then
        # SysV init
        service fail2ban status >/dev/null 2>&1
        return $?
    elif [ -f /etc/init.d/fail2ban ]; then
        # 直接使用init脚本
        /etc/init.d/fail2ban status >/dev/null 2>&1
        return $?
    else
        # 最后尝试检查进程
        pgrep -f "/usr/bin/fail2ban-server" >/dev/null 2>&1
        return $?
    fi
}

# 管理站点配置（整合所有站点管理功能）
manage_site_config() {
    local options=("追加单个站点" "删除单个站点" "添加默认日志监控" "删除默认日志监控" "返回主菜单")
    local exit_option=${#options[@]}
    
    while true; do
        clear
        print_header "站点配置管理"
        
        # 显示当前状态
        echo -e " ${CONFIG_ICON} ${BOLD}当前配置状态:${NC}"
        echo -n " 默认日志监控: "
        if grep -q "#default_logs_start" "$CONFIG_FILE"; then
            echo -e "${GREEN}已启用${NC}"
        else
            echo -e "${YELLOW}未启用${NC}"
        fi
        
        echo -n " 已配置站点数: "
        local site_count=$(grep -c "#.*_start" "$CONFIG_FILE")
        echo -e "${GREEN}$site_count${NC}"
        echo
        
        print_divider
        
        # 显示菜单选项
        for i in "${!options[@]}"; do
            show_menu_option "$((i+1))" "${options[$i]}"
        done
        
        print_divider
        
        # 获取用户选择
        echo -ne "${BOLD}请选择 [1-$exit_option]:${NC} "
        read -r config_op
        
        # 验证输入
        if ! [[ "$config_op" =~ ^[0-9]+$ ]] || [ "$config_op" -lt 1 ] || [ "$config_op" -gt $exit_option ]; then
            print_status "无效的选择" "error"
            sleep 1
            continue
        fi
        
        # 处理选择
        case $config_op in
            1) # 追加站点
                clear
                print_header "追加单个站点"
                
                # 先检查是否有可追加的站点
                local available_sites=()
                local site_found=0
                local configured_sites=$(grep -o "#.*_start" "$CONFIG_FILE" 2>/dev/null | sed 's/#\(.*\)_start/\1/' || echo "")
                
                print_status "正在扫描可追加的站点..." "info"
                sleep 0.5
                
                # 收集可用站点
                while IFS= read -r log_file; do
                    site_prefix=$(basename "$log_file" .access.log)
                    if ! echo "$configured_sites" | grep -q "$site_prefix"; then
                        available_sites+=("$site_prefix")
                        site_found=1
                    fi
                done < <(find "$LOG_DIR" -name "*.access.log" -type f -mtime -1)
                
                if [ $site_found -eq 0 ]; then
                    print_status "没有可追加的站点" "warning"
                else
                    echo -e "\n${BOLD}${CYAN}可追加的站点:${NC}"
                    print_divider "-"
                    
                    # 显示站点列表
                    for i in "${!available_sites[@]}"; do
                        echo -e "${CYAN}$((i+1)).${NC} ${available_sites[$i]}"
                    done
                    
                    print_divider "-"
                    
                    # 用户选择站点
                    echo -ne "${BOLD}请选择要追加的站点 [1-${#available_sites[@]}] (输入0返回):${NC} "
                    read -r site_choice
                    
                    # 验证并处理选择
                    if [[ "$site_choice" =~ ^[0-9]+$ ]]; then
                        if [ "$site_choice" -eq 0 ]; then
                            continue
                        elif [ "$site_choice" -ge 1 ] && [ "$site_choice" -le "${#available_sites[@]}" ]; then
                            local selected_site="${available_sites[$((site_choice-1))]}"
                            
                            echo -ne "${YELLOW}${WARNING_ICON} 确认追加站点 ${BOLD}$selected_site${NC}${YELLOW}? [y/N]:${NC} "
                            read -r confirm
                            
                            if [[ "$confirm" =~ ^[Yy]$ ]]; then
                                show_progress "追加站点中" 0.05
                                if add_site_config "$selected_site"; then
                                    reload_or_restart_fail2ban
                                    print_status "站点 $selected_site 已成功追加" "success"
                                else
                                    print_status "追加站点 $selected_site 失败" "error"
                                fi
                            else
                                print_status "操作已取消" "info"
                            fi
                        else
                            print_status "无效的选择" "error"
                        fi
                    else
                        print_status "无效的输入" "error"
                    fi
                fi
                ;;
            2) # 删除站点
                clear
                print_header "删除站点配置"
                
                # 获取已配置的站点列表
                local sites=()
                while IFS= read -r site; do
                    [ -z "$site" ] && continue
                    sites+=("$site")
                done <<< "$(grep -o "#.*_start" "$CONFIG_FILE" | sed 's/#\(.*\)_start/\1/' | sort)"
                
                if [ ${#sites[@]} -eq 0 ]; then
                    print_status "没有已配置的站点" "warning"
                else
                    echo -e "\n${BOLD}${CYAN}已配置的站点:${NC}"
                    print_divider "-"
                    
                    # 显示站点列表
                    for i in "${!sites[@]}"; do
                        echo -e "${CYAN}$((i+1)).${NC} ${sites[$i]}"
                    done
                    
                    print_divider "-"
                    
                    # 用户选择站点
                    echo -ne "${BOLD}请选择要删除的站点 [1-${#sites[@]}] (输入0返回):${NC} "
                    read -r site_choice
                    
                    # 验证并处理选择
                    if [[ "$site_choice" =~ ^[0-9]+$ ]]; then
                        if [ "$site_choice" -eq 0 ]; then
                            continue
                        elif [ "$site_choice" -ge 1 ] && [ "$site_choice" -le "${#sites[@]}" ]; then
                            local selected_site="${sites[$((site_choice-1))]}"
                            
                            echo -ne "${RED}${WARNING_ICON} 确定要删除站点 ${BOLD}$selected_site${NC}${RED}? [y/N]:${NC} "
                            read -r confirm
                            
                            if [[ "$confirm" =~ ^[Yy]$ ]]; then
                                show_progress "删除站点中" 0.05
                                if remove_site "$selected_site"; then
                                    print_status "站点 $selected_site 已成功删除" "success"
                                else
                                    print_status "删除站点 $selected_site 失败" "error"
                                fi
                            else
                                print_status "操作已取消" "info"
                            fi
                        else
                            print_status "无效的选择" "error"
                        fi
                    else
                        print_status "无效的输入" "error"
                    fi
                fi
                ;;
            3) # 添加默认日志
                clear
                print_header "添加默认日志监控"
                
                if grep -q "#default_logs_start" "$CONFIG_FILE"; then
                    print_status "默认日志已在监控列表中" "info"
                else
                    if [ -f "$LOG_DIR/access.log" ]; then
                        if [ $(find "$LOG_DIR/access.log" -mtime -1 | wc -l) -gt 0 ]; then
                            echo -ne "${YELLOW}${WARNING_ICON} 确认添加默认日志监控? [y/N]:${NC} "
                            read -r confirm
                            
                            if [[ "$confirm" =~ ^[Yy]$ ]]; then
                                show_progress "添加默认日志监控" 0.05
                                add_default_logs
                                print_status "已添加默认日志到监控列表" "success"
                            else
                                print_status "操作已取消" "info"
                            fi
                        else
                            print_status "默认日志最近一天内未更新，跳过" "warning"
                        fi
                    else
                        print_status "默认日志文件不存在" "error"
                    fi
                fi
                ;;
            4) # 删除默认日志
                clear
                print_header "删除默认日志监控"
                
                if ! grep -q "#default_logs_start" "$CONFIG_FILE"; then
                    print_status "默认日志不在监控列表中" "info"
                else
                    echo -ne "${RED}${WARNING_ICON} 确认删除默认日志监控? [y/N]:${NC} "
                    read -r confirm
                    
                    if [[ "$confirm" =~ ^[Yy]$ ]]; then
                        show_progress "删除默认日志监控" 0.05
                        remove_default_logs
                        print_status "已从监控列表中移除默认日志" "success"
                    else
                        print_status "操作已取消" "info"
                    fi
                fi
                ;;
            $exit_option) # 返回
                return 0
                ;;
        esac
        
        # 按任意键继续
        echo
        read -n 1 -s -r -p "按任意键继续..." key
        echo
    done
}

# 启动fail2ban服务
start_fail2ban() {
    if command_exists systemctl; then
        systemctl start fail2ban
    elif command_exists service; then
        service fail2ban start
    elif [ -f /etc/init.d/fail2ban ]; then
        /etc/init.d/fail2ban start
    else
        print_status "无法识别系统的服务管理器" "error"
        return 1
    fi
    
    # 等待服务启动
    sleep 2
    check_fail2ban_status
    return $?
}

# 显示fail2ban状态（美化版）
show_status() {
    clear
    print_header "Fail2Ban 系统状态"
    
    if ! command_exists fail2ban-client; then
        print_status "fail2ban-client 未安装" "error"
        return 1
    fi
    
    # 检查服务是否运行
    if ! check_fail2ban_status; then
        print_status "Fail2Ban 服务未运行" "error"
        
        if command_exists systemctl; then
            local status_info=$(systemctl status fail2ban | head -n 5)
            echo -e "${YELLOW}${INFO_ICON} 服务状态信息:${NC}\n$status_info"
        fi
        
        echo
        print_status "正在尝试启动服务..." "info"
        
        if ! start_fail2ban; then
            print_status "无法启动 Fail2Ban 服务" "error"
            return 1
        else
            print_status "Fail2Ban 服务已成功启动" "success"
        fi
    else
        print_status "Fail2Ban 服务正在运行" "success"
    fi
    
    # 显示版本信息
    local version_info=$(fail2ban-client --version 2>/dev/null)
    echo -e "\n${BOLD}${CYAN}Fail2Ban 版本:${NC}"
    print_divider "-"
    echo -e "${GREEN}$version_info${NC}"
    
    # 显示状态概览
    echo -e "\n${BOLD}${CYAN}Fail2Ban 状态概览:${NC}"
    print_divider "-"
    local status_overview=$(fail2ban-client status)
    echo -e "$status_overview"
    
    # 获取并显示各个jail的详细状态
    local jails=$(echo "$status_overview" | grep "Jail list:" | cut -d':' -f2 | tr ',' ' ')
    
    if [ ! -z "$jails" ]; then
        echo -e "\n${BOLD}${CYAN}Jail 状态详情:${NC}"
        print_divider "-"
        
        for jail in $jails; do
            jail=$(echo "$jail" | tr -d ' ')
            if [ ! -z "$jail" ]; then
                echo -ne "${CYAN}正在获取 ${jail} 状态...${NC}\r"
                local status_output=$(fail2ban-client status "$jail")
                
                # 提取数据
                local filter=$(echo "$status_output" | grep "Filter:" | awk -F: '{print $2}' | xargs)
                local actions=$(echo "$status_output" | grep "Actions:" | awk -F: '{print $2}' | xargs)
                local current_banned=$(echo "$status_output" | grep "Currently banned:" | awk '{print $4}')
                local total_banned=$(echo "$status_output" | grep "Total banned:" | awk '{print $4}')
                local banned_ips=$(echo "$status_output" | grep "Banned IP list:" | cut -d':' -f2)
                
                # 显示Jail标题
                echo -e "\n${BOLD}${WHITE}[$jail]${NC} - ${BG_BLUE}${WHITE} 当前封禁: $current_banned 个IP ${NC}"
                
                # 显示详细信息
                echo -e "${BLUE}过滤器:${NC} $filter"
                echo -e "${BLUE}动作:${NC} $actions"
                echo -e "${BLUE}当前封禁:${NC} $current_banned"
                echo -e "${BLUE}总计封禁:${NC} $total_banned"
                
                # 如果有封禁的IP，则显示列表
                if [ "$current_banned" -gt 0 ]; then
                    print_divider "·"
                    echo -e "${YELLOW}${LOCK_ICON} 已封禁IP列表:${NC}"
                    echo "$banned_ips" | tr ',' '\n' | sed 's/^ //g' | while read -r ip; do
                        [ -z "$ip" ] && continue
                        echo -e " ${ARROW_ICON} ${YELLOW}$ip${NC}"
                    done
                fi
            fi
        done
    fi
    
    return 0
}

# 修改IP白名单
edit_whitelist() {
    [ ! -f "$CONFIG_FILE" ] && print_status "配置文件不存在" "error" && return 1
    
    clear
    print_header "IP白名单管理"
    
    # 提取当前白名单
    local current_whitelist=$(grep "^ignoreip" "$CONFIG_FILE" | cut -d'=' -f2 | sed 's/^[ \t]*//')
    
    echo -e "${BOLD}${CYAN}当前白名单IP列表:${NC}"
    print_divider "-"
    
    if [ -z "$current_whitelist" ]; then
        print_status "当前没有设置白名单IP" "warning"
    else
        local i=1
        echo "$current_whitelist" | tr ',' '\n' | while read -r ip; do
            [ -z "$ip" ] && continue
            echo -e "${CYAN}${i}.${NC} ${ip}"
            i=$((i+1))
        done
    fi
    
    print_divider "-"
    
    # 显示选项
    echo
    show_menu_option "1" "添加IP到白名单"
    show_menu_option "2" "从白名单中删除IP"
    show_menu_option "3" "返回主菜单"
    echo
    
    echo -ne "${BOLD}请选择:${NC} "
    read -r op_choice
    
    case $op_choice in
        1) # 添加IP
            echo -ne "${BOLD}请输入要添加的IP (多个IP用逗号分隔):${NC} "
            read -r new_ips
            [ -z "$new_ips" ] && return 0
            
            # 显示进度条
            show_progress "更新白名单" 0.05
            
            # 合并并去重
            local combined_list="${current_whitelist},${new_ips}"
            local unique_list=$(echo "$combined_list" | tr ',' '\n' | sort -u | grep -v '^$' | tr '\n' ',' | sed 's/,$//')
            
            # 更新配置
            sed -i "s/^ignoreip = .*$/ignoreip = ${unique_list}/" "$CONFIG_FILE" && \
                reload_or_restart_fail2ban && \
                print_status "已更新白名单并重启服务" "success"
            ;;
        2) # 删除IP
            if [ -z "$current_whitelist" ]; then
                print_status "没有可删除的IP" "warning"
                return 0
            fi
            
            echo -ne "${BOLD}请输入要删除的IP编号(多个用空格分隔):${NC} "
            read -r ids
            [ -z "$ids" ] && return 0
            
            # 显示进度条
            show_progress "更新白名单" 0.05
            
            # 将白名单转换为数组
            IFS=',' read -r -a ip_array <<< "$current_whitelist"
            
            # 创建新列表，排除要删除的IP
            local new_list=()
            local ip_count=${#ip_array[@]}
            
            for i in $(seq 0 $((ip_count-1))); do
                if ! echo " $ids " | grep -q " $((i+1)) "; then
                    new_list+=("${ip_array[$i]}")
                fi
            done
            
            # 转换回逗号分隔的字符串
            local new_list_str=$(IFS=,; echo "${new_list[*]}")
            
            # 更新配置
            sed -i "s/^ignoreip = .*$/ignoreip = ${new_list_str}/" "$CONFIG_FILE" && \
                reload_or_restart_fail2ban && \
                print_status "已更新白名单并重启服务" "success"
            ;;
        3) # 返回
            return 0
            ;;
        *)
            print_status "无效的选择" "error"
            ;;
    esac
    
    return 0
}

# 主程序
main() {
    # 检测系统和防火墙类型
    detect_os
    detect_firewall
    adjust_config
    check_directories

    # 主循环
    while true; do
        # 显示菜单
        show_menu
        read -r choice
        
        case $choice in
            1) # 安装配置
                clear
                print_header "安装和配置Fail2Ban"
                install_packages
                if [ $? -eq 0 ]; then
                    show_progress "配置防火墙" 0.05
                    configure_firewall
                    
                    show_progress "创建过滤器" 0.03
                    create_filters
                    
                    print_status "安装和配置成功完成！" "success"
                else
                    print_status "安装过程中出现错误" "error"
                fi
                ;;
            2) # 添加所有站点
                clear
                print_header "添加所有站点"
                echo -ne "${YELLOW}${WARNING_ICON} 这将清除现有站点配置并重新添加所有站点，确认继续? [y/N]:${NC} "
                read -r confirm
                if [[ "$confirm" =~ ^[Yy]$ ]]; then
                    add_all_sites
                else
                    print_status "操作已取消" "info"
                fi
                ;;
            3) # 管理站点配置
                manage_site_config
                ;;
            4) # 列出封禁IP
                list_banned_ips
                ;;
            5) # 解除IP封禁
                clear
                print_header "解除IP封禁"
                
                echo -e "${YELLOW}${WARNING_ICON} 请谨慎解封IP，确保您了解相关风险。${NC}"
                echo
                
                echo -ne "${BOLD}请输入要解封的IP:${NC} "
                read -r ip
                
                if [ -n "$ip" ]; then
                    echo -ne "${YELLOW}${WARNING_ICON} 确认解封IP ${BOLD}${ip}${NC}${YELLOW}? [y/N]:${NC} "
                    read -r confirm
                    if [[ "$confirm" =~ ^[Yy]$ ]]; then
                        show_progress "解封IP中" 0.05
                        unban_ip "$ip"
                    else
                        print_status "操作已取消" "info"
                    fi
                else
                    print_status "未指定IP，操作已取消" "warning"
                fi
                ;;
            6) # 列出已配置站点
                clear
                print_header "已配置站点列表"
                list_configured_sites
                ;;
            7) # 修改白名单
                edit_whitelist
                ;;
            8) # 显示状态
                show_status
                ;;
            0) # 退出
                clear
                print_header "退出程序"
                print_status "感谢使用Fail2Ban管理脚本，再见！" "success"
                exit 0
                ;;
            *)
                print_status "无效的选择，请重试" "error"
                sleep 1
                ;;
        esac
        
        # 按任意键继续
        echo
        read -n 1 -s -r -p "按任意键继续..."
        echo
    done
}

# 如果直接运行脚本，则执行main函数
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main
fi