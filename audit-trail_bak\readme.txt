=== Audit ===
Contributors: johnny5, vincentb
Donate link: http://urbangiraffe.com/about/
Tags: audit, log, version, diff, admin, dashboard, notification, record, history, security, tracking, user
Requires at least: 4.7
Tested up to: 6.4
Stable tag: 2.5.5
Requires PHP: 7.4
Supports PHP: 7.4-8.4
License: GPLv2 or later
License URI: http://www.gnu.org/licenses/gpl-2.0.html

全面详尽的WordPress管理日志系统，记录所有后台操作，包括用户登录时间、内容编辑历史、媒体文件操作等。

== Description ==

通过Audit Trail，您可以全面监控WordPress网站的所有管理操作，简单、轻量级，是同时管理多个网站的理想选择。

= 主要功能 =

* 记录用户登录登出
* 监控内容创建和编辑
* 追踪评论管理
* 记录文件上传和删除
* 捕获插件激活与停用
* 支持多种页面构建器操作跟踪
* 自动清理过期日志
* 高流量站点优化模式

= 高级功能 =

* 按作者、日期、操作类型等过滤日志
* 批量操作功能
* 敏感数据自动屏蔽
* 支持IPv6
* 资源使用优化

== Installation ==

1. 上传 'audit-trail' 文件夹到 /wp-content/plugins/ 目录
2. 在WordPress插件菜单中激活Audit Trail
3. 访问"工具 > Audit Trail"进行管理

== Frequently Asked Questions ==

= 如何清理旧日志？ =

插件提供自动清理功能，您可以在设置中配置保留天数，默认为30天。也可以手动执行清理操作。

= 这会占用很多数据库空间吗？ =

Audit Trail针对高流量站点进行了优化，默认启用高流量模式，仅记录1%的页面访问，并排除静态资源请求，有效控制数据库大小。

= 支持哪些页面构建器？ =

目前支持Elementor、Beaver Builder、Divi Builder、WPBakery和SiteOrigin，以及原生Gutenberg编辑器。

== Changelog ==

= 2.5.5 =
* PHP兼容性优化：增强与PHP 7.4-8.4的兼容性
* 数据处理优化：完善敏感数据处理机制，防止数据泄露
* 其他改进：更新作者信息和版本号，统一数组声明语法

= 2.5.4 =
* 多站点优化：改进多WordPress站点使用时的资源占用
* 功能修复：修复按操作类型批量删除功能，同步操作类型列表
* 代码优化：改进删除方法，分离手动清理功能

= 2.5.3 =
* 新增功能：支持记录多种页面构建器操作
* 优化页面性能：添加高流量模式，控制日志记录数量
* 自动清理机制：增加自动清理功能，定期清理旧日志

= 2.5.2 =
* 功能改进：按操作类型批量删除功能
* 优化：减少不必要的数据库查询
* 安全性：增强数据处理安全性

= 2.5.1 =
* 修复：与WordPress 6.3兼容性问题
* 添加：支持新的WordPress钩子
* 改进：日志条目清理流程

= 2.5.0 =
* 重要更新：完全重写内部架构，提高性能
* 添加：全面的中文支持
* 新增：高级过滤系统，便于管理大量日志

= 2.4.0 =
* 更新列表表格以匹配WP 4.3

= 2.3.1 =
* 修复保存文章bug

= 2.3.0 =
* 修复插件以适配WP 3.9
* 移除对jQuery UI Dialog的依赖

== Upgrade Notice ==

= 2.5.5 =
此版本增强了PHP 7.4-8.4兼容性，优化了Elementor集成，改进了JSON数据处理。建议所有用户升级。

= 2.5.4 =
此版本改进了多站点支持，修复了批量删除功能，优化了资源使用。推荐所有多站点管理员升级。

= 2.5.3 =
增加页面构建器支持和高流量站点优化，提高插件适用性。

=== 核心功能 ===

* **全面的操作日志** - 记录WordPress后台的几乎所有操作，方便多用户系统中的管理和问责
* **文章版本控制** - 保存文章和页面的完整历史记录，支持版本比较和恢复
* **用户活动追踪** - 记录注册用户和访客的页面访问情况
* **登录安全监控** - 记录所有登录尝试，包括失败的登录
* **CSV导出功能** - 支持将日志导出为CSV文件进行离线分析
* **高级搜索功能** - 按用户名、操作类型、IP地址和日期范围搜索日志
* **权限控制** - 可禁止特定用户使用审计插件
* **用户友好界面** - 简洁直观的中文操作界面
* **低资源消耗** - 优化的数据处理机制，减少对网站性能的影响
* **页面构建器监控** - 跟踪Elementor、Beaver Builder、Divi、WPBakery、SiteOrigin和Gutenberg等页面构建器的编辑操作
* **访客访问记录** - 可选择记录或排除访客的页面访问记录

=== 技术实现细节 ===

==== 数据记录机制 ====

插件通过WordPress钩子系统监听以下操作类别：

1. **文章与页面管理**
   * 创建、编辑、删除文章和页面
   * 状态转换（草稿、发布、私密、待审、回收站等之间的转换）
   * 使用JSON格式保存完整的文章内容和元数据

2. **页面构建器操作**
   * 监控Elementor编辑器和文档保存
   * 跟踪Beaver Builder布局保存
   * 记录Divi Builder的修改
   * 监视WPBakery页面构建器操作
   * 追踪SiteOrigin面板数据变更
   * 记录Gutenberg块编辑

3. **用户活动**
   * 登录/登出追踪
   * 登录失败记录
   * 用户注册
   * 个人资料更新
   * 密码重置请求

4. **媒体库操作**
   * 上传、编辑、删除附件文件
   * 记录文件名、类型和关联文章

5. **评论管理**
   * 评论编辑和删除

6. **分类目录和链接**
   * 分类目录的添加、编辑、删除
   * 链接管理操作

7. **主题和插件**
   * 主题切换记录
   * 插件激活和停用

8. **页面访问**
   * 记录注册用户和访客的页面访问情况
   * 可选择性排除页面访问日志以减少数据量

==== 数据库结构 ====

插件在WordPress数据库中创建一个表`{wp_prefix}audit_trail`，包含以下字段：

* `id` - 日志记录的唯一标识符
* `operation` - 操作类型（如save_post, wp_login等）
* `user_id` - 执行操作的用户ID
* `ip` - 用户IP地址
* `happened_at` - 操作发生的时间
* `item_id` - 相关项目的ID（如文章ID、用户ID等）
* `data` - 操作相关的详细数据（JSON格式）
* `title` - 操作项目的标题或名称

==== 安全设计 ====

* **权限控制** - 限制只有具有`publish_posts`、`audit_trail`或`edit_plugins`权限的用户才能访问审计日志
* **防止未授权访问** - 通过WordPress nonce验证所有操作请求
* **用户过滤** - 可配置忽略特定用户的操作
* **禁用权限** - 可设置禁止特定用户使用审计插件
* **IPv6支持** - 适当处理IPv4和IPv6地址

==== 界面实现 ====

* **AJAX技术** - 使用AJAX加载详细信息，减少页面刷新
* **响应式设计** - 适应不同屏幕尺寸的管理界面
* **数据过滤** - 提供多种过滤选项，便于管理大量日志数据
* **批量操作** - 支持批量删除日志记录
* **自定义搜索** - 高级搜索和过滤功能，支持搜索结果导出

=== 自定义扩展 ===

插件提供以下过滤器和动作钩子，便于开发者自定义和扩展功能：

* `audit_collect` - 添加新的监听操作类型
* `audit_show_operation` - 自定义操作类型的显示方式
* `audit_show_item` - 自定义操作项目的显示方式
* `audit_show_details` - 自定义操作详情的显示方式
* `audit_listen` - 添加自定义操作的监听钩子
* `audit_operation` - 进一步自定义操作显示方式

=== 语言支持 ===

* 简体中文（默认界面语言）
* 英语
* 爱沙尼亚语
* 白俄罗斯语
* 德语
* 日语
* 罗马尼亚语
* 立陶宛语

*注意：插件默认使用简体中文界面*

== 安装 ==

1. 下载 `audit-trail.zip` 文件
2. 解压缩
3. 将 `audit-trail` 目录上传到您的 `/wp-content/plugins` 目录
4. 在WordPress管理界面激活插件
5. 通过 `工具/Audit` 菜单配置插件

详细的插件安装说明可以在[插件安装页面](http://urbangiraffe.com/articles/how-to-install-a-wordpress-plugin/)上找到。

== 常见问题解答 ==

= 如何筛选特定用户的操作日志？ =
在审计日志页面顶部有搜索框，您可以输入用户名进行筛选。

= 如何导出日志数据？ =
在日志列表页面，点击"导出CSV"按钮即可将当前筛选的日志导出为CSV文件。

= 如何排除页面访问记录？ =
在日志列表页面的搜索区域中，勾选"排除'访问页面'日志"选项，这将过滤掉所有template_redirect操作记录。此设置也会应用到CSV导出中。

= 支持哪些页面构建器？ =
插件目前支持以下页面构建器的操作监控：
- Elementor
- Beaver Builder
- Divi Builder
- WPBakery Page Builder
- SiteOrigin Page Builder
- Gutenberg（WordPress原生编辑器）

= CSV导出时显示403错误怎么办？ =

这通常是由于以下原因之一导致：

1. 权限问题 - 确保您的用户账号有"publish_posts"、"audit_trail"或"edit_plugins"权限之一。
2. 插件权限设置 - 检查您的用户ID未被添加到"禁止用户"设置中。
3. 服务器配置 - 部分服务器可能限制直接访问PHP文件，请联系您的主机提供商或系统管理员。
4. WordPress安全设置 - 某些安全插件可能阻止直接访问插件PHP文件，尝试临时禁用这些安全插件后再试。

如果以上方法无法解决问题，您可以尝试通过在wp-config.php中添加以下代码来启用调试：
`define('WP_DEBUG', true);`
`define('WP_DEBUG_LOG', true);`
然后再次尝试导出，并查看WordPress错误日志以获取更详细的错误信息。

= 如何阻止某些用户查看审计日志？ =
在插件设置页面，您可以指定不允许访问审计功能的用户ID，用逗号分隔。

= 日志会占用大量数据库空间吗？ =
Audit插件采用了优化的数据存储方式，但长期使用后日志会不断增长。建议定期清理旧日志，特别是在高流量网站上。您可以在设置中配置自动过期时间，超过该时间的日志将被自动删除。

= 为什么某些操作没有被记录？ =
请确保在插件设置中启用了相应的操作类型监控。此外，如果用户被列入"忽略用户"列表，他们的操作将不会被记录。

== 截图 ==

1. 审计日志界面

== 文档 ==

完整文档可以在[Audit Trail页面](http://urbangiraffe.com/plugins/audit-trail/)上找到。

== 数据库表及字段详细说明 ==

插件创建的`{wp_prefix}audit_trail`表包含以下字段：

1. **id** (int) - 自增主键
2. **operation** (varchar) - 操作类型标识符
3. **user_id** (int) - 执行操作的WordPress用户ID
4. **ip** (int unsigned) - 用户的IP地址（IPv4地址转换为整数存储）
5. **happened_at** (datetime) - 操作发生的时间戳
6. **item_id** (int) - 与操作相关的对象ID（如文章ID、评论ID等）
7. **data** (longtext) - 操作的详细数据，以JSON格式存储
8. **title** (varchar) - 操作对象的标题或名称

== 关键代码实现说明 ==

=== 主要类和功能 ===

1. **Audit_Trail** - 主插件类，负责初始化、菜单创建和基本功能
   * `plugins_loaded()` - 启动所有已配置操作的监听
   * `admin_menu()` - 创建管理菜单
   * `plugin_activated()` - 处理插件激活和数据库创建

2. **AT_Auditor** - 核心监听类，处理所有操作的记录
   * `audit_collect()` - 注册可监听的操作类型
   * `audit_listen()` - 为每种操作类型添加相应的WordPress钩子
   * 包含多个针对特定操作的处理函数（如`save_post()`, `wp_login()`等）
   * 专门的页面构建器监听函数（如`elementor_editor_after_save()`等）

3. **AT_Audit** - 日志记录模型类
   * `create()` - 创建新的日志记录
   * `get()`, `get_by_post()` - 检索日志记录
   * `delete()` - 删除日志记录
   * `expire()` - 清理过期日志

4. **Audit_Trail_Table** - 表格显示类，继承WP_List_Table
   * `prepare_items()` - 处理搜索和分页
   * 提供特殊样式和格式化功能

5. **AuditAjax** - 处理AJAX请求的类
   * `at_view()` - 显示日志详情
   * `at_close()` - 关闭详情视图
   * `at_get_users()` - 获取用户列表

=== 监听的WordPress钩子 ===

* **文章相关**：`save_post`, `delete_post`, 多种状态转换钩子
* **用户相关**：`wp_login`, `wp_logout`, `user_register`, `profile_update`
* **评论相关**：`edit_comment`, `delete_comment`
* **附件相关**：`add_attachment`, `edit_attachment`, `delete_attachment`
* **分类相关**：`add_category`, `edit_category`, `delete_category`
* **链接相关**：`add_link`, `edit_link`, `delete_link`
* **主题相关**：`switch_theme`
* **插件相关**：`activate_plugin`, `deactivate_plugin`
* **页面访问**：`template_redirect`
* **页面构建器相关**：`elementor/editor/after_save`, `elementor/document/after_save`, `fl_builder_after_save_layout`, `siteorigin_panels_save_post`, `vc_after_save_post`, `et_fb_save_layout`, `blocks_parsed`

== 未来功能规划 ==

为保持插件简洁高效，计划添加以下低资源消耗功能：

1. **日志定期自动清理**：设置日志自动清理的阈值，如超过特定数量或特定日期的日志自动删除，防止数据库膨胀。

2. **关键操作通知**：仅对重要操作（如登录失败多次、用户权限变更等）发送邮件或站内通知，避免过多通知造成干扰。

3. **日志导出改进**：增加按搜索结果导出功能，仅导出必要的日志记录，节省处理时间。

4. **智能日志聚合**：将相似操作（如短时间内同一用户的多次相同操作）聚合显示，减少日志条目数量。

5. **轻量级统计报表**：添加简单的操作统计图表，直观显示用户活动情况，无需复杂计算。

这些功能都遵循"按需加载"原则，确保插件运行轻量高效。

== 变更日志 ==

= 2.4.1 =
* 修复页面构建器插件的update等操作无法记录的问题
* 增加更多页面构建器操作钩子，全方位捕获Elementor、Beaver Builder、Divi、WPBakery和SiteOrigin的编辑活动
* 优化Gutenberg块编辑器的跟踪机制，增加块渲染的记录
* 提高页面构建器相关钩子的执行优先级，确保所有操作都能被记录
* 增强各页面构建器操作细节的展示，包括操作类型和时间信息

= 2.4 =
* 改进附件删除的记录方式，现在会记录更多有用的文件信息
* 增强访客访问记录的展示方式，清晰区分访客和已登录用户
* 优化IP地址处理，支持IPv6地址格式
* 修复特定情况下错误展示的用户信息
* 改进插件设置界面，使配置更加直观

= 2.3 =
* 添加"排除访问页面日志"选项，可过滤掉template_redirect操作记录
* 优化CSV导出功能，使用admin-ajax.php作为中介，解决NGINX限制问题
* 改进权限检查，现在支持publish_posts、audit_trail或edit_plugins权限的用户导出CSV
* 改进CSV导出中的IP地址显示
* 优化搜索功能，支持将排除选项应用到CSV导出结果中

= 2.2 =
* 添加页面构建器监控功能，支持监控Elementor、Beaver Builder、Divi Builder等流行页面构建器的编辑操作
* 新增详细的页面构建器编辑记录视图，显示构建器类型、编辑时间等信息
* 在CSV导出和搜索功能中添加对页面构建器操作的支持
* 优化Gutenberg块编辑的监控机制

= 2.1 =
* 修复CSV导出时可能出现的403错误问题
* 改进CSV导出功能的权限验证，与管理界面权限一致
* 优化WordPress环境加载方式，提高兼容性
* 增加常见问题解答章节，提供更多故障排除指导

= 2.0 =
* 修改插件名称为"Audit"，作者名为"vince"
* 添加日志搜索功能，支持按用户名、操作类型、IP地址和日期范围搜索
* 优化日志列表显示和搜索结果处理
* 提升整体用户体验和界面交互

= 1.9 =
* 简化界面设计，移除语言选择器，保持插件全中文界面
* 翻译"禁止用户"和"登录失败记录日志"选项
* 优化语言加载逻辑，确保始终使用中文界面
* 保持插件主名称"Audit Trail"不变

= 1.8 =
* 修复语言切换功能，确保设置后立即生效
* 优化语言文件加载机制，增强翻译覆盖率
* 添加页面自动刷新功能，以应用语言更改
* 改进用户体验，使界面更加友好

= 1.7 =
* 添加语言设置选项（中文、英文或使用WordPress默认语言）
* 添加用户选择功能，方便管理员选择禁止使用插件的用户
* 优化用户体验和界面交互

= 1.6 =
* 添加禁止特定用户使用插件的功能
* 移除Plugin Support功能和页面
* 简化界面

= 1.5 =
* 设置默认界面语言为简体中文
* 确保所有界面和操作使用中文显示

= 1.4 =
* 更新以兼容WordPress 4.3-6.4
* 使用现代PHP构造函数替换旧式构造函数
* 替换已废弃的get_userdatabylogin函数为get_user_by
* 改进IP地址处理以支持IPv6
* 更新category页面链接为新的edit-tags.php格式

= 1.3 =
* 切换到JSON格式保存数据
* 修复在激活时创建数据库的问题

= 1.2.4 =
* 不在文章更新日志条目中包含修订版本
* 跟踪插件激活/停用

= 1.2.3 =
* 修复1.2.2引入的Ajax错误

= 1.2.2 =
* 修复数据双重序列化问题
* 修复忽略用户0以忽略所有匿名用户
* 小规模代码清理

= 1.2.1 =
* 为WP 4刷新
* 由Massimiliano提供的意大利语翻译

= 1.2 =
* 修复分页
* 添加失败登录审计
* 实验性的error_log()支持，可与fail2ban一起使用

= 1.1.16 =
* 修复WP 3.5警告

= 1.1.15 =
* 恢复每页日志数量为25

= 1.1.14 =
* 清理所有代码
* XSS安全审查

= 1.1.13 =
* 添加立陶宛语
* 添加俄语

= 1.1.12 =
* 在跟踪列表底部添加批量操作
* 修复分页器
* 更新罗马尼亚语翻译

= 1.1.11 =
* 使用WP本地时间显示审计跟踪

= 1.1.10 =
* 3.2兼容性
* 罗马尼亚语翻译，感谢Mikalay Lisica

= 1.1.9 =
* 添加日语翻译，感谢Chestnut
* 修复显示文章详情的错误
* 修复删除按钮

= 1.1.7 =
* 添加中文翻译，感谢maoanyuan！

= 1.1.6 =
* 添加白俄罗斯语翻译

= 1.1.5 =
* 修复要监控的操作

= 1.1.4 =
* 添加爱沙尼亚语
* 恢复删除项目

= 1.1.3 =
* 移除深度斜杠

= 1.1.2 =
* 不保存文章差异
* 修复CSV导出中的用户名

= 1.1.1 =
* 使用jQuery
* 移除文章编辑差异 - 这已经内置于WordPress多个版本中

= 1.1 =
* WP 2.8兼容性

= 1.0.10 =
* 仅在AT页面包含prototype

= 1.0.9 =
* WP 2.5兼容性

= 1.0.8 =
* 根据博客时区偏移显示日志项目

= 1.0.7 =
* 修复favicon.ico日志
* 忽略某些用户
* 跟踪失败的登录尝试

= 1.0.6 =
* 修复警告
* 允许按用户名搜索

= 1.0.5 =
* 修复过期
* 停止记录自动保存

= 1.0.4 =
* 支持管理SSL

= 1.0.3 =
* 修复拼写错误
* 添加反转文章编辑顺序的选项

= 1.0 =
* 修订代码
* 更多AJAX操作
* 可扩展的审计器

= 0.3 =
* 使其适用于不同的数据库前缀

= 0.2 =
* 添加版本历史记录

= 0.1 =
* 首次发布
