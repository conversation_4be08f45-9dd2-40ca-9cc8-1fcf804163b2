<?php
/**
 * Plugin Name: Audit
 * Plugin URI: http://urbangiraffe.com/plugins/audit-trail/
 * Description: 全面详尽的WordPress管理日志系统，记录所有后台操作，包括用户登录时间、内容编辑历史、媒体文件操作等，便于管理员监控与追踪站点变更。
 * 是同时管理多个WordPress网站的理想选择 - 简单、轻量级，具有极低的资源消耗。强化了与Elementor等主流页面构建器的兼容性。
 * 未来功能考虑: 自动日志清理(已实现)，关键操作通知，智能日志聚合，以及对更多页面构建器的支持。
 * Version: 2.5.15
 * Author: <PERSON> / Vince
 * Author URI: http://urbangiraffe.com
 * Text Domain: audit-trail
 * Domain Path: /locale
 * Requires at least: 4.7
 * Tested up to: 6.4
 * Requires PHP: 7.2
 * Supports PHP: 7.2-8.4
 * ============================================================================================================
 * This software is provided "as is" and any express or implied warranties, including, but not limited to, the
 * implied warranties of merchantibility and fitness for a particular purpose are disclaimed. In no event shall
 * the copyright owner or contributors be liable for any direct, indirect, incidental, special, exemplary, or
 * consequential damages(including, but not limited to, procurement of substitute goods or services; loss of
 * use, data, or profits; or business interruption) however caused and on any theory of liability, whether in
 * contract, strict liability, or tort(including negligence or otherwise) arising in any way out of the use of
 * this software, even if advised of the possibility of such damage.
 * ============================================================================================================
 *
 * Available filters are:
 *   - audit_collect        - Passed an array of methods to monitor, return the array with any additions
 * 	- audit_show_operation - Passed an AT_Audit object, return the object with 'message' changed for type of operation
 *   - audit_show_item      - Passed an AT_Audit object, return the object with 'message' changed for type of item
 *   - audit_show_details   - Passed an AT_Audit object, return a message to display when the operation is clicked for more details
 *
 * Available actions are:
 *   - audit_listen - Passed the name of a method to monitor.  Add appropriate filters/actions to monitor the method
 */

// 紧急调试模式 - 在页面加载前捕获所有错误
if (isset($_GET['page']) && $_GET['page'] == 'audit-trail.php') {
    // 确保错误会被记录
    @ini_set('log_errors', 1);
    @ini_set('display_errors', 0);
    
    // 记录访问信息
    error_log('Audit Trail页面访问开始: ' . date('Y-m-d H:i:s'));
    
    // 记录PHP版本和内存限制
    error_log('PHP版本: ' . PHP_VERSION . ', 内存限制: ' . ini_get('memory_limit'));
    
    // 记录请求信息
    error_log('请求URL: ' . (isset($_SERVER['REQUEST_URI']) ? $_SERVER['REQUEST_URI'] : 'unknown'));
    
    // 记录WordPress版本
    global $wp_version;
    error_log('WordPress版本: ' . (isset($wp_version) ? $wp_version : 'unknown'));
    
    // 设置错误处理器
    set_error_handler(function($errno, $errstr, $errfile, $errline) {
        error_log("Audit Trail紧急错误: [$errno] $errstr - $errfile:$errline");
        return false; // 继续执行默认错误处理
    });
    
    // 设置异常处理器
    set_exception_handler(function($exception) {
        error_log("Audit Trail未捕获异常: " . $exception->getMessage() . " in " . $exception->getFile() . " on line " . $exception->getLine());
        error_log("异常堆栈跟踪: " . $exception->getTraceAsString());
        
        // 输出友好的错误页面而不是500错误
        header("HTTP/1.1 200 OK");
        echo '<!DOCTYPE html><html><head><meta charset="utf-8"><title>Audit Trail错误</title>';
        echo '<style>body{font-family:Arial,sans-serif;line-height:1.6;color:#333;max-width:800px;margin:0 auto;padding:20px}h1{color:#d54e21}pre{background:#f5f5f5;padding:10px;overflow:auto}</style>';
        echo '</head><body>';
        echo '<h1>Audit Trail插件加载出错</h1>';
        echo '<p>插件在加载过程中遇到了问题。此错误已被记录，以下是错误详情：</p>';
        echo '<pre>' . htmlspecialchars($exception->getMessage()) . '</pre>';
        echo '<p>错误位置：' . htmlspecialchars($exception->getFile()) . ' 第 ' . $exception->getLine() . ' 行</p>';
        echo '<p><a href="' . admin_url('plugins.php') . '">返回插件页面</a></p>';
        echo '</body></html>';
        exit;
    });
    
    // 使用输出缓冲，捕获所有输出
    ob_start(function($output) {
        // 如果检测到致命错误但没有被异常处理器捕获
        $error = error_get_last();
        if ($error && in_array($error['type'], [E_ERROR, E_PARSE, E_CORE_ERROR, E_COMPILE_ERROR])) {
            error_log("Audit Trail致命错误: [{$error['type']}] {$error['message']} - {$error['file']}:{$error['line']}");
            
            // 输出友好的错误页面
            $error_page = '<!DOCTYPE html><html><head><meta charset="utf-8"><title>Audit Trail错误</title>';
            $error_page .= '<style>body{font-family:Arial,sans-serif;line-height:1.6;color:#333;max-width:800px;margin:0 auto;padding:20px}h1{color:#d54e21}pre{background:#f5f5f5;padding:10px;overflow:auto}</style>';
            $error_page .= '</head><body>';
            $error_page .= '<h1>Audit Trail插件发生致命错误</h1>';
            $error_page .= '<p>插件在运行过程中遇到了致命错误。此错误已被记录，以下是错误详情：</p>';
            $error_page .= '<pre>' . htmlspecialchars($error['message']) . '</pre>';
            $error_page .= '<p>错误位置：' . htmlspecialchars($error['file']) . ' 第 ' . $error['line'] . ' 行</p>';
            $error_page .= '<p><a href="' . admin_url('plugins.php') . '">返回插件页面</a></p>';
            $error_page .= '</body></html>';
            return $error_page;
        }
        
        return $output;
    });
    
    // 注册关闭函数
    register_shutdown_function(function() {
        // 结束输出缓冲
        @ob_end_flush();
        
        // 记录访问完成
        error_log('Audit Trail页面访问结束: ' . date('Y-m-d H:i:s'));
        
        // 记录内存使用情况
        $memory_usage = round(memory_get_peak_usage() / 1024 / 1024, 2);
        error_log('内存峰值使用: ' . $memory_usage . ' MB');
    });
}

// 防止直接访问
if (!defined('ABSPATH')) {
    exit;
}

// 尝试增加内存限制
@ini_set('memory_limit', '256M');

// 定义插件目录路径常量，用于错误处理器判断
define('AUDIT_TRAIL_DIR', plugin_dir_path(__FILE__));

// 紧急恢复模式 - 如果URL包含debug=1参数，禁用所有钩子以减少错误可能性
if (isset($_GET['page']) && $_GET['page'] == 'audit-trail.php' && isset($_GET['debug']) && $_GET['debug'] == 1) {
    define('AUDIT_TRAIL_DEBUG', true);
    error_log('Audit Trail紧急恢复模式已启用');
} else {
    define('AUDIT_TRAIL_DEBUG', false);
}

// 包含错误处理函数
function audit_trail_error_handler($errno, $errstr, $errfile, $errline) {
    // 只处理审计插件自身的错误
    if (strpos($errfile, AUDIT_TRAIL_DIR) !== false) {
        // 记录错误但继续执行
        error_log("Audit Trail错误: [$errno] $errstr - $errfile:$errline");
    }
    
    // 不阻止默认错误处理
    return false;
}

// 仅在管理界面或AJAX请求时设置错误处理器
if (is_admin() || (defined('DOING_AJAX') && DOING_AJAX)) {
    // 只捕获严重错误，忽略通知和警告
    set_error_handler("audit_trail_error_handler", E_ERROR | E_PARSE | E_CORE_ERROR | E_COMPILE_ERROR | E_USER_ERROR);
}

define( 'AUDIT_TRAIL_VERSION', '2.5.15' );

/**
 * Audit plugin
 * 
 * 设计理念：保持简洁、轻量、低资源消耗
 * 
 * 未来功能建议：
 * - 日志自动清理：根据时间或数量阈值自动清理
 * - 关键操作通知：仅关键操作发送通知
 * - 智能日志聚合：合并短时间内的重复操作
 * - 轻量级统计：简单图表展示活动情况
 * - 搜索结果导出：仅导出必要记录
 *
 * @package Audit
 **/

class Audit_Trail {
	private static $instance = null;
	private $auditor;

	static function init() {
		if ( is_null( self::$instance ) ) {
			self::$instance = new Audit_Trail();

			// 将语言加载移到这里，确保WordPress完全加载后执行
			add_action('plugins_loaded', function() {
				load_plugin_textdomain('audit-trail', false, dirname(plugin_basename(__FILE__)).'/locale/');
			});
			
			// 检查是否需要升级
			self::maybe_upgrade();
		}

		return self::$instance;
	}

	/**
	 * 应用语言设置为中文
	 */
	static function apply_language_setting() {
		// 固定使用中文语言
		if (strpos($_SERVER['REQUEST_URI'], 'audit-trail') !== false) {
			// 确保WP_Locale_Switcher完全初始化后再调用
			if (function_exists('switch_to_locale') && isset($GLOBALS['wp_locale_switcher'])) {
				switch_to_locale('zh_CN');
				
				// 加载语言文件
				$mo_file = WP_PLUGIN_DIR . '/audit-trail/locale/zh_CN.mo';
				if (file_exists($mo_file)) {
					load_textdomain('audit-trail', $mo_file);
				}
			} else {
				// 记录错误，用于调试
				error_log('Audit Trail: 无法切换语言，WP_Locale_Switcher未初始化');
			}
		}
	}

	/**
	 * Constructor hooks all the appropriate filters and actions for the plugin, as well as creating the auditor
	 * object which monitors everything else
	 *
	 * @return void
	 **/

	function __construct() {
		// 设置错误捕获
		try {
			// 检查是否在插件页面
			$is_plugin_page = false;
			if (is_admin() && isset($_GET['page']) && $_GET['page'] == basename(__FILE__)) {
				$is_plugin_page = true;
				
				// 在插件页面加载前自动检查并修复数据库表
				add_action('admin_init', array($this, 'auto_repair_database'));
			}
			
			// 检查必要的文件
			$required_files = [
				dirname(__FILE__).'/models/auditor.php',
				dirname(__FILE__).'/models/audit.php',
				dirname(__FILE__).'/models/batch-logger.php'
			];
			
			foreach ($required_files as $file) {
				if (!file_exists($file)) {
					throw new Exception("找不到必要的文件: " . basename($file));
				}
				include_once($file);
			}
			
			if (is_admin()) {
				if (!class_exists('WP_List_Table')) {
					require_once ABSPATH . 'wp-admin/includes/class-wp-list-table.php';
				}
				
				include_once(dirname(__FILE__).'/models/pager.php');
				
				add_action('admin_menu', array($this, 'admin_menu'));
				add_action('load-tools_page_audit-trail', array($this, 'admin_head'));
				
				// 将语言设置移到admin_init钩子，确保WordPress完全加载后执行
				add_action('admin_init', array($this, 'apply_language_setting'));
				
				// Ajax函数
				if (defined('DOING_AJAX')) {
					include_once dirname(__FILE__).'/ajax.php';
					$this->ajax = new AuditAjax();
				}
				
				// 使用插件基名的正确方式
				$plugin_basename = plugin_basename(__FILE__);
				add_filter('plugin_action_links_' . $plugin_basename, array($this, 'plugin_settings'), 10, 1);
			}
			
			// 注册自动清理计划任务
			$hook_name = 'audit_trail_daily_cleanup_' . get_current_blog_id();
			add_action($hook_name, array($this, 'auto_cleanup'));
			
			// 检查是否需要设置自动清理计划任务
			if (!wp_next_scheduled($hook_name)) {
				$random_time = time() + rand(0, 3600); // 随机延迟0-1小时
				wp_schedule_event($random_time, 'daily', $hook_name);
			}
			
			// 设置默认值（仅在选项不存在时）
			$default_options = [
				'audit_auto_cleanup_enabled' => true,
				'audit_auto_cleanup_days' => 30,
				'audit_high_volume_mode' => true,
				'audit_log_sample_rate' => 10,
				'audit_methods' => ['post', 'attach', 'user', 'theme', 'link', 'category', 'comment', 'audit', 'plugin', 'pagebuilder']
			];
			
			foreach ($default_options as $option => $default_value) {
				if (get_option($option) === false) {
					add_option($option, $default_value);
				}
			}
			
			// 只在管理界面或首次激活后检查表
			if (is_admin() && get_option('audit_trail_just_activated', false)) {
				// 检查表并删除激活标记
				AT_Audit::install_tables();
				delete_option('audit_trail_just_activated');
			}
			
			// 初始化审计器
			$this->auditor = new AT_Auditor();
			$this->plugins_loaded();
			
		} catch (Exception $e) {
			// 记录错误信息
			error_log('Audit Trail初始化错误: ' . $e->getMessage());
			
			// 在管理界面显示错误
			if (is_admin()) {
				add_action('admin_notices', function() use ($e) {
					echo '<div class="error"><p>审计插件初始化错误: ' . esc_html($e->getMessage()) . '</p></div>';
				});
			}
		}
	}

	/**
	 * 自动清理过期日志
	 */
	function auto_cleanup() {
		// 检查是否启用了自动清理
		if (!get_option('audit_auto_cleanup_enabled', true)) {
			return;
		}
		
		// 获取保留天数设置
		$days = intval(get_option('audit_auto_cleanup_days', 30));
		if ($days <= 0) {
			return;
		}
		
		// 执行清理
		AT_Audit::expire($days);
		
		// 记录清理时间
		update_option('audit_last_cleanup', current_time('mysql'));
	}
	
	/**
	 * 检查插件版本并执行必要的升级
	 */
	static function maybe_upgrade() {
		$current_version = get_option('audit_trail', '0.0');
		
		if (version_compare($current_version, AUDIT_TRAIL_VERSION, '<')) {
			// 执行升级操作
			self::upgrade_plugin($current_version);
			
			// 更新版本号
			update_option('audit_trail', AUDIT_TRAIL_VERSION);
		}
	}
	
	/**
	 * 根据版本执行不同的升级操作
	 */
	static function upgrade_plugin($from_version) {
		global $wpdb;
		
		// 添加默认设置
		if (!get_option('audit_auto_cleanup_days', false)) {
			add_option('audit_auto_cleanup_days', 30); // 默认保留30天日志
		}
		
		if (!get_option('audit_auto_cleanup_enabled', false)) {
			add_option('audit_auto_cleanup_enabled', true); // 默认启用自动清理
		}
		
		if (!get_option('audit_high_volume_mode', false)) {
			add_option('audit_high_volume_mode', true); // 默认启用高流量模式
		}
		
		if (!get_option('audit_log_sample_rate', false)) {
			add_option('audit_log_sample_rate', 10); // 默认采样率10%
		}

		// 设置默认监控方法，除了"用户页访问"外，其他默认勾选
		if (!get_option('audit_methods', false)) {
			$methods = array('post', 'attach', 'user', 'theme', 'link', 'category', 'comment', 'audit', 'plugin', 'pagebuilder');
			// 注意：没有包含'viewing'（用户页访问）
			add_option('audit_methods', $methods);
		}
		
		// 确保数据库表被创建
		AT_Audit::install_tables();
		
		// 升级表格结构，支持IPv6
		$table_name = $wpdb->prefix . 'audit_trail';
		$ipv6_upgraded = get_option('audit_ipv6_upgraded', false);
		
		if (!$ipv6_upgraded) {
			// 检查ip字段类型
			$row = $wpdb->get_row("SHOW COLUMNS FROM $table_name LIKE 'ip'");
			if ($row && strpos(strtolower($row->Type), 'int') !== false) {
				// 从int升级到varchar(45)以支持IPv6
				$wpdb->query("ALTER TABLE $table_name CHANGE `ip` `ip` VARCHAR(45) NULL DEFAULT NULL");
				update_option('audit_ipv6_upgraded', true);
			}
		}
		
		// 添加索引以提高性能
		$indexes_created = get_option('audit_indexes_created', false);
		
		if (!$indexes_created) {
			$wpdb->query("ALTER TABLE $table_name ADD INDEX `operation_index` (`operation`(32))");
			$wpdb->query("ALTER TABLE $table_name ADD INDEX `user_id_index` (`user_id`)");
			$wpdb->query("ALTER TABLE $table_name ADD INDEX `happened_at_index` (`happened_at`)");
			$wpdb->query("ALTER TABLE $table_name ADD INDEX `ip_index` (`ip`)");
			update_option('audit_indexes_created', true);
		}
	}

	function plugin_settings( $links ) {
		$settings_link = '<a href="tools.php?page=audit-trail.php">'.__('Trail', 'audit-trail' ).'</a>';
		array_unshift( $links, $settings_link );
		return $links;
	}

	/**
	 * After all the plugins have loaded this starts listening for all registered filters/actions
	 *
	 * @return void
	 **/

	function plugins_loaded() {
		$methods = get_option( 'audit_methods' );

		if ( !empty( $methods) && is_array( $methods ) ) {
			foreach( $methods AS $name)
				do_action( 'audit_listen', $name);
		}
	}

	function base_url() {
		return __FILE__;
	}


	/**
	 * Creates the database and upgrades any existing data
	 *
	 * @return void
	 **/

	static function plugin_activated() {
		global $wpdb;
		
		// 尝试增加PHP内存限制
		@ini_set('memory_limit', '512M'); // 增加到512M，确保有足够内存
		
		// 记录激活时间和信息
		error_log('Audit Trail: 插件激活开始 - ' . date('Y-m-d H:i:s'));
		
		try {
			// 确保WordPress数据库函数可用
			if (!function_exists('dbDelta')) {
				require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
			}

			// 打印数据库信息，帮助诊断
			$db_info = "数据库类型: " . $wpdb->db_server_info() . ", 数据库前缀: " . $wpdb->prefix;
			error_log('Audit Trail: 数据库信息 - ' . $db_info);
			
			// 检查表是否已存在
			$table_name = $wpdb->prefix . 'audit_trail';
			$table_exists = $wpdb->get_var("SHOW TABLES LIKE '$table_name'") === $table_name;
			
			if ($table_exists) {
				error_log('Audit Trail: 表已存在，跳过创建');
			} else {
				// 尝试直接使用硬编码的创建表命令
				error_log('Audit Trail: 开始创建数据库表');
				$charset_collate = $wpdb->get_charset_collate();
				
				$sql = "CREATE TABLE IF NOT EXISTS $table_name (
					`id` int(11) NOT NULL auto_increment,
					`operation` varchar(40) NOT NULL default '',
					`user_id` int(11) NOT NULL,
					`ip` varchar(45) NOT NULL default '',
					`happened_at` datetime NOT NULL,
					`item_id` int(11) default NULL,
					`data` longtext,
					`title` varchar(100) default NULL,
					PRIMARY KEY (`id`)
				) $charset_collate;";
				
				$create_result = $wpdb->query($sql);
				
				if ($create_result === false) {
					error_log('Audit Trail: 直接创建表失败 - ' . $wpdb->last_error);
					
					// 尝试使用dbDelta
					dbDelta($sql);
					
					// 再次检查表是否创建
					$table_exists = $wpdb->get_var("SHOW TABLES LIKE '$table_name'") === $table_name;
					if (!$table_exists) {
						throw new Exception('无法创建数据库表，请检查数据库权限');
					}
				}
				
				// 表创建成功，添加索引
				$indexes = array(
					"ALTER TABLE $table_name ADD INDEX `idx_operation` (`operation`)",
					"ALTER TABLE $table_name ADD INDEX `idx_user_id` (`user_id`)",
					"ALTER TABLE $table_name ADD INDEX `idx_happened_at` (`happened_at`)",
					"ALTER TABLE $table_name ADD INDEX `idx_ip` (`ip`)"
				);
				
				foreach ($indexes as $index_sql) {
					$wpdb->query($index_sql);
				}
				
				error_log('Audit Trail: 数据库表创建完成');
			}
			
			// 设置默认选项，使用update_option而不是add_option，以确保已存在时也更新
			update_option('audit_auto_cleanup_days', 30); // 默认保留30天日志
			update_option('audit_auto_cleanup_enabled', true); // 默认启用自动清理
			update_option('audit_high_volume_mode', true); // 默认启用高流量模式
			update_option('audit_log_sample_rate', 1); // 默认1%记录
			
			// 设置激活标记，用于首次加载时检查表
			update_option('audit_trail_just_activated', true);
			
			// 优化计划任务，使用站点ID作为唯一标识
			$hook_name = 'audit_trail_daily_cleanup_' . get_current_blog_id();
			if (!wp_next_scheduled($hook_name)) {
				// 随机化执行时间，避免多个站点同时执行
				$random_time = time() + rand(0, 3600); // 随机延迟0-1小时
				wp_schedule_event($random_time, 'daily', $hook_name);
			}
			
			update_option('audit_trail', AUDIT_TRAIL_VERSION);
			error_log('Audit Trail: 插件激活成功完成 - ' . date('Y-m-d H:i:s'));
		}
		catch (Exception $e) {
			// 记录激活过程中的错误
			error_log('Audit Trail激活错误: ' . $e->getMessage());
			
			// 尝试再次强制创建表
			try {
				// 自定义表创建函数
				self::force_create_audit_table();
				
				// 设置激活标记
				update_option('audit_trail_just_activated', true);
			} catch (Exception $inner_e) {
				error_log('Audit Trail: 二次尝试创建表失败: ' . $inner_e->getMessage());
			}
		}
	}
	
	/**
	 * 强制创建审计表
	 * 增加创建表的单独函数，确保表一定被创建
	 */
	private static function force_create_audit_table() {
		global $wpdb;
		
		$table_name = $wpdb->prefix . 'audit_trail';
		
		// 确保WordPress数据库函数可用
		if (!function_exists('dbDelta')) {
			require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
		}
		
		// 首先尝试删除可能存在但有问题的表
		$wpdb->query("DROP TABLE IF EXISTS $table_name");
		
		// 使用最简单的SQL创建表
		$sql = "CREATE TABLE $table_name (
			`id` int(11) NOT NULL auto_increment,
			`operation` varchar(40) NOT NULL default '',
			`user_id` int(11) NOT NULL,
			`ip` varchar(45) NOT NULL default '',
			`happened_at` datetime NOT NULL,
			`item_id` int(11) default NULL,
			`data` longtext,
			`title` varchar(100) default NULL,
			PRIMARY KEY (`id`)
		)";
		
		// 如果可以确定charset_collate，则添加
		if (method_exists($wpdb, 'get_charset_collate')) {
			$sql .= ' ' . $wpdb->get_charset_collate();
		} else {
			// 使用默认的字符集和排序规则
			$sql .= ' CHARACTER SET utf8 COLLATE utf8_general_ci';
		}
		
		// 执行创建表
		$result = $wpdb->query($sql);
		
		if ($result === false) {
			error_log('Audit Trail: 强制创建表SQL错误: ' . $wpdb->last_error);
			throw new Exception('无法强制创建表: ' . $wpdb->last_error);
		}
		
		// 添加基本索引
		$wpdb->query("ALTER TABLE $table_name ADD INDEX `idx_operation` (`operation`)");
		$wpdb->query("ALTER TABLE $table_name ADD INDEX `idx_happened_at` (`happened_at`)");
		
		// 再次检查表是否创建成功
		$table_exists = $wpdb->get_var("SHOW TABLES LIKE '$table_name'") === $table_name;
		if (!$table_exists) {
			throw new Exception('强制创建表失败，表仍不存在');
		}
		
		error_log('Audit Trail: 强制创建表成功');
	}

	/**
	 * Inject Audit Trail into the menu
	 *
	 * @return void
	 **/

	function admin_menu() {
		// 检查当前用户是否被禁止使用插件
		$current_user_id = get_current_user_id();
		$forbidden_users = explode(',', get_option('audit_forbidden_users', ''));
		
		if (in_array($current_user_id, $forbidden_users)) {
			return; // 用户被禁止使用，不添加菜单
		}
		
		if ( current_user_can( 'edit_plugins' ) || current_user_can( 'audit_trail' ) )
  			add_management_page( __("Audit",'audit-trail' ), __("Audit",'audit-trail' ), "publish_posts", basename( __FILE__), array( $this, "admin_screen") );
	}


	/**
	 * Inserts the edit box into the edit post/page area
	 *
	 * @return void
	 **/

	function edit_box() {
		global $post;
		$this->render( 'edit_box', array( 'trail' => AT_Audit::get_by_post( $post->ID) ));
	}

	function edit_box_advanced() {
		global $post;
		$this->render( 'edit_box_25', array( 'trail' => AT_Audit::get_by_post( $post->ID) ));
	}

	function submenu( $inwrap = false) {
		// Decide what to do
		$sub = isset( $_GET['sub']) ? $_GET['sub'] : '';
		if ( !in_array( $sub, array( 'options' ) ) )
			$sub = '';

		if ( $inwrap == true)
			$this->render( 'submenu', array( 'sub' => $sub, 'class' => 'class="subsubsub"', 'trail' => ' | ' ) );

		return $sub;
	}

	/**
	 * Displays the admin screen
	 *
	 * @return void
	 **/

	function admin_screen() {
		global $wpdb;
		
		try {
			// 检查是否显示数据库诊断
			if (isset($_GET['diagnostic']) && $_GET['diagnostic'] == 1) {
				$this->database_diagnostic();
				return;
			}
			
			// 如果是紧急恢复模式，显示简化界面
			if (defined('AUDIT_TRAIL_DEBUG') && AUDIT_TRAIL_DEBUG === true) {
				echo '<div class="wrap">';
				echo '<h1>' . __('Audit Trail - 紧急恢复模式', 'audit-trail') . '</h1>';
				echo '<div class="notice notice-warning"><p>' . __('插件当前运行在紧急恢复模式下。此模式禁用了大部分功能以减少错误可能性。', 'audit-trail') . '</p></div>';
				
				// 显示系统信息
				echo '<h2>' . __('系统信息', 'audit-trail') . '</h2>';
				echo '<table class="widefat" style="width:600px;">';
				echo '<tr><td>' . __('PHP版本', 'audit-trail') . '</td><td>' . PHP_VERSION . '</td></tr>';
				echo '<tr><td>' . __('WordPress版本', 'audit-trail') . '</td><td>' . get_bloginfo('version') . '</td></tr>';
				echo '<tr><td>' . __('Audit Trail版本', 'audit-trail') . '</td><td>' . AUDIT_TRAIL_VERSION . '</td></tr>';
				echo '<tr><td>' . __('内存限制', 'audit-trail') . '</td><td>' . ini_get('memory_limit') . '</td></tr>';
				echo '<tr><td>' . __('数据库前缀', 'audit-trail') . '</td><td>' . $wpdb->prefix . '</td></tr>';
				echo '</table>';
				
				// 提供数据库修复选项
				echo '<h2>' . __('数据库维护', 'audit-trail') . '</h2>';
				echo '<form method="post">';
				wp_nonce_field('audit_trail_db_repair');
				echo '<p><input type="submit" name="repair_db" value="' . __('修复数据库表', 'audit-trail') . '" class="button button-primary" /></p>';
				echo '</form>';
				
				// 如果用户点击了修复按钮
				if (isset($_POST['repair_db']) && check_admin_referer('audit_trail_db_repair')) {
					// 尝试修复表
					AT_Audit::install_tables();
					
					// 检查表是否存在
					$table_name = $wpdb->prefix . 'audit_trail';
					$table_exists = $wpdb->get_var("SHOW TABLES LIKE '$table_name'") === $table_name;
					
					if ($table_exists) {
						echo '<div class="notice notice-success"><p>' . __('数据库表已成功创建或修复。', 'audit-trail') . '</p></div>';
						
						// 尝试优化表
						$wpdb->query("OPTIMIZE TABLE $table_name");
						
						// 检查并修复索引
						$this->check_and_repair_indexes($table_name);
						
						echo '<p><a href="' . admin_url('tools.php?page=audit-trail.php') . '" class="button">' . __('返回正常模式', 'audit-trail') . '</a></p>';
					} else {
						echo '<div class="notice notice-error"><p>' . __('数据库表创建或修复失败。请检查数据库权限。', 'audit-trail') . '</p></div>';
					}
				}
				
				echo '</div>';
				return;
			}
			
			// 检查当前用户是否被禁止使用插件
			$current_user_id = get_current_user_id();
			$forbidden_users = explode(',', get_option('audit_forbidden_users', ''));
			
			if (in_array($current_user_id, $forbidden_users)) {
				wp_die(__('You are not allowed to access this page.', 'audit-trail'));
				return;
			}
			
			if (!current_user_can('edit_plugins') && !current_user_can('audit_trail')) {
				return;
			}
			
			// 检查数据库表是否存在
			$table_name = $wpdb->prefix . 'audit_trail';
			$table_exists = $wpdb->get_var("SHOW TABLES LIKE '$table_name'") === $table_name;
			
			if (!$table_exists) {
				// 表不存在，尝试创建
				error_log('Audit Trail: 数据库表不存在，尝试创建');
				AT_Audit::install_tables();
				
				// 再次检查表是否创建成功
				$table_exists = $wpdb->get_var("SHOW TABLES LIKE '$table_name'") === $table_name;
				
				if (!$table_exists) {
					// 表创建失败，显示错误消息和诊断链接
					echo '<div class="error"><p>';
					echo __('无法创建审计日志数据库表。请检查数据库权限或联系服务器管理员。', 'audit-trail');
					echo '</p></div>';
					echo '<p><a href="' . admin_url('tools.php?page=audit-trail.php&diagnostic=1') . '" class="button button-primary">' . __('运行数据库诊断', 'audit-trail') . '</a></p>';
					return;
				}
			}
			
			// 显示数据库诊断链接
			echo '<div class="nav-tab-wrapper">';
			echo '<a href="' . admin_url('tools.php?page=audit-trail.php') . '" class="nav-tab' . (!isset($_GET['sub']) ? ' nav-tab-active' : '') . '">' . __('审计记录', 'audit-trail') . '</a>';
			echo '<a href="' . admin_url('tools.php?page=audit-trail.php&sub=options') . '" class="nav-tab' . (isset($_GET['sub']) && $_GET['sub'] == 'options' ? ' nav-tab-active' : '') . '">' . __('设置', 'audit-trail') . '</a>';
			echo '<a href="' . admin_url('tools.php?page=audit-trail.php&diagnostic=1') . '" class="nav-tab">' . __('诊断', 'audit-trail') . '</a>';
			echo '</div>';
			
			// 决定显示什么
			$sub = $this->submenu();
			
			// 清理过期日志
			AT_Audit::expire(get_option('audit_expiry') === false ? 30 : get_option('audit_expiry'));
			
			if ($sub == '') {
				$this->screen_trail();
			} else if ($sub == 'options') {
				$this->screen_options();
			}
		} catch (Exception $e) {
			// 捕获并显示任何异常
			error_log('Audit Trail: admin_screen错误: ' . $e->getMessage());
			echo '<div class="error"><p>';
			echo __('加载审计日志页面时出错: ', 'audit-trail') . esc_html($e->getMessage());
			echo '</p></div>';
			echo '<p><a href="' . admin_url('tools.php?page=audit-trail.php&diagnostic=1') . '" class="button button-primary">' . __('运行数据库诊断', 'audit-trail') . '</a></p>';
		}
	}


	/**
	 * Displays the audit trail log
	 *
	 * @return void
	 **/

	function screen_trail() {
		try {
			// 检查必要的类是否存在
			if (!class_exists('Audit_Trail_Table')) {
				// 尝试加载pager.php文件
				$pager_file = dirname(__FILE__).'/models/pager.php';
				if (file_exists($pager_file)) {
					include_once($pager_file);
				} else {
					throw new Exception(__('找不到必要的文件: models/pager.php', 'audit-trail'));
				}
			}
			
			// 实例化表格类
			$table = new Audit_Trail_Table();
			
			// 准备表格项目
			try {
				$table->prepare_items();
			} catch (Exception $e) {
				error_log('Audit Trail: 准备表格项目时出错: ' . $e->getMessage());
				echo '<div class="error"><p>';
				echo __('加载审计日志数据时出错: ', 'audit-trail') . esc_html($e->getMessage());
				echo '</p></div>';
				
				// 尝试显示空表格
				$table->items = array();
				$table->_pagination_args = array(
					'total_items' => 0,
					'total_pages' => 0,
					'per_page' => 20,
				);
			}
			
			// 渲染视图
			$this->render('trail', array('table' => $table));
			
		} catch (Exception $e) {
			error_log('Audit Trail: screen_trail错误: ' . $e->getMessage());
			echo '<div class="error"><p>';
			echo __('显示审计日志时出错: ', 'audit-trail') . esc_html($e->getMessage());
			echo '</p></div>';
		}
	}


	/**
	 * Display audit trail options
	 *
	 * @return void
	 **/

	function screen_options() {
		$saved = false;
		
		if ( isset( $_POST['save']) && check_admin_referer( 'audittrail-update_options' ) ) {
			update_option( 'audit_methods',    stripslashes_deep( $_POST['methods'] ) );
			update_option( 'audit_expiry',     intval( $_POST['expiry']) );
			update_option( 'audit_post',       isset( $_POST['post']) ? true : false);
			update_option( 'audit_post_order', isset( $_POST['post_order']) ? true : false);
			update_option( 'audit_version',    isset( $_POST['version']) ? 'true' : 'false' );
			update_option( 'audit_ignore',     preg_replace( '/[^0-9,]/', '', $_POST['ignore_users']) );
			update_option( 'audit_forbidden_users', preg_replace( '/[^0-9,]/', '', $_POST['forbidden_users']) );
			update_option( 'audit_error_log',  isset( $_POST['error_log'] ) ? true : false );
			
			// 保存新增的高流量模式设置
			update_option( 'audit_auto_cleanup_enabled', isset( $_POST['auto_cleanup_enabled'] ) ? true : false );
			update_option( 'audit_high_volume_mode', isset( $_POST['high_volume_mode'] ) ? true : false );
			update_option( 'audit_log_sample_rate', intval( $_POST['log_sample_rate'] ) );
			update_option( 'audit_exclude_paths', sanitize_textarea_field( $_POST['exclude_paths'] ) );
			
			$saved = true;
			$this->render_message( __( 'Options have been updated', 'audit-trail' ) );
		}
		
		// 处理手动清理请求 - 移到这里以确保独立处理
		if (isset($_POST['manual_cleanup']) && check_admin_referer( 'audittrail-update_options' )) {
			$days = intval($_POST['manual_cleanup_days']);
			if ($days > 0) {
				$deleted = AT_Audit::delete_entries_older_than($days);
				$this->render_message(sprintf(__('已清理 %d 条%d天前的日志记录', 'audit-trail'), $deleted, $days));
			}
		}
		
		// 处理按操作类型批量删除 - 移到这里以确保独立处理
		if (isset($_POST['delete_by_operation']) && check_admin_referer( 'audittrail-update_options' )) {
			if (isset($_POST['operation_type']) && !empty($_POST['operation_type'])) {
				$operation = sanitize_text_field($_POST['operation_type']);
				if (!empty($operation)) {
					$deleted = AT_Audit::delete_by_operation($operation);
					$this->render_message(sprintf(__('已删除 %d 条"%s"类型的日志记录', 'audit-trail'), $deleted, $operation));
				}
			} else {
				$this->render_message(__('请选择要删除的操作类型', 'audit-trail'));
			}
		}

		$current = get_option( 'audit_methods' );
		if ( !is_array( $current) )
			$current = array();

		$methods = apply_filters( 'audit_collect', array() );
		if ( is_array( $methods) )
			ksort( $methods);

		$error_log = get_option( 'audit_error_log' );

		$expiry = get_option( 'audit_expiry' );
		if ( $expiry === false)
			$expiry = 30;

		$forbidden_users = get_option( 'audit_forbidden_users', '' );

		$this->render( 'options', array( 
			'methods' => $methods, 
			'current' => $current, 
			'expiry' => $expiry, 
			'error_log' => $error_log, 
			'post' => get_option( 'audit_post' ), 
			'post_order' => get_option( 'audit_post_order' ), 
			'version' => get_option( 'audit_version' ) == 'false' ? false : true, 
			'ignore_users' => get_option( 'audit_ignore' ),
			'forbidden_users' => $forbidden_users,
			'saved' => $saved
		));
	}

	function admin_head() {
		wp_enqueue_style( 'audit-trail', plugin_dir_url( __FILE__ ).'admin.css' );
		
		// 确保在admin页面加载时应用中文语言
		self::apply_language_setting();
		
		// 添加安全头部，防止XSS攻击
		if (!headers_sent()) {
			header('X-XSS-Protection: 1; mode=block');
			header('X-Content-Type-Options: nosniff');
			header('X-Frame-Options: SAMEORIGIN');
			header('Referrer-Policy: strict-origin-when-cross-origin');
			header('Content-Security-Policy: default-src \'self\'; script-src \'self\' \'unsafe-inline\' \'unsafe-eval\' https://cdnjs.cloudflare.com https://code.jquery.com; style-src \'self\' \'unsafe-inline\' https://cdnjs.cloudflare.com; img-src \'self\' data:; font-src \'self\' data:;');
		}
	}

	function version() {
		$plugin_data = implode( '', file( __FILE__) );

		if ( preg_match( '|Version:(.*)|i', $plugin_data, $version) )
			return trim( $version[1]);
		return '';
	}

	private function render( $template, $template_vars = array() ) {
		foreach ( $template_vars AS $key => $val ) {
			$$key = $val;
		}

		if ( file_exists( dirname( __FILE__ )."/view/admin/$template.php" ) )
			include dirname( __FILE__ )."/view/admin/$template.php";
	}

	private function capture( $ug_name, $ug_vars = array() ) {
		ob_start();

		$this->render( $ug_name, $ug_vars );
		$output = ob_get_contents();

		ob_end_clean();
		return $output;
	}

	private function render_message( $message, $timeout = 0 ) {
		?>
<div class="updated" id="message" onclick="this.parentNode.removeChild(this)">
	<p><?php echo esc_html( $message ) ?></p>
</div>
	<?php
	}

	function process_options()
	{
		// 此函数已废弃，为避免混淆，内部逻辑已移至screen_options方法
		return;
	}

	/**
	 * 自动检查并修复数据库表
	 */
	function auto_repair_database() {
		global $wpdb;
		
		// 记录开始修复
		error_log('Audit Trail: 开始检查并修复数据库表 - ' . date('Y-m-d H:i:s'));
		
		try {
			// 调用静态方法确保表存在
			AT_Audit::install_tables();
			
			// 检查表是否可访问
			$table_name = $wpdb->prefix . 'audit_trail';
			$test_query = $wpdb->prepare("SELECT COUNT(*) FROM $table_name LIMIT 1");
			$result = $wpdb->get_var($test_query);
			
			// 如果有数据库错误，记录并尝试修复
			if ($wpdb->last_error) {
				error_log('Audit Trail: 数据库表访问错误: ' . $wpdb->last_error);
				
				// 尝试修复表
				$repair_result = $wpdb->query("REPAIR TABLE $table_name");
				error_log('Audit Trail: 修复表结果: ' . ($repair_result !== false ? '成功' : '失败'));
				
				// 尝试优化表
				$optimize_result = $wpdb->query("OPTIMIZE TABLE $table_name");
				error_log('Audit Trail: 优化表结果: ' . ($optimize_result !== false ? '成功' : '失败'));
				
				// 检查索引
				$this->check_and_repair_indexes($table_name);
			}
			
			// 记录修复完成
			error_log('Audit Trail: 数据库表检查和修复完成 - ' . date('Y-m-d H:i:s'));
			
		} catch (Exception $e) {
			error_log('Audit Trail: 数据库修复过程中出错: ' . $e->getMessage());
			
			// 尝试使用更直接的方式创建表
			try {
				$this->force_create_table();
			} catch (Exception $inner_e) {
				error_log('Audit Trail: 强制创建表失败: ' . $inner_e->getMessage());
			}
		}
	}
	
	/**
	 * 强制创建表 - 最后的尝试
	 */
	private function force_create_table() {
		global $wpdb;
		
		$table_name = $wpdb->prefix . 'audit_trail';
		$charset_collate = $wpdb->get_charset_collate();
		
		// 尝试删除可能存在但有问题的表
		$wpdb->query("DROP TABLE IF EXISTS $table_name");
		
		// 创建新表
		$sql = "CREATE TABLE $table_name (
			`id` int(11) NOT NULL auto_increment,
			`operation` varchar(40) NOT NULL default '',
			`user_id` int(11) NOT NULL,
			`ip` varchar(45) NOT NULL default '',
			`happened_at` datetime NOT NULL,
			`item_id` int(11) default NULL,
			`data` longtext,
			`title` varchar(100) default NULL,
			PRIMARY KEY (`id`)
		) $charset_collate;";
		
		$result = $wpdb->query($sql);
		
		if ($result === false) {
			throw new Exception('强制创建表失败: ' . $wpdb->last_error);
		}
		
		// 添加索引
		$this->check_and_repair_indexes($table_name);
	}
	
	/**
	 * 检查并修复表索引
	 */
	function check_and_repair_indexes($table_name) {
		global $wpdb;
		
		// 检查必要的索引是否存在
		$indexes = $wpdb->get_results("SHOW INDEX FROM $table_name");
		$index_names = array();
		
		if ($indexes) {
			foreach ($indexes as $index) {
				$index_names[] = $index->Key_name;
			}
		}
		
		// 添加缺失的索引
		$required_indexes = array(
			'idx_operation' => "ALTER TABLE $table_name ADD INDEX idx_operation (operation)",
			'idx_user_id' => "ALTER TABLE $table_name ADD INDEX idx_user_id (user_id)",
			'idx_happened_at' => "ALTER TABLE $table_name ADD INDEX idx_happened_at (happened_at)",
			'idx_ip' => "ALTER TABLE $table_name ADD INDEX idx_ip (ip)"
		);
		
		foreach ($required_indexes as $index_name => $sql) {
			if (!in_array($index_name, $index_names)) {
				$wpdb->query($sql);
				error_log("Audit Trail: 添加缺失的索引 $index_name");
			}
		}
	}

	/**
	 * 执行数据库诊断，检查是否可以连接数据库和创建表
	 */
	function database_diagnostic() {
		global $wpdb;
		
		echo '<div class="wrap">';
		echo '<h1>Audit Trail - 数据库诊断</h1>';
		
		// 检查数据库连接
		echo '<h2>数据库连接状态</h2>';
		echo '<ul>';
		
		// 获取数据库信息
		try {
			$db_version = $wpdb->get_var("SELECT VERSION()");
			echo '<li style="color:green">✓ 成功连接到数据库，版本：' . esc_html($db_version) . '</li>';
			
			// 数据库权限检查
			echo '<li>数据库用户：' . esc_html(DB_USER) . '@' . esc_html(DB_HOST) . '</li>';
			echo '<li>数据库名：' . esc_html(DB_NAME) . '</li>';
			echo '<li>表前缀：' . esc_html($wpdb->prefix) . '</li>';
			
			// 检查表的创建权限
			$test_table = $wpdb->prefix . 'audit_trail_test';
			$wpdb->query("DROP TABLE IF EXISTS $test_table");
			$create_result = $wpdb->query("CREATE TABLE $test_table (id INT NOT NULL AUTO_INCREMENT PRIMARY KEY, test VARCHAR(50))");
			
			if ($create_result !== false) {
				echo '<li style="color:green">✓ 当前用户具有创建表的权限</li>';
				$wpdb->query("DROP TABLE IF EXISTS $test_table");
			} else {
				echo '<li style="color:red">✗ 当前用户可能没有创建表的权限：' . esc_html($wpdb->last_error) . '</li>';
			}
		} catch (Exception $e) {
			echo '<li style="color:red">✗ 连接数据库时出错：' . esc_html($e->getMessage()) . '</li>';
		}
		
		echo '</ul>';
		
		// 检查审计表状态
		echo '<h2>审计表状态</h2>';
		echo '<ul>';
		
		$table_name = $wpdb->prefix . 'audit_trail';
		$table_exists = $wpdb->get_var("SHOW TABLES LIKE '$table_name'") === $table_name;
		
		if ($table_exists) {
			echo '<li style="color:green">✓ 审计表 ' . esc_html($table_name) . ' 已存在</li>';
			
			// 检查表结构
			echo '<h3>表结构</h3>';
			echo '<table class="widefat" style="width:100%;">';
			echo '<thead><tr><th>字段</th><th>类型</th><th>是否为空</th><th>默认值</th><th>其他</th></tr></thead>';
			echo '<tbody>';
			
			$columns = $wpdb->get_results("SHOW COLUMNS FROM $table_name");
			if ($columns) {
				foreach ($columns as $column) {
					echo '<tr>';
					echo '<td>' . esc_html($column->Field) . '</td>';
					echo '<td>' . esc_html($column->Type) . '</td>';
					echo '<td>' . esc_html($column->Null) . '</td>';
					echo '<td>' . esc_html($column->Default) . '</td>';
					echo '<td>' . esc_html($column->Extra) . '</td>';
					echo '</tr>';
				}
			} else {
				echo '<tr><td colspan="5">无法获取表结构</td></tr>';
			}
			
			echo '</tbody></table>';
			
			// 检查索引
			echo '<h3>表索引</h3>';
			echo '<table class="widefat" style="width:100%;">';
			echo '<thead><tr><th>索引名</th><th>列名</th><th>是否唯一</th></tr></thead>';
			echo '<tbody>';
			
			$indexes = $wpdb->get_results("SHOW INDEX FROM $table_name");
			if ($indexes) {
				foreach ($indexes as $index) {
					echo '<tr>';
					echo '<td>' . esc_html($index->Key_name) . '</td>';
					echo '<td>' . esc_html($index->Column_name) . '</td>';
					echo '<td>' . (esc_html($index->Non_unique) == '0' ? '是' : '否') . '</td>';
					echo '</tr>';
				}
			} else {
				echo '<tr><td colspan="3">无法获取表索引</td></tr>';
			}
			
			echo '</tbody></table>';
			
			// 表数据统计
			$record_count = $wpdb->get_var("SELECT COUNT(*) FROM $table_name");
			echo '<p>表中共有 ' . intval($record_count) . ' 条记录</p>';
			
			// 提供修复选项
			echo '<form method="post">';
			wp_nonce_field('audit_trail_repair_table');
			echo '<p><input type="submit" name="repair_table" value="修复并优化表" class="button button-primary" /> ';
			echo '<input type="submit" name="recreate_table" value="重新创建表（会删除所有数据）" class="button button-secondary" onclick="return confirm(\'警告：这将删除所有审计日志数据，确定要继续吗？\');" /></p>';
			echo '</form>';
			
			// 处理表修复请求
			if (isset($_POST['repair_table']) && check_admin_referer('audit_trail_repair_table')) {
				// 尝试修复表
				$repair_result = $wpdb->query("REPAIR TABLE $table_name");
				$optimize_result = $wpdb->query("OPTIMIZE TABLE $table_name");
				
				if ($repair_result !== false) {
					echo '<div class="notice notice-success"><p>表修复操作成功完成</p></div>';
				} else {
					echo '<div class="notice notice-error"><p>表修复操作失败: ' . esc_html($wpdb->last_error) . '</p></div>';
				}
				
				// 检查并添加缺失的索引
				echo '<div class="notice notice-info"><p>检查并添加缺失的索引...</p></div>';
				$this->check_and_repair_indexes($table_name);
			}
			
			// 处理表重建请求
			if (isset($_POST['recreate_table']) && check_admin_referer('audit_trail_repair_table')) {
				// 删除旧表并重新创建
				$wpdb->query("DROP TABLE IF EXISTS $table_name");
				AT_Audit::install_tables();
				
				// 检查表是否创建成功
				$table_exists = $wpdb->get_var("SHOW TABLES LIKE '$table_name'") === $table_name;
				if ($table_exists) {
					echo '<div class="notice notice-success"><p>表已成功重新创建</p></div>';
					echo '<p><a href="' . admin_url('tools.php?page=audit-trail.php&diagnostic=1') . '" class="button">刷新诊断结果</a></p>';
				} else {
					echo '<div class="notice notice-error"><p>表重新创建失败</p></div>';
				}
			}
			
		} else {
			echo '<li style="color:red">✗ 审计表 ' . esc_html($table_name) . ' 不存在</li>';
			
			// 提供表创建选项
			echo '<form method="post">';
			wp_nonce_field('audit_trail_create_table');
			echo '<p><input type="submit" name="create_table" value="创建审计表" class="button button-primary" /></p>';
			echo '</form>';
			
			// 处理表创建请求
			if (isset($_POST['create_table']) && check_admin_referer('audit_trail_create_table')) {
				// 尝试创建表
				AT_Audit::install_tables();
				
				// 检查表是否创建成功
				$table_exists = $wpdb->get_var("SHOW TABLES LIKE '$table_name'") === $table_name;
				if ($table_exists) {
					echo '<div class="notice notice-success"><p>表已成功创建</p></div>';
					echo '<p><a href="' . admin_url('tools.php?page=audit-trail.php&diagnostic=1') . '" class="button">刷新诊断结果</a></p>';
				} else {
					echo '<div class="notice notice-error"><p>表创建失败</p></div>';
					
					// 尝试强制创建
					try {
						$this->force_create_table();
						$table_exists = $wpdb->get_var("SHOW TABLES LIKE '$table_name'") === $table_name;
						if ($table_exists) {
							echo '<div class="notice notice-success"><p>表已通过强制方法成功创建</p></div>';
							echo '<p><a href="' . admin_url('tools.php?page=audit-trail.php&diagnostic=1') . '" class="button">刷新诊断结果</a></p>';
						} else {
							echo '<div class="notice notice-error"><p>多次尝试后仍无法创建表，请检查数据库权限</p></div>';
						}
					} catch (Exception $e) {
						echo '<div class="notice notice-error"><p>强制创建表时出错: ' . esc_html($e->getMessage()) . '</p></div>';
					}
				}
			}
		}
		
		echo '</ul>';
		
		// PHP信息
		echo '<h2>PHP和系统信息</h2>';
		echo '<table class="widefat" style="width:100%;">';
		echo '<tr><td>PHP版本</td><td>' . esc_html(PHP_VERSION) . '</td></tr>';
		echo '<tr><td>内存限制</td><td>' . esc_html(ini_get('memory_limit')) . '</td></tr>';
		echo '<tr><td>最大执行时间</td><td>' . esc_html(ini_get('max_execution_time')) . ' 秒</td></tr>';
		echo '<tr><td>WordPress版本</td><td>' . esc_html(get_bloginfo('version')) . '</td></tr>';
		echo '<tr><td>Audit Trail插件版本</td><td>' . esc_html(AUDIT_TRAIL_VERSION) . '</td></tr>';
		echo '<tr><td>服务器软件</td><td>' . esc_html($_SERVER['SERVER_SOFTWARE'] ?? 'unknown') . '</td></tr>';
		echo '</table>';
		
		// 插件文件检查
		echo '<h2>插件文件状态</h2>';
		echo '<ul>';
		
		$required_files = array(
			'audit-trail.php' => __FILE__,
			'models/audit.php' => dirname(__FILE__) . '/models/audit.php',
			'models/auditor.php' => dirname(__FILE__) . '/models/auditor.php',
			'models/pager.php' => dirname(__FILE__) . '/models/pager.php',
			'models/batch-logger.php' => dirname(__FILE__) . '/models/batch-logger.php'
		);
		
		foreach ($required_files as $name => $path) {
			if (file_exists($path)) {
				echo '<li style="color:green">✓ ' . esc_html($name) . ' 存在 (' . esc_html(filesize($path)) . ' 字节)</li>';
			} else {
				echo '<li style="color:red">✗ ' . esc_html($name) . ' 不存在或不可读</li>';
			}
		}
		
		echo '</ul>';
		
		// 返回链接
		echo '<p><a href="' . admin_url('tools.php?page=audit-trail.php') . '" class="button">返回审计页面</a></p>';
		
		echo '</div>';
	}
}

/**
 * Standard plugin setup
 **/
register_activation_hook( __FILE__, array( 'Audit_Trail', 'plugin_activated' ) );
register_deactivation_hook( __FILE__, array( 'Audit_Trail', 'plugin_deactivated' ) );

// 修复因钩子错误导致的问题
remove_action('shutdown', ['AuditTrailBatchLogger', 'flush']);

// 处理可能存在的类名冲突
if (!class_exists('AT_Batch_Logger') && class_exists('AuditTrailBatchLogger')) {
    class_alias('AuditTrailBatchLogger', 'AT_Batch_Logger');
}

// Instantiate
add_action( 'plugins_loaded', array( 'Audit_Trail', 'init' ) );
