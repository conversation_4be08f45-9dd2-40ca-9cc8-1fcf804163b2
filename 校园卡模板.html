<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>广州大学校园卡模板</title>
    <style>
        body {
            font-family: "Microsoft YaHei", sans-serif;
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100vh;
            background-color: #f5f5f5;
            margin: 0;
        }
        
        .card-container {
            width: 420px;
            height: 260px;
            background: linear-gradient(to right, #1a6fc4, #2e8ae6);
            border-radius: 12px;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
            position: relative;
            overflow: hidden;
        }
        
        .card-header {
            display: flex;
            justify-content: space-between;
            padding: 15px 20px;
            color: white;
            position: relative;
        }
        
        .logo-area {
            width: 50px;
            height: 50px;
            background-color: rgba(255, 255, 255, 0.8);
            border-radius: 50%;
            display: flex;
            justify-content: center;
            align-items: center;
            margin-right: 10px;
            cursor: pointer;
            position: relative;
            overflow: hidden;
        }
        
        .logo-placeholder {
            font-size: 12px;
            color: #666;
            text-align: center;
        }
        
        .logo-preview {
            width: 100%;
            height: 100%;
            object-fit: cover;
            display: none;
        }
        
        .school-info {
            display: flex;
            flex-direction: column;
        }
        
        .school-name {
            font-size: 24px;
            font-weight: bold;
        }
        
        .card-type {
            font-size: 18px;
        }
        
        .card-id {
            position: absolute;
            top: 40px;
            right: 20px;
            font-size: 14px;
        }
        
        .eng-name {
            font-size: 12px;
            opacity: 0.8;
            margin-top: 2px;
        }
        
        .card-content {
            display: flex;
            padding: 10px 20px;
        }
        
        .photo-area {
            width: 85px;
            height: 120px;
            background-color: #f0f0f0;
            border: 1px solid #ddd;
            position: relative;
            cursor: pointer;
        }
        
        .photo-placeholder {
            width: 100%;
            height: 100%;
            display: flex;
            justify-content: center;
            align-items: center;
            color: #666;
            font-size: 12px;
            text-align: center;
        }
        
        .photo-preview {
            width: 100%;
            height: 100%;
            object-fit: cover;
            display: none;
        }
        
        .info-area {
            flex-grow: 1;
            padding-left: 20px;
            display: flex;
            flex-direction: column;
            justify-content: center;
        }
        
        .info-details {
            margin-left: 15px;
        }
        
        .building-graphic {
            position: absolute;
            width: 200px;
            height: 100px;
            right: 20px;
            top: 80px;
            opacity: 0.2;
            pointer-events: none;
        }
        
        .card-footer {
            background-color: #f0f0f0;
            padding: 15px 20px;
            position: absolute;
            bottom: 0;
            width: 100%;
            box-sizing: border-box;
            display: flex;
            justify-content: flex-end;
        }
        
        .info-row {
            margin-bottom: 8px;
            display: flex;
        }
        
        .info-label {
            width: 60px;
            font-weight: bold;
        }
        
        .info-value {
            border: none;
            border-bottom: 1px solid #ccc;
            background: transparent;
            width: 150px;
        }
        
        .footer-logo {
            position: absolute;
            right: 20px;
            bottom: 15px;
            width: 40px;
            height: 40px;
            opacity: 0.7;
        }
        
        #photo-input, #logo-input {
            display: none;
        }
        
        .download-btn {
            margin-top: 20px;
            padding: 10px 20px;
            background-color: #1a6fc4;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
        }
        
        .download-btn:hover {
            background-color: #0d5ca8;
        }
    </style>
</head>
<body>
    <div style="text-align: center;">
        <div class="card-container" id="card">
            <div class="card-header">
                <div style="display: flex; align-items: center;">
                    <label for="logo-input" class="logo-area">
                        <div class="logo-placeholder" id="logo-placeholder">点击上传图标</div>
                        <img id="logo-preview" class="logo-preview">
                    </label>
                    <input type="file" id="logo-input" accept="image/*">
                    
                    <div class="school-info">
                        <div class="school-name">广州大学</div>
                        <div class="eng-name">GUANGZHOU UNIVERSITY</div>
                    </div>
                </div>
                <div>
                    <div class="card-id">23105618</div>
                    <div class="card-type">学生卡</div>
                </div>
            </div>
            
            <div class="card-content">
                <label for="photo-input" class="photo-area">
                    <div class="photo-placeholder" id="placeholder">点击上传照片</div>
                    <img id="photo-preview" class="photo-preview">
                </label>
                <input type="file" id="photo-input" accept="image/*">
                
                <div class="info-area">
                    <div class="info-details">
                        <div class="info-row">
                            <div class="info-label">姓名：</div>
                            <input type="text" class="info-value" id="name-input" placeholder="输入姓名">
                        </div>
                        <div class="info-row">
                            <div class="info-label">学号：</div>
                            <input type="text" class="info-value" id="id-input" placeholder="输入学号">
                        </div>
                        <div class="info-row">
                            <div class="info-label">学院：</div>
                            <input type="text" class="info-value" id="faculty-input" placeholder="输入学院">
                        </div>
                    </div>
                    
                    <div class="building-graphic">
                        <!-- 简化的建筑线条图 -->
                        <svg width="200" height="100" viewBox="0 0 200 100" xmlns="http://www.w3.org/2000/svg">
                            <path d="M40,80 L40,30 L60,30 L60,80" stroke="white" fill="none" stroke-width="1.5"/>
                            <path d="M80,80 L80,20 L100,10 L120,20 L120,80" stroke="white" fill="none" stroke-width="1.5"/>
                            <path d="M140,80 L140,40 L180,40 L180,80" stroke="white" fill="none" stroke-width="1.5"/>
                            <rect x="150" y="50" width="20" height="30" stroke="white" fill="none" stroke-width="1.5"/>
                            <circle cx="90" cy="50" r="5" stroke="white" fill="none" stroke-width="1.5"/>
                        </svg>
                    </div>
                </div>
            </div>
            
            <div class="card-footer">
                <div class="footer-logo">
                    <!-- 简化的校徽 -->
                    <svg width="40" height="40" viewBox="0 0 40 40" xmlns="http://www.w3.org/2000/svg">
                        <circle cx="20" cy="20" r="18" fill="#1a6fc4" opacity="0.6"/>
                        <path d="M15,15 C15,12 20,8 25,15 C30,22 18,28 15,15 Z" fill="white" opacity="0.7"/>
                    </svg>
                </div>
            </div>
        </div>
        
        <button class="download-btn" id="download-btn">保存卡片图片</button>
    </div>

    <script src="https://html2canvas.hertzen.com/dist/html2canvas.min.js"></script>
    <script>
        // 照片上传预览
        const photoInput = document.getElementById('photo-input');
        const photoPreview = document.getElementById('photo-preview');
        const placeholder = document.getElementById('placeholder');
        
        photoInput.addEventListener('change', function(e) {
            const file = e.target.files[0];
            if (file) {
                const reader = new FileReader();
                reader.onload = function(event) {
                    photoPreview.src = event.target.result;
                    photoPreview.style.display = 'block';
                    photoPreview.style.objectFit = 'cover';
                    placeholder.style.display = 'none';
                };
                reader.readAsDataURL(file);
            }
        });
        
        // 徽标上传预览
        const logoInput = document.getElementById('logo-input');
        const logoPreview = document.getElementById('logo-preview');
        const logoPlaceholder = document.getElementById('logo-placeholder');
        
        logoInput.addEventListener('change', function(e) {
            const file = e.target.files[0];
            if (file) {
                const reader = new FileReader();
                reader.onload = function(event) {
                    logoPreview.src = event.target.result;
                    logoPreview.style.display = 'block';
                    logoPlaceholder.style.display = 'none';
                };
                reader.readAsDataURL(file);
            }
        });
        
        // 保存卡片为图片
        document.getElementById('download-btn').addEventListener('click', function() {
            html2canvas(document.getElementById('card')).then(canvas => {
                const link = document.createElement('a');
                link.download = '广州大学校园卡.png';
                link.href = canvas.toDataURL('image/png');
                link.click();
            });
        });
    </script>
</body>
</html> 