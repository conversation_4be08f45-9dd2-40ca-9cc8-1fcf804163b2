# Nginx 网站性能优化配置
# 这个配置文件包含了提升网站速度的各种优化设置

# 主配置块
http {
    # 基础设置
    sendfile on;
    tcp_nopush on;
    tcp_nodelay on;
    keepalive_timeout 65;
    types_hash_max_size 2048;
    
    # 隐藏Nginx版本信息（安全考虑）
    server_tokens off;
    
    # 设置客户端请求体大小限制
    client_max_body_size 100M;
    client_body_buffer_size 128k;
    
    # 设置客户端头部大小限制
    client_header_buffer_size 1k;
    large_client_header_buffers 4 4k;
    
    # 超时设置
    client_body_timeout 12;
    client_header_timeout 12;
    send_timeout 10;
    
    # Gzip 压缩配置（重要！）
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_proxied any;
    gzip_comp_level 6;
    gzip_types
        text/plain
        text/css
        text/xml
        text/javascript
        text/json
        application/javascript
        application/xml+rss
        application/json
        application/ld+json
        application/atom+xml
        image/svg+xml;
    
    # Brotli 压缩配置（如果支持）
    # brotli on;
    # brotli_comp_level 6;
    # brotli_types text/plain text/css application/json application/javascript text/xml application/xml application/xml+rss text/javascript;
    
    # 日志格式优化
    log_format main '$remote_addr - $remote_user [$time_local] "$request" '
                    '$status $body_bytes_sent "$http_referer" '
                    '"$http_user_agent" "$http_x_forwarded_for" '
                    '$request_time $upstream_response_time';
    
    # 虚拟主机配置
    server {
        listen 80;
        listen [::]:80;
        server_name example.com www.example.com;
        
        # HTTP 重定向到 HTTPS
        return 301 https://$server_name$request_uri;
    }
    
    # HTTPS 配置
    server {
        listen 443 ssl http2;
        listen [::]:443 ssl http2;
        server_name example.com www.example.com;
        
        # 网站根目录
        root /var/www/html;
        index index.html index.htm index.php;
        
        # SSL 证书配置
        ssl_certificate /path/to/certificate.crt;
        ssl_certificate_key /path/to/private.key;
        
        # SSL 优化配置
        ssl_protocols TLSv1.2 TLSv1.3;
        ssl_ciphers ECDHE-RSA-AES128-GCM-SHA256:ECDHE-RSA-AES256-GCM-SHA384:ECDHE-RSA-AES128-SHA256:ECDHE-RSA-AES256-SHA384;
        ssl_prefer_server_ciphers off;
        ssl_session_cache shared:SSL:10m;
        ssl_session_timeout 10m;
        
        # HSTS 安全头
        add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;
        
        # 其他安全头
        add_header X-Frame-Options DENY;
        add_header X-Content-Type-Options nosniff;
        add_header X-XSS-Protection "1; mode=block";
        
        # 访问日志
        access_log /var/log/nginx/access.log main;
        error_log /var/log/nginx/error.log;
        
        # 根目录处理
        location / {
            try_files $uri $uri/ /index.php?$query_string;
        }
        
        # 静态文件缓存配置（重要！）
        location ~* \.(jpg|jpeg|png|gif|ico|svg)$ {
            expires 1y;
            add_header Cache-Control "public, immutable";
            add_header Vary Accept-Encoding;
            access_log off;
        }
        
        # CSS 和 JS 文件缓存
        location ~* \.(css|js)$ {
            expires 1y;
            add_header Cache-Control "public, immutable";
            add_header Vary Accept-Encoding;
            access_log off;
        }
        
        # 字体文件缓存
        location ~* \.(woff|woff2|ttf|eot)$ {
            expires 1y;
            add_header Cache-Control "public, immutable";
            add_header Access-Control-Allow-Origin *;
            access_log off;
        }
        
        # HTML 文件缓存（较短时间）
        location ~* \.html$ {
            expires 1h;
            add_header Cache-Control "public, must-revalidate";
        }
        
        # API 接口不缓存
        location /api/ {
            expires -1;
            add_header Cache-Control "no-cache, no-store, must-revalidate";
            add_header Pragma "no-cache";
            
            # 如果是反向代理到后端
            # proxy_pass http://backend;
            # proxy_set_header Host $host;
            # proxy_set_header X-Real-IP $remote_addr;
            # proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            # proxy_set_header X-Forwarded-Proto $scheme;
        }
        
        # PHP 文件处理（如果使用 PHP）
        location ~ \.php$ {
            fastcgi_pass unix:/var/run/php/php7.4-fpm.sock;
            fastcgi_index index.php;
            fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
            include fastcgi_params;
            
            # PHP 缓存设置
            fastcgi_cache_valid 200 1h;
            fastcgi_cache_use_stale error timeout invalid_header http_500;
        }
        
        # 禁止访问隐藏文件
        location ~ /\. {
            deny all;
            access_log off;
            log_not_found off;
        }
        
        # 禁止访问备份文件
        location ~* \.(bak|backup|old|orig|original|tmp)$ {
            deny all;
            access_log off;
            log_not_found off;
        }
        
        # robots.txt 和 sitemap.xml
        location = /robots.txt {
            allow all;
            log_not_found off;
            access_log off;
        }
        
        location = /sitemap.xml {
            allow all;
            log_not_found off;
            access_log off;
        }
        
        # favicon.ico
        location = /favicon.ico {
            log_not_found off;
            access_log off;
            expires 1y;
            add_header Cache-Control "public, immutable";
        }
        
        # 图片防盗链（可选）
        location ~* \.(jpg|jpeg|png|gif)$ {
            valid_referers none blocked server_names *.example.com;
            if ($invalid_referer) {
                return 403;
            }
        }
        
        # 限制请求频率（防止恶意请求）
        location /login {
            limit_req zone=login burst=5 nodelay;
            # 其他配置...
        }
    }
    
    # 限制请求频率配置
    limit_req_zone $binary_remote_addr zone=login:10m rate=1r/s;
    limit_req_zone $binary_remote_addr zone=api:10m rate=10r/s;
    
    # 上游服务器配置（如果使用负载均衡）
    upstream backend {
        server 127.0.0.1:8080 weight=3;
        server 127.0.0.1:8081 weight=2;
        server 127.0.0.1:8082 weight=1 backup;
        
        # 健康检查
        # health_check;
        keepalive 32;
    }
}

# 事件配置
events {
    worker_connections 1024;
    use epoll;
    multi_accept on;
}

# 工作进程配置
worker_processes auto;
worker_rlimit_nofile 65535;

# PID 文件
pid /var/run/nginx.pid;

# 错误日志
error_log /var/log/nginx/error.log warn;
