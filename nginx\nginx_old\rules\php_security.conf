# PHP基本处理规则
location ~ \.php$ {
    try_files $uri =404;
    
    # 常规PHP文件处理
    fastcgi_split_path_info ^(.+\.php)(/.+)$;
    fastcgi_pass unix:/var/run/php/php7.4-fpm.sock;  # 根据您的PHP版本调整
    fastcgi_index index.php;
    include fastcgi_params;
    fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
    fastcgi_param PATH_INFO $fastcgi_path_info;
    
    # 隐藏PHP版本号
    fastcgi_hide_header X-Powered-By;
    proxy_hide_header X-Powered-By;
    
    # 安全响应头
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header Referrer-Policy "strict-origin-when-cross-origin" always;
    
    # 增加FastCGI超时时间
    fastcgi_read_timeout 300;
}

# 增加buffer size
fastcgi_buffers 16 16k;
fastcgi_buffer_size 32k; 