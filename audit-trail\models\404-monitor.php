<?php
/**
 * 404真实IP监控功能
 * 记录触发404的真实IP地址并排除CDN IP
 */

// 防止直接访问
if (!defined('ABSPATH')) {
    exit;
}

class AT_404_Monitor {
    // 单例实例
    private static $instance = null;
    
    // CDN IP缓存
    private $cdn_ips = null;
    
    /**
     * 获取单例实例
     */
    public static function get_instance() {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * 构造函数
     */
    private function __construct() {
        // 捕获404请求
        add_action('template_redirect', array($this, 'monitor_404_requests'), 1);
        
        // 注册定期持久化事件
        add_action('audit_404_persist_event', array($this, 'persist_404_stats'));
        if (!wp_next_scheduled('audit_404_persist_event')) {
            wp_schedule_event(time(), 'hourly', 'audit_404_persist_event');
        }
        
        // 注册定期清理事件
        add_action('audit_404_cleanup_event', array($this, 'cleanup_404_stats'));
        if (!wp_next_scheduled('audit_404_cleanup_event')) {
            wp_schedule_event(time(), 'daily', 'audit_404_cleanup_event');
        }
        
        // 添加管理页面
        add_action('admin_menu', array($this, 'add_admin_menu'));
    }
    
    /**
     * 监控404请求
     */
    public function monitor_404_requests() {
        // 只在404页面执行
        if (!is_404()) {
            return;
        }
        
        // 获取真实IP
        $ip = $this->get_real_ip_address();
        
        // 排除CDN IP
        if ($this->is_cdn_ip($ip)) {
            return;
        }
        
        // 更新统计
        $this->update_ip_stats($ip);
    }
    
    /**
     * 获取真实IP地址
     */
    private function get_real_ip_address() {
        // 可能包含真实IP的HTTP头
        $ip_headers = array(
            'HTTP_CF_CONNECTING_IP', // Cloudflare
            'HTTP_X_FORWARDED_FOR',  // 常见代理/CDN
            'HTTP_X_REAL_IP',        // Nginx代理
            'HTTP_CLIENT_IP',        // 客户端代理
            'REMOTE_ADDR'            // 直接连接
        );
        
        foreach ($ip_headers as $header) {
            if (!empty($_SERVER[$header])) {
                // X-Forwarded-For可能包含多个IP，取第一个
                if ($header == 'HTTP_X_FORWARDED_FOR') {
                    $ip_array = explode(',', $_SERVER[$header]);
                    $ip = trim($ip_array[0]);
                } else {
                    $ip = $_SERVER[$header];
                }
                
                // 验证是否为有效IP
                if (filter_var($ip, FILTER_VALIDATE_IP)) {
                    return $ip;
                }
            }
        }
        
        // 默认返回REMOTE_ADDR
        return $_SERVER['REMOTE_ADDR'];
    }
    
    /**
     * 检查IP是否为CDN IP
     */
    private function is_cdn_ip($ip) {
        // 初始化CDN IP列表
        if ($this->cdn_ips === null) {
            $this->cdn_ips = array(
                'quic' => array(),
                'cloudflare_ranges' => array()
            );
            
            // QuicCloud CDN IP列表
            $this->cdn_ips['quic'] = array(
                '*************', '**************', '**************', '*************',
                '***************', '**************', '***************', '**************',
                '**************', '**************', '*************', '*************',
                '**************', '*************', '**************', '**************',
                '***************', '*************', '*************', '**************',
                '**************', '************', '***********', '************',
                '*************', '**************', '*************', '*************',
                '**************', '************', '************', '***************',
                '**************', '**************', '**************', '**************',
                '**************', '**************', '***************', '*************',
                '**************', '**************', '*************', '**************',
                '************4', '176.9.114.118', '178.17.171.177', '178.22.124.247',
                '178.22.124.251', '178.255.220.12', '18.192.146.200', '185.116.60.231',
                '185.116.60.232', '185.126.237.51', '185.186.78.89', '185.212.169.91',
                '185.228.26.40', '185.231.233.130', '185.53.57.40', '185.53.57.89',
                '188.172.228.182', '188.172.229.113', '188.64.184.71', '190.92.176.5',
                '191.96.101.140', '192.248.156.201', '192.248.191.135', '192.99.38.117',
                '193.203.191.189', '194.36.144.221', '195.137.220.243', '195.231.17.141',
                '199.247.28.91', '199.59.247.242', '199.71.214.15', '201.182.97.70',
                '202.61.226.253', '204.10.163.237', '209.124.84.191', '209.208.26.218',
                '211.23.143.87', '213.159.1.75', '213.183.48.170', '213.184.85.245',
                '216.128.179.195', '216.238.104.48', '216.238.71.13', '23.160.56.125',
                '31.131.4.244', '31.22.115.186', '31.40.212.152', '34.247.229.180',
                '34.249.110.197', '38.114.121.40', '38.54.30.228', '38.54.79.187',
                '38.60.253.237', '41.185.29.210', '41.223.52.170', '45.124.65.86',
                '45.248.77.61', '45.32.123.201', '45.32.183.112', '45.32.67.144',
                '45.32.77.223', '45.63.67.181', '45.76.247.71', '45.76.252.131',
                '45.77.148.74', '45.77.165.216', '45.77.51.171', '46.250.220.133',
                '49.12.102.29', '5.134.119.103', '5.134.119.194', '51.158.202.109',
                '51.81.186.219', '51.81.33.156', '54.246.224.74', '54.36.103.97',
                '61.219.247.87', '61.219.247.90', '64.176.165.8', '64.176.4.251',
                '64.227.16.93', '65.108.104.232', '65.109.39.175', '6***********',
                '***********', '***********', '*************', '************',
                '*************', '************', '**************', '*************',
                '*************', '*************', '*************', '**************',
                '**********', '*************', '*************', '***********',
                '*************', '************', '*************', '**************',
                '**************'
            );
            
            // Cloudflare IP范围
            $this->cdn_ips['cloudflare_ranges'] = array(
                '************/20', '************/22', '************/22', '**********/22',
                '************/18', '*************/18', '************/20', '************/20',
                '*************/22', '************/17', '***********/15', '**********/13',
                '**********/14', '**********/13', '**********/22'
            );
        }
        
        // 检查是否在QuicCloud IP列表中
        if (in_array($ip, $this->cdn_ips['quic'])) {
            return true;
        }
        
        // 检查是否在Cloudflare IP范围内
        $ip_long = ip2long($ip);
        if ($ip_long !== false) {
            foreach ($this->cdn_ips['cloudflare_ranges'] as $range) {
                list($subnet, $mask) = explode('/', $range);
                $subnet_long = ip2long($subnet);
                $mask_long = ~((1 << (32 - $mask)) - 1);
                
                if (($ip_long & $mask_long) == ($subnet_long & $mask_long)) {
                    return true;
                }
            }
        }
        
        return false;
    }
    
    /**
     * 更新IP统计
     */
    private function update_ip_stats($ip) {
        // 获取缓存的404统计数据
        $stats = get_transient('audit_404_ip_stats');
        if (!$stats) {
            $stats = array();
        }
        
        // 更新统计
        if (!isset($stats[$ip])) {
            $stats[$ip] = array(
                'count' => 1,
                'last_time' => time()
            );
        } else {
            $stats[$ip]['count']++;
            $stats[$ip]['last_time'] = time();
        }
        
        // 缓存5分钟，减少写入频率
        set_transient('audit_404_ip_stats', $stats, 5 * MINUTE_IN_SECONDS);
        
        // 检查是否需要立即持久化存储（如果请求量突然增大）
        if (isset($stats[$ip]['count']) && $stats[$ip]['count'] % 50 === 0) {
            $this->persist_404_stats();
        }
    }
    
    /**
     * 将缓存的统计数据持久化到数据库
     */
    public function persist_404_stats() {
        $stats = get_transient('audit_404_ip_stats');
        if (!$stats) {
            return;
        }
        
        // 获取现有的持久化数据
        $persist_stats = get_option('audit_404_ip_stats', array());
        
        // 合并数据
        foreach ($stats as $ip => $data) {
            if (!isset($persist_stats[$ip])) {
                $persist_stats[$ip] = $data;
            } else {
                $persist_stats[$ip]['count'] += $data['count'];
                $persist_stats[$ip]['last_time'] = max($persist_stats[$ip]['last_time'], $data['last_time']);
            }
        }
        
        // 限制记录的IP数量，保留最活跃的1000个
        if (count($persist_stats) > 1000) {
            // 按计数排序
            uasort($persist_stats, function($a, $b) {
                return $b['count'] - $a['count'];
            });
            $persist_stats = array_slice($persist_stats, 0, 1000, true);
        }
        
        // 更新持久化数据
        update_option('audit_404_ip_stats', $persist_stats);
        update_option('audit_404_last_persist', time());
        
        // 清除临时缓存
        delete_transient('audit_404_ip_stats');
    }
    
    /**
     * 清理过期数据
     */
    public function cleanup_404_stats() {
        $persist_stats = get_option('audit_404_ip_stats', array());
        if (empty($persist_stats)) {
            return;
        }
        
        $cutoff_time = time() - (30 * DAY_IN_SECONDS); // 保留30天数据
        
        foreach ($persist_stats as $ip => $data) {
            if ($data['last_time'] < $cutoff_time) {
                unset($persist_stats[$ip]);
            }
        }
        
        update_option('audit_404_ip_stats', $persist_stats);
    }
    
    /**
     * 添加管理菜单
     */
    public function add_admin_menu() {
        add_submenu_page(
            'tools.php',
            '404 IP监控',
            '404 IP监控',
            'manage_options',
            'audit-404-monitor',
            array($this, 'render_admin_page')
        );
    }
    
    /**
     * 渲染管理页面
     */
    public function render_admin_page() {
        // 处理清除统计数据的请求
        if (isset($_POST['clear_404_stats']) && check_admin_referer('clear_404_stats')) {
            delete_option('audit_404_ip_stats');
            delete_transient('audit_404_ip_stats');
            echo '<div class="notice notice-success"><p>404统计数据已清除</p></div>';
        }
        
        // 获取统计数据
        $stats = get_option('audit_404_ip_stats', array());
        
        // 排序（默认按计数降序）
        $sort_by = isset($_GET['sort']) ? $_GET['sort'] : 'count';
        $sort_order = isset($_GET['order']) && $_GET['order'] == 'asc' ? 'asc' : 'desc';
        
        if ($sort_by === 'ip') {
            // IP地址排序
            $keys = array_keys($stats);
            if ($sort_order === 'asc') {
                sort($keys);
            } else {
                rsort($keys);
            }
            $sorted_stats = array();
            foreach ($keys as $key) {
                $sorted_stats[$key] = $stats[$key];
            }
            $stats = $sorted_stats;
        } else {
            // 按计数或时间排序
            uasort($stats, function($a, $b) use ($sort_by, $sort_order) {
                if ($sort_by === 'last_time') {
                    $result = $a['last_time'] - $b['last_time'];
                } else { // count
                    $result = $a['count'] - $b['count'];
                }
                return $sort_order === 'asc' ? $result : -$result;
            });
        }
        
        // 分页
        $per_page = 50;
        $current_page = isset($_GET['paged']) ? max(1, intval($_GET['paged'])) : 1;
        $total_items = count($stats);
        $total_pages = ceil($total_items / $per_page);
        
        // 截取当前页数据
        $stats = array_slice($stats, ($current_page - 1) * $per_page, $per_page, true);
        
        // 输出统计表格
        ?>
        <div class="wrap">
            <h1>404 IP监控统计</h1>
            
            <form method="post">
                <?php wp_nonce_field('clear_404_stats'); ?>
                <input type="submit" name="clear_404_stats" class="button" value="清除统计数据" onclick="return confirm('确定要清除所有404统计数据吗？');">
            </form>
            
            <div class="tablenav top">
                <div class="tablenav-pages">
                    <span class="displaying-num"><?php echo esc_html($total_items); ?> 个项目</span>
                    <?php if ($total_pages > 1): ?>
                    <span class="pagination-links">
                        <?php
                        // 构建分页URL
                        $base_url = add_query_arg(array(
                            'page' => 'audit-404-monitor',
                            'sort' => $sort_by,
                            'order' => $sort_order
                        ), admin_url('tools.php'));
                        
                        // 首页链接
                        if ($current_page > 1) {
                            echo '<a class="first-page button" href="' . esc_url(add_query_arg('paged', 1, $base_url)) . '"><span>«</span></a>';
                        } else {
                            echo '<span class="first-page button disabled">«</span>';
                        }
                        
                        // 上一页链接
                        if ($current_page > 1) {
                            echo '<a class="prev-page button" href="' . esc_url(add_query_arg('paged', $current_page - 1, $base_url)) . '"><span>‹</span></a>';
                        } else {
                            echo '<span class="prev-page button disabled">‹</span>';
                        }
                        
                        echo '<span class="paging-input">' . $current_page . ' / ' . $total_pages . '</span>';
                        
                        // 下一页链接
                        if ($current_page < $total_pages) {
                            echo '<a class="next-page button" href="' . esc_url(add_query_arg('paged', $current_page + 1, $base_url)) . '"><span>›</span></a>';
                        } else {
                            echo '<span class="next-page button disabled">›</span>';
                        }
                        
                        // 末页链接
                        if ($current_page < $total_pages) {
                            echo '<a class="last-page button" href="' . esc_url(add_query_arg('paged', $total_pages, $base_url)) . '"><span>»</span></a>';
                        } else {
                            echo '<span class="last-page button disabled">»</span>';
                        }
                        ?>
                    </span>
                    <?php endif; ?>
                </div>
            </div>
            
            <table class="widefat striped">
                <thead>
                    <tr>
                        <th>
                            <a href="<?php echo esc_url(add_query_arg(array('sort' => 'ip', 'order' => ($sort_by == 'ip' && $sort_order == 'asc') ? 'desc' : 'asc'), remove_query_arg('paged'))); ?>">
                                IP地址 <?php echo $sort_by == 'ip' ? ($sort_order == 'asc' ? '↑' : '↓') : ''; ?>
                            </a>
                        </th>
                        <th>
                            <a href="<?php echo esc_url(add_query_arg(array('sort' => 'count', 'order' => ($sort_by == 'count' && $sort_order == 'asc') ? 'desc' : 'asc'), remove_query_arg('paged'))); ?>">
                                404请求次数 <?php echo $sort_by == 'count' ? ($sort_order == 'asc' ? '↑' : '↓') : ''; ?>
                            </a>
                        </th>
                        <th>
                            <a href="<?php echo esc_url(add_query_arg(array('sort' => 'last_time', 'order' => ($sort_by == 'last_time' && $sort_order == 'asc') ? 'desc' : 'asc'), remove_query_arg('paged'))); ?>">
                                最近请求时间 <?php echo $sort_by == 'last_time' ? ($sort_order == 'asc' ? '↑' : '↓') : ''; ?>
                            </a>
                        </th>
                    </tr>
                </thead>
                <tbody>
                    <?php if (empty($stats)): ?>
                    <tr>
                        <td colspan="3">暂无数据</td>
                    </tr>
                    <?php else: ?>
                    <?php foreach ($stats as $ip => $data): ?>
                    <tr>
                        <td><?php echo esc_html($ip); ?></td>
                        <td><?php echo esc_html($data['count']); ?></td>
                        <td><?php echo esc_html(date('Y-m-d H:i:s', $data['last_time'])); ?></td>
                    </tr>
                    <?php endforeach; ?>
                    <?php endif; ?>
                </tbody>
            </table>
            
            <?php if ($total_pages > 1): ?>
            <div class="tablenav bottom">
                <div class="tablenav-pages">
                    <span class="displaying-num"><?php echo esc_html($total_items); ?> 个项目</span>
                    <span class="pagination-links">
                        <?php
                        // 首页链接
                        if ($current_page > 1) {
                            echo '<a class="first-page button" href="' . esc_url(add_query_arg('paged', 1, $base_url)) . '"><span>«</span></a>';
                        } else {
                            echo '<span class="first-page button disabled">«</span>';
                        }
                        
                        // 上一页链接
                        if ($current_page > 1) {
                            echo '<a class="prev-page button" href="' . esc_url(add_query_arg('paged', $current_page - 1, $base_url)) . '"><span>‹</span></a>';
                        } else {
                            echo '<span class="prev-page button disabled">‹</span>';
                        }
                        
                        echo '<span class="paging-input">' . $current_page . ' / ' . $total_pages . '</span>';
                        
                        // 下一页链接
                        if ($current_page < $total_pages) {
                            echo '<a class="next-page button" href="' . esc_url(add_query_arg('paged', $current_page + 1, $base_url)) . '"><span>›</span></a>';
                        } else {
                            echo '<span class="next-page button disabled">›</span>';
                        }
                        
                        // 末页链接
                        if ($current_page < $total_pages) {
                            echo '<a class="last-page button" href="' . esc_url(add_query_arg('paged', $total_pages, $base_url)) . '"><span>»</span></a>';
                        } else {
                            echo '<span class="last-page button disabled">»</span>';
                        }
                        ?>
                    </span>
                </div>
            </div>
            <?php endif; ?>
        </div>
        <?php
    }
}

// 初始化404监控
function init_404_monitor() {
    AT_404_Monitor::get_instance();
}
add_action('plugins_loaded', 'init_404_monitor');