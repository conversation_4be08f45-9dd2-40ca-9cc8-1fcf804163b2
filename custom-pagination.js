/**
 * 自定义分页功能
 * 替换Fusion Builder的默认分页
 */

class CustomPagination {
    constructor(options = {}) {
        this.currentPage = options.currentPage || 1;
        this.totalPages = options.totalPages || 1;
        this.totalPosts = options.totalPosts || 0;
        this.postsPerPage = options.postsPerPage || 6;
        this.container = options.container || '.aobailei-news-blog';
        this.maxVisiblePages = options.maxVisiblePages || 7;
        
        this.init();
    }
    
    init() {
        this.createPaginationHTML();
        this.bindEvents();
        this.updateInfo();
    }
    
    createPaginationHTML() {
        const container = document.querySelector(this.container);
        if (!container) return;
        
        // 移除原有分页
        const existingPagination = container.querySelector('.pagination');
        if (existingPagination) {
            existingPagination.style.display = 'none';
        }
        
        // 创建自定义分页容器
        const paginationWrapper = document.createElement('div');
        paginationWrapper.className = 'custom-pagination-wrapper';
        paginationWrapper.innerHTML = this.generatePaginationHTML();
        
        container.appendChild(paginationWrapper);
    }
    
    generatePaginationHTML() {
        const startItem = (this.currentPage - 1) * this.postsPerPage + 1;
        const endItem = Math.min(this.currentPage * this.postsPerPage, this.totalPosts);
        
        return `
            <div class="pagination-info">
                总共 ${this.totalPosts} 条记录，第 ${startItem}-${endItem} 条
            </div>
            
            <div class="pagination-buttons">
                ${this.generatePageButtons()}
            </div>
            
            <div class="goto-page">
                <label>前往</label>
                <input type="number" min="1" max="${this.totalPages}" value="${this.currentPage}" class="goto-input">
                <label>页</label>
                <button class="goto-btn">确定</button>
            </div>
        `;
    }
    
    generatePageButtons() {
        let buttons = '';
        
        // 上一页按钮
        const prevDisabled = this.currentPage <= 1 ? 'disabled' : '';
        buttons += `<a href="#" class="pagination-btn prev-btn ${prevDisabled}" data-page="${this.currentPage - 1}">‹</a>`;
        
        // 页码按钮
        const pageNumbers = this.getVisiblePageNumbers();
        
        pageNumbers.forEach(page => {
            if (page === '...') {
                buttons += `<span class="pagination-ellipsis">...</span>`;
            } else {
                const current = page === this.currentPage ? 'current' : '';
                buttons += `<a href="#" class="pagination-btn ${current}" data-page="${page}">${page}</a>`;
            }
        });
        
        // 下一页按钮
        const nextDisabled = this.currentPage >= this.totalPages ? 'disabled' : '';
        buttons += `<a href="#" class="pagination-btn next-btn ${nextDisabled}" data-page="${this.currentPage + 1}">›</a>`;
        
        return buttons;
    }
    
    getVisiblePageNumbers() {
        const pages = [];
        const half = Math.floor(this.maxVisiblePages / 2);
        
        let start = Math.max(1, this.currentPage - half);
        let end = Math.min(this.totalPages, this.currentPage + half);
        
        // 调整范围以确保显示足够的页码
        if (end - start + 1 < this.maxVisiblePages) {
            if (start === 1) {
                end = Math.min(this.totalPages, start + this.maxVisiblePages - 1);
            } else {
                start = Math.max(1, end - this.maxVisiblePages + 1);
            }
        }
        
        // 添加第一页和省略号
        if (start > 1) {
            pages.push(1);
            if (start > 2) {
                pages.push('...');
            }
        }
        
        // 添加中间页码
        for (let i = start; i <= end; i++) {
            pages.push(i);
        }
        
        // 添加省略号和最后一页
        if (end < this.totalPages) {
            if (end < this.totalPages - 1) {
                pages.push('...');
            }
            pages.push(this.totalPages);
        }
        
        return pages;
    }
    
    bindEvents() {
        const wrapper = document.querySelector('.custom-pagination-wrapper');
        if (!wrapper) return;
        
        // 页码按钮点击事件
        wrapper.addEventListener('click', (e) => {
            if (e.target.classList.contains('pagination-btn') && !e.target.classList.contains('disabled')) {
                e.preventDefault();
                const page = parseInt(e.target.dataset.page);
                if (page && page !== this.currentPage) {
                    this.goToPage(page);
                }
            }
        });
        
        // 前往页面按钮事件
        const gotoBtn = wrapper.querySelector('.goto-btn');
        const gotoInput = wrapper.querySelector('.goto-input');
        
        if (gotoBtn && gotoInput) {
            gotoBtn.addEventListener('click', () => {
                const page = parseInt(gotoInput.value);
                if (page >= 1 && page <= this.totalPages && page !== this.currentPage) {
                    this.goToPage(page);
                }
            });
            
            gotoInput.addEventListener('keypress', (e) => {
                if (e.key === 'Enter') {
                    gotoBtn.click();
                }
            });
        }
    }
    
    goToPage(page) {
        if (page < 1 || page > this.totalPages || page === this.currentPage) {
            return;
        }
        
        // 显示加载状态
        this.showLoading();
        
        // 更新URL参数
        const url = new URL(window.location);
        if (page === 1) {
            url.searchParams.delete('paged');
        } else {
            url.searchParams.set('paged', page);
        }
        
        // 使用AJAX加载新内容或直接跳转
        if (this.useAjax) {
            this.loadPageContent(page, url.toString());
        } else {
            window.location.href = url.toString();
        }
    }
    
    loadPageContent(page, url) {
        // AJAX加载内容的实现
        fetch(url, {
            headers: {
                'X-Requested-With': 'XMLHttpRequest'
            }
        })
        .then(response => response.text())
        .then(html => {
            // 解析返回的HTML并更新内容
            const parser = new DOMParser();
            const doc = parser.parseFromString(html, 'text/html');
            const newContent = doc.querySelector(this.container);
            
            if (newContent) {
                const currentContainer = document.querySelector(this.container);
                currentContainer.innerHTML = newContent.innerHTML;
                
                // 更新分页
                this.currentPage = page;
                this.createPaginationHTML();
                
                // 滚动到内容顶部
                currentContainer.scrollIntoView({ behavior: 'smooth' });
            }
        })
        .catch(error => {
            console.error('加载页面内容失败:', error);
            // 降级到直接跳转
            window.location.href = url;
        })
        .finally(() => {
            this.hideLoading();
        });
    }
    
    showLoading() {
        const wrapper = document.querySelector('.custom-pagination-wrapper');
        if (wrapper) {
            wrapper.classList.add('pagination-loading');
        }
    }
    
    hideLoading() {
        const wrapper = document.querySelector('.custom-pagination-wrapper');
        if (wrapper) {
            wrapper.classList.remove('pagination-loading');
        }
    }
    
    updateInfo() {
        // 可以在这里添加更新信息的逻辑
    }
    
    // 公共方法：更新分页数据
    update(options) {
        Object.assign(this, options);
        this.createPaginationHTML();
    }
}

// 初始化分页
document.addEventListener('DOMContentLoaded', function() {
    // 从页面获取分页信息
    const getPaginationData = () => {
        // 这里需要根据实际情况获取分页数据
        // 可以从PHP传递到JavaScript，或者从DOM元素中读取
        
        const urlParams = new URLSearchParams(window.location.search);
        const currentPage = parseInt(urlParams.get('paged')) || 1;
        
        // 这些值需要从后端传递或者通过其他方式获取
        const totalPosts = window.paginationData?.totalPosts || 100; // 示例值
        const postsPerPage = window.paginationData?.postsPerPage || 6;
        const totalPages = Math.ceil(totalPosts / postsPerPage);
        
        return {
            currentPage,
            totalPages,
            totalPosts,
            postsPerPage
        };
    };
    
    // 检查是否存在目标容器
    if (document.querySelector('.aobailei-news-blog')) {
        const paginationData = getPaginationData();
        new CustomPagination(paginationData);
    }
});

// 导出类以供其他脚本使用
window.CustomPagination = CustomPagination;
