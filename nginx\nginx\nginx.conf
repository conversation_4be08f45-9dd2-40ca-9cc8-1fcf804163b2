user www-data;
worker_processes auto;
pid /run/nginx.pid;
error_log /var/log/nginx/error.log;
include /etc/nginx/modules-enabled/*.conf;

# 性能优化设置
worker_rlimit_nofile 20000;
worker_cpu_affinity auto;

events {
	worker_connections 2048;
	use epoll;
	multi_accept on;
}

http {
	# 导入真实IP配置
	include readip.conf;
	
	# 导入IP白名单配置
	include rules/whitelists.conf;

	##
	# Basic Settings
	##

	sendfile on;
	tcp_nopush on;
	tcp_nodelay on;
	types_hash_max_size 2048;
	server_tokens off;  # 隐藏NGINX版本号

	# 禁用目录列表
	autoindex off;

	# 上传文件大小限制
	client_max_body_size 20M;

	# 提高服务器名称哈希表大小
	server_names_hash_max_size 512;
	server_names_hash_bucket_size 128;
	# server_name_in_redirect off;

	# MIME类型设置
	include /etc/nginx/mime.types;
	default_type application/octet-stream;
	
	# 自定义MIME类型
	types {
		font/ttf    ttf;
		font/otf    otf;
	}

	##
	# SSL Settings
	##

	ssl_protocols TLSv1.2 TLSv1.3;
	ssl_prefer_server_ciphers on;
	ssl_ciphers 'ECDHE-RSA-AES256-GCM-SHA384:ECDHE-RSA-AES128-GCM-SHA256:DHE-RSA-AES256-GCM-SHA384:DHE-RSA-AES128-GCM-SHA256:ECDHE-RSA-AES256-SHA384:ECDHE-RSA-AES256-SHA:DHE-RSA-AES256-SHA256:DHE-RSA-AES256-SHA:AES256-GCM-SHA384:AES128-GCM-SHA256:AES256-SHA256:AES256-SHA:HIGH:!MEDIUM:!LOW:!aNULL:!eNULL:!EXPORT:!DES:!MD5:!PSK:!RC4:@STRENGTH';
	ssl_session_timeout 10m;
	ssl_session_cache shared:SSL:10m;
	add_header Strict-Transport-Security "max-age=63072000" always;

	##
	# Logging Settings
	##

	# 自定义日志格式，包含真实IP信息
	log_format main '$remote_addr - $http_x_real_ip - $http_x_forwarded_for - $remote_user [$time_local] "$request" '
              '$status $body_bytes_sent "$http_referer" '
              '"$http_user_agent"';

	# 将此格式设为默认
	access_log /var/log/nginx/access.log main;

	##
	# Gzip Settings
	##

	gzip on;
	gzip_vary on;
	gzip_proxied any;
	gzip_comp_level 6;
	gzip_buffers 16 8k;
	gzip_http_version 1.1;
	gzip_types text/plain text/css application/json application/javascript text/xml application/xml application/xml+rss text/javascript font/ttf font/otf image/svg+xml;
	gzip_min_length 2k;

	##
	# 请求限制和安全设置
	##

	# 包含统一白名单配置
	include /etc/nginx/rules/whitelists.conf;

	# WordPress登录页面限流
	limit_req_zone $limited_addr zone=wordpress:10m rate=2r/s;
	# 一般请求限流（每秒5次请求）
	limit_req_zone $limited_addr zone=general:10m rate=5r/s;
	# 搜索请求限流（每秒1次请求）
	limit_req_zone $limited_addr zone=search_limit:10m rate=1r/s;
	# 基于IP限流
	limit_req_zone $limited_addr zone=perip:10m rate=5r/s;
	
	limit_conn_zone $limited_addr zone=conn_limit:10m;
	limit_req_status 429;
	
	# 客户端请求设置
	client_body_buffer_size 128k;
	client_header_buffer_size 512k;
	# client_max_body_size 1280m; # 注释掉这一行，因为在上面已经有全局设置
	large_client_header_buffers 4 512k;
	client_body_timeout 600;
	client_header_timeout 600;
	keepalive_timeout 65;
	send_timeout 600;
	
	# FastCGI设置
	fastcgi_connect_timeout 600;
	fastcgi_send_timeout 600;
	fastcgi_read_timeout 600;
	fastcgi_buffer_size 16k;
	fastcgi_buffers 16 16k;
	fastcgi_busy_buffers_size 32k;
	fastcgi_temp_file_write_size 32k;
	
	# 判断User-Agent和Referer
	map $http_user_agent $is_empty_user_agent {
		"" 1;
		default 0;
	}

	map $http_referer $is_empty_referer {
		"" 1;
		default 0;
	}
	
	# 定义WebP支持
	map $http_accept $webp_suffix {
		default "";
		"~*webp" ".webp";
	}
	
	# 定义安装引用变量
	map $http_referer $is_setup_referer {
		~*/wp-admin/setup-config.php 1;
		~*/wp-admin/install.php 1;
		default 0;
	}
	
	# 定义PHP-FPM上游服务器
	upstream php {
		server unix:/run/php/php8.4-fpm.sock;
	}

	##
	# Virtual Host Configs
	##

	include /etc/nginx/conf.d/*.conf;
	include /etc/nginx/sites-enabled/*;


}


#mail {
#	# See sample authentication script at:
#	# http://wiki.nginx.org/ImapAuthenticateWithApachePhpScript
#
#	# auth_http localhost/auth.php;
#	# pop3_capabilities "TOP" "USER";
#	# imap_capabilities "IMAP4rev1" "UIDPLUS";
#
#	server {
#		listen     localhost:110;
#		protocol   pop3;
#		proxy      on;
#	}
#
#	server {
#		listen     localhost:143;
#		protocol   imap;
#		proxy      on;
#	}
#}
