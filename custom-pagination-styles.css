/* 自定义分页样式 - 类似图片中的效果 */

/* 隐藏原有的分页 */
.aobailei-news-blog .pagination {
    display: none;
}

/* 自定义分页容器 */
.custom-pagination-wrapper {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 15px;
    margin: 40px 0;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 8px;
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
}

/* 总条数显示 */
.pagination-info {
    color: #666;
    font-size: 14px;
    margin-right: 20px;
}

/* 分页按钮容器 */
.pagination-buttons {
    display: flex;
    align-items: center;
    gap: 5px;
}

/* 分页按钮基础样式 */
.pagination-btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    min-width: 32px;
    height: 32px;
    padding: 0 8px;
    border: 1px solid #ddd;
    background: white;
    color: #333;
    text-decoration: none;
    border-radius: 4px;
    font-size: 14px;
    transition: all 0.3s ease;
    cursor: pointer;
}

/* 分页按钮悬停效果 */
.pagination-btn:hover {
    background: #f0f0f0;
    border-color: #ccc;
    text-decoration: none;
}

/* 当前页按钮样式 */
.pagination-btn.current {
    background: #007cba;
    color: white;
    border-color: #007cba;
}

.pagination-btn.current:hover {
    background: #005a87;
    border-color: #005a87;
}

/* 禁用状态 */
.pagination-btn:disabled,
.pagination-btn.disabled {
    background: #f5f5f5;
    color: #ccc;
    border-color: #e0e0e0;
    cursor: not-allowed;
}

/* 省略号 */
.pagination-ellipsis {
    padding: 0 8px;
    color: #999;
    font-size: 14px;
}

/* 前往页面输入框 */
.goto-page {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-left: 20px;
}

.goto-page label {
    color: #666;
    font-size: 14px;
    white-space: nowrap;
}

.goto-page input {
    width: 50px;
    height: 32px;
    padding: 0 8px;
    border: 1px solid #ddd;
    border-radius: 4px;
    text-align: center;
    font-size: 14px;
}

.goto-page button {
    height: 32px;
    padding: 0 12px;
    background: #007cba;
    color: white;
    border: none;
    border-radius: 4px;
    font-size: 14px;
    cursor: pointer;
    transition: background 0.3s ease;
}

.goto-page button:hover {
    background: #005a87;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .custom-pagination-wrapper {
        flex-direction: column;
        gap: 15px;
        padding: 15px;
    }
    
    .pagination-info {
        margin-right: 0;
        margin-bottom: 10px;
    }
    
    .goto-page {
        margin-left: 0;
        margin-top: 10px;
    }
    
    .pagination-buttons {
        flex-wrap: wrap;
        justify-content: center;
    }
}

@media (max-width: 480px) {
    .pagination-btn {
        min-width: 28px;
        height: 28px;
        font-size: 12px;
    }
    
    .goto-page input {
        width: 40px;
        height: 28px;
    }
    
    .goto-page button {
        height: 28px;
        padding: 0 8px;
        font-size: 12px;
    }
}

/* 加载状态 */
.pagination-loading {
    opacity: 0.6;
    pointer-events: none;
}

.pagination-loading::after {
    content: "";
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #007cba;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}
