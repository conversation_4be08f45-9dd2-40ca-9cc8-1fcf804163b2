#!/usr/bin/env python3
"""
SMTP测试工具 - 用于测试SMTP服务器连接、认证和发送邮件
"""

import argparse
import smtplib
import socket
import ssl
import sys
import time
from email.mime.multipart import MIMEMultipart
from email.mime.text import MIMEText
from email.utils import formatdate


class SMTPTester:
    def __init__(self, host, port=25, username=None, password=None, 
                 use_tls=False, use_ssl=False, timeout=10, debug_level=0):
        """初始化SMTP测试器"""
        self.host = host
        self.port = port
        self.username = username
        self.password = password
        self.use_tls = use_tls
        self.use_ssl = use_ssl
        self.timeout = timeout
        self.debug_level = debug_level
        self.server = None

    def connect(self):
        """测试连接到SMTP服务器"""
        print(f"\n[*] 尝试连接到 {self.host}:{self.port}...")
        start_time = time.time()
        
        try:
            # 根据SSL设置选择连接方式
            if self.use_ssl:
                context = ssl.create_default_context()
                self.server = smtplib.SMTP_SSL(
                    self.host, self.port, timeout=self.timeout, context=context)
            else:
                self.server = smtplib.SMTP(self.host, self.port, timeout=self.timeout)
            
            # 设置调试级别
            self.server.set_debuglevel(self.debug_level)
            
            # 获取服务器响应
            code, response = self.server.ehlo()
            print(f"[+] 连接成功! 响应时间: {time.time() - start_time:.2f}秒")
            print(f"[+] 服务器响应: {code} {response.decode()}")
            
            # 检查支持的功能
            supported_features = []
            for feature in response.decode().split('\n'):
                if feature.strip():
                    supported_features.append(feature.strip())
            
            print("\n[*] 服务器支持的功能:")
            for feature in supported_features[1:]:  # 跳过第一行(服务器名称)
                print(f"    - {feature}")
            
            return True
            
        except socket.timeout:
            print(f"[-] 连接超时! 无法在{self.timeout}秒内连接到服务器。")
            return False
        except socket.gaierror:
            print(f"[-] 无法解析主机名 {self.host}")
            return False
        except ConnectionRefusedError:
            print(f"[-] 连接被拒绝! 请检查主机名和端口是否正确。")
            return False
        except Exception as e:
            print(f"[-] 连接错误: {str(e)}")
            return False

    def test_tls(self):
        """测试TLS连接升级"""
        if not self.server:
            print("[-] 未连接到服务器，无法测试TLS")
            return False
            
        if self.use_ssl:
            print("[*] 已使用SSL连接，跳过TLS测试")
            return True
            
        print("\n[*] 测试TLS连接...")
        try:
            self.server.starttls()
            print("[+] TLS连接成功!")
            return True
        except smtplib.SMTPException as e:
            print(f"[-] TLS连接失败: {str(e)}")
            return False
        except Exception as e:
            print(f"[-] TLS错误: {str(e)}")
            return False

    def test_auth(self):
        """测试SMTP认证"""
        if not self.server:
            print("[-] 未连接到服务器，无法测试认证")
            return False
            
        if not self.username or not self.password:
            print("[*] 未提供用户名或密码，跳过认证测试")
            return False
            
        print(f"\n[*] 尝试使用用户名 '{self.username}' 进行认证...")
        try:
            self.server.login(self.username, self.password)
            print("[+] 认证成功!")
            return True
        except smtplib.SMTPAuthenticationError:
            print("[-] 认证失败! 用户名或密码错误。")
            return False
        except smtplib.SMTPException as e:
            print(f"[-] 认证错误: {str(e)}")
            return False
        except Exception as e:
            print(f"[-] 认证过程中发生错误: {str(e)}")
            return False

    def send_test_email(self, from_addr, to_addr, subject="SMTP测试邮件", 
                        body="这是一封SMTP测试邮件。"):
        """发送测试邮件"""
        if not self.server:
            print("[-] 未连接到服务器，无法发送邮件")
            return False
            
        print(f"\n[*] 尝试发送测试邮件从 {from_addr} 到 {to_addr}...")
        
        try:
            # 创建邮件
            msg = MIMEMultipart()
            msg['From'] = from_addr
            msg['To'] = to_addr
            msg['Subject'] = subject
            msg['Date'] = formatdate(localtime=True)
            msg.attach(MIMEText(body, 'plain'))
            
            # 发送邮件
            self.server.sendmail(from_addr, to_addr, msg.as_string())
            print("[+] 测试邮件发送成功!")
            return True
        except smtplib.SMTPRecipientsRefused:
            print("[-] 收件人被拒绝!")
            return False
        except smtplib.SMTPSenderRefused:
            print("[-] 发件人被拒绝!")
            return False
        except smtplib.SMTPDataError as e:
            print(f"[-] 发送数据错误: {str(e)}")
            return False
        except Exception as e:
            print(f"[-] 发送邮件时发生错误: {str(e)}")
            return False

    def close(self):
        """关闭SMTP连接"""
        if self.server:
            try:
                self.server.quit()
                print("\n[*] SMTP连接已关闭")
            except:
                print("\n[*] SMTP连接已关闭(异常)")


def main():
    """主函数，处理命令行参数并执行测试"""
    parser = argparse.ArgumentParser(description='SMTP服务器测试工具')
    
    # 服务器设置
    parser.add_argument('--host', required=True, help='SMTP服务器主机名或IP地址')
    parser.add_argument('--port', type=int, default=25, help='SMTP服务器端口 (默认: 25)')
    
    # 认证设置
    parser.add_argument('--username', help='SMTP认证用户名')
    parser.add_argument('--password', help='SMTP认证密码')
    
    # 连接设置
    parser.add_argument('--tls', action='store_true', help='使用STARTTLS')
    parser.add_argument('--ssl', action='store_true', help='使用SSL/TLS连接')
    parser.add_argument('--timeout', type=int, default=10, help='连接超时时间(秒) (默认: 10)')
    
    # 邮件设置
    parser.add_argument('--from', dest='from_addr', help='发件人地址')
    parser.add_argument('--to', dest='to_addr', help='收件人地址')
    parser.add_argument('--subject', default='SMTP测试邮件', help='邮件主题 (默认: SMTP测试邮件)')
    parser.add_argument('--body', default='这是一封SMTP测试邮件。', help='邮件内容 (默认: 这是一封SMTP测试邮件。)')
    
    # 调试设置
    parser.add_argument('--debug', action='store_true', help='启用SMTP调试输出')
    
    # 测试选项
    parser.add_argument('--test-connection', action='store_true', help='只测试连接')
    parser.add_argument('--test-auth', action='store_true', help='测试连接和认证')
    parser.add_argument('--send-email', action='store_true', help='测试连接、认证(如果提供)并发送邮件')
    
    args = parser.parse_args()
    
    # 验证参数
    if args.ssl and args.tls:
        print("[-] 错误: 不能同时使用 --ssl 和 --tls 选项")
        sys.exit(1)
        
    if args.send_email and (not args.from_addr or not args.to_addr):
        print("[-] 错误: 发送邮件需要提供 --from 和 --to 参数")
        sys.exit(1)
        
    if (args.username and not args.password) or (not args.username and args.password):
        print("[-] 错误: 认证需要同时提供用户名和密码")
        sys.exit(1)
    
    # 创建SMTP测试器
    tester = SMTPTester(
        host=args.host,
        port=args.port,
        username=args.username,
        password=args.password,
        use_tls=args.tls,
        use_ssl=args.ssl,
        timeout=args.timeout,
        debug_level=1 if args.debug else 0
    )
    
    try:
        # 测试连接
        if not tester.connect():
            sys.exit(1)
            
        # 如果需要TLS，测试TLS
        if args.tls and not tester.test_tls():
            sys.exit(1)
            
        # 如果提供了认证信息，测试认证
        if (args.username and args.password) and not tester.test_auth():
            sys.exit(1)
            
        # 如果需要发送邮件，发送测试邮件
        if args.send_email:
            tester.send_test_email(
                from_addr=args.from_addr,
                to_addr=args.to_addr,
                subject=args.subject,
                body=args.body
            )
            
        print("\n[+] 所有测试完成!")
        
    finally:
        # 确保关闭连接
        tester.close()


if __name__ == "__main__":
    print("=" * 60)
    print("SMTP测试工具 - 用于测试SMTP服务器连接、认证和发送邮件")
    print("=" * 60)
    main() 