<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Nginx图文培训教程</title>
    <style>
        :root {
            --primary-color: #2c3e50;
            --secondary-color: #3498db;
            --accent-color: #e74c3c;
            --success-color: #27ae60;
            --warning-color: #f39c12;
            --bg-color: #ecf0f1;
            --text-color: #2c3e50;
            --card-bg: #ffffff;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            line-height: 1.6;
            color: var(--text-color);
            background: linear-gradient(135deg, var(--bg-color) 0%, #bdc3c7 100%);
            min-height: 100vh;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 40px;
            padding: 40px 20px;
            background: var(--card-bg);
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }

        .logo-container {
            margin-bottom: 20px;
        }

        .logo {
            width: 80px;
            height: 80px;
            background: var(--primary-color);
            border-radius: 50%;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 2em;
            font-weight: bold;
        }

        .header h1 {
            color: var(--primary-color);
            font-size: 2.5em;
            margin: 20px 0 10px;
        }

        .header p {
            font-size: 1.2em;
            color: #7f8c8d;
        }

        .customization-panel {
            background: var(--card-bg);
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 30px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }

        .customization-panel h3 {
            margin-bottom: 15px;
            color: var(--primary-color);
        }

        .color-picker {
            display: inline-block;
            margin: 5px;
        }

        .color-picker input {
            width: 50px;
            height: 30px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
        }

        .color-picker label {
            display: block;
            font-size: 0.9em;
            text-align: center;
            margin-top: 5px;
        }

        .navigation {
            display: flex;
            justify-content: center;
            flex-wrap: wrap;
            gap: 15px;
            margin-bottom: 30px;
        }

        .nav-btn {
            padding: 12px 24px;
            background: var(--secondary-color);
            color: white;
            border: none;
            border-radius: 25px;
            cursor: pointer;
            font-size: 1em;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(52, 152, 219, 0.3);
        }

        .nav-btn:hover {
            background: var(--primary-color);
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(52, 152, 219, 0.4);
        }

        .nav-btn.active {
            background: var(--accent-color);
        }

        .chapter {
            display: none;
            background: var(--card-bg);
            padding: 40px;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            margin-bottom: 30px;
        }

        .chapter.active {
            display: block;
            animation: fadeIn 0.5s ease-in;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .chapter h2 {
            color: var(--primary-color);
            font-size: 2em;
            margin-bottom: 20px;
            border-bottom: 3px solid var(--secondary-color);
            padding-bottom: 10px;
        }

        .analogy-box {
            background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);
            color: white;
            padding: 25px;
            border-radius: 10px;
            margin: 20px 0;
            position: relative;
            overflow: hidden;
        }

        .analogy-box::before {
            content: '💡';
            font-size: 2em;
            position: absolute;
            top: 15px;
            right: 20px;
            opacity: 0.3;
        }

        .diagram {
            background: #f8f9fa;
            border: 2px solid var(--secondary-color);
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            text-align: center;
        }

        .flow-diagram {
            display: flex;
            align-items: center;
            justify-content: space-between;
            flex-wrap: wrap;
            gap: 10px;
        }

        .flow-step {
            background: var(--secondary-color);
            color: white;
            padding: 15px;
            border-radius: 10px;
            flex: 1;
            min-width: 120px;
            position: relative;
        }

        .flow-step::after {
            content: '→';
            position: absolute;
            right: -15px;
            top: 50%;
            transform: translateY(-50%);
            font-size: 1.5em;
            color: var(--secondary-color);
        }

        .flow-step:last-child::after {
            display: none;
        }

        .comparison-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
            background: white;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }

        .comparison-table th {
            background: var(--primary-color);
            color: white;
            padding: 15px;
            text-align: left;
        }

        .comparison-table td {
            padding: 12px 15px;
            border-bottom: 1px solid #ecf0f1;
        }

        .comparison-table tr:nth-child(even) {
            background: #f8f9fa;
        }

        .highlight-box {
            background: linear-gradient(135deg, #00b894 0%, #00a085 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
        }

        .warning-box {
            background: linear-gradient(135deg, #fdcb6e 0%, #e17055 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
        }

        .progress-bar {
            width: 100%;
            height: 8px;
            background: #ecf0f1;
            border-radius: 4px;
            margin: 20px 0;
            overflow: hidden;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, var(--secondary-color), var(--success-color));
            border-radius: 4px;
            transition: width 0.3s ease;
        }

        .interactive-demo {
            background: #f8f9fa;
            border: 2px dashed var(--secondary-color);
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            text-align: center;
        }

        .demo-button {
            background: var(--success-color);
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
            transition: all 0.3s ease;
        }

        .demo-button:hover {
            background: var(--primary-color);
            transform: scale(1.05);
        }

        .footer {
            text-align: center;
            padding: 30px;
            background: var(--primary-color);
            color: white;
            border-radius: 15px;
            margin-top: 40px;
        }

        @media (max-width: 768px) {
            .container {
                padding: 10px;
            }
            
            .header h1 {
                font-size: 2em;
            }
            
            .chapter {
                padding: 20px;
            }
            
            .flow-diagram {
                flex-direction: column;
            }
            
            .flow-step::after {
                content: '↓';
                right: 50%;
                top: 100%;
                transform: translateX(50%);
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 头部 -->
        <div class="header">
            <div class="logo-container">
                <div class="logo" id="logo">N</div>
            </div>
            <h1 id="main-title">Nginx图文培训教程</h1>
            <p>用生活化比喻轻松理解Nginx技术原理</p>
        </div>

        <!-- 定制化面板 -->
        <div class="customization-panel">
            <h3>🎨 个性化定制</h3>
            <div style="display: flex; flex-wrap: wrap; gap: 20px;">
                <div class="color-picker">
                    <input type="color" id="primary-color" value="#2c3e50">
                    <label>主色调</label>
                </div>
                <div class="color-picker">
                    <input type="color" id="secondary-color" value="#3498db">
                    <label>辅助色</label>
                </div>
                <div class="color-picker">
                    <input type="color" id="accent-color" value="#e74c3c">
                    <label>强调色</label>
                </div>
                <div style="margin-left: 20px;">
                    <label for="logo-text">Logo文字:</label>
                    <input type="text" id="logo-text" value="N" maxlength="2" style="width: 50px; margin-left: 10px;">
                </div>
                <div style="margin-left: 20px;">
                    <label for="company-name">公司名称:</label>
                    <input type="text" id="company-name" placeholder="您的公司名称" style="margin-left: 10px;">
                </div>
            </div>
        </div>

        <!-- 进度条 -->
        <div class="progress-bar">
            <div class="progress-fill" id="progress-fill" style="width: 0%"></div>
        </div>

        <!-- 导航 -->
        <div class="navigation">
            <button class="nav-btn active" onclick="showChapter(0)">基础概念</button>
            <button class="nav-btn" onclick="showChapter(1)">前后端关系</button>
            <button class="nav-btn" onclick="showChapter(2)">HTTP服务器</button>
            <button class="nav-btn" onclick="showChapter(3)">反向代理</button>
            <button class="nav-btn" onclick="showChapter(4)">限制功能</button>
        </div>

        <!-- 第一章：基础概念 -->
        <div class="chapter active" id="chapter-0">
            <h2>🏠 第一章：Nginx基础概念</h2>

            <div class="analogy-box">
                <h3>🏢 服务器就像24小时便利店</h3>
                <p><strong>便利店老板</strong> = 服务器软件</p>
                <p><strong>顾客</strong> = 网站访问者</p>
                <p><strong>商品</strong> = 网页、图片、视频等文件</p>
                <p><strong>收银台</strong> = 处理请求的地方</p>
            </div>

            <div class="diagram">
                <h4>网站访问流程</h4>
                <div class="flow-diagram">
                    <div class="flow-step">用户输入网址</div>
                    <div class="flow-step">连接到服务器</div>
                    <div class="flow-step">发送请求</div>
                    <div class="flow-step">服务器处理</div>
                    <div class="flow-step">返回网页</div>
                </div>
            </div>

            <div class="highlight-box">
                <h3>🎯 Nginx = 超级聪明的餐厅经理</h3>
                <p>普通餐厅只有1个服务员，顾客排长队；</p>
                <p>Nginx餐厅有1个超级经理，可以同时接待多个顾客，分配任务给后厨，管理排队秩序！</p>
            </div>

            <table class="comparison-table">
                <tr>
                    <th>特点</th>
                    <th>比喻</th>
                    <th>实际意义</th>
                </tr>
                <tr>
                    <td>高性能 ⚡</td>
                    <td>像高铁一样快速</td>
                    <td>同时处理成千上万个请求</td>
                </tr>
                <tr>
                    <td>低资源消耗 💡</td>
                    <td>像节能灯泡</td>
                    <td>占用很少内存和CPU</td>
                </tr>
                <tr>
                    <td>稳定可靠 🏔️</td>
                    <td>像大山一样稳固</td>
                    <td>很少崩溃，长时间运行</td>
                </tr>
            </table>

            <div class="interactive-demo">
                <h4>🎯 互动演示：为什么需要Nginx？</h4>
                <button class="demo-button" onclick="showDemo('without-nginx')">没有Nginx的网站</button>
                <button class="demo-button" onclick="showDemo('with-nginx')">有Nginx的网站</button>
                <div id="demo-result" style="margin-top: 15px; font-weight: bold;"></div>
            </div>
        </div>

        <!-- 第二章：前后端关系 -->
        <div class="chapter" id="chapter-1">
            <h2>🏪 第二章：前后端关系图解</h2>

            <div class="analogy-box">
                <h3>🎭 餐厅比喻理解前后端</h3>
                <p><strong>前端</strong> = 餐厅前厅（服务员、菜单、装修）</p>
                <p><strong>后端</strong> = 餐厅后厨（厨师、食材处理、烹饪）</p>
                <p><strong>数据库</strong> = 仓库（食材库、库存管理）</p>
            </div>

            <div class="diagram">
                <h4>系统架构图</h4>
                <div style="display: flex; justify-content: space-around; flex-wrap: wrap; gap: 20px;">
                    <div style="background: #74b9ff; color: white; padding: 20px; border-radius: 10px; flex: 1; min-width: 200px;">
                        <h4>前端 (餐厅前厅)</h4>
                        <p>• 用户界面</p>
                        <p>• 页面设计</p>
                        <p>• 用户交互</p>
                        <p>• 显示结果</p>
                    </div>
                    <div style="background: #00b894; color: white; padding: 20px; border-radius: 10px; flex: 1; min-width: 200px;">
                        <h4>后端 (餐厅后厨)</h4>
                        <p>• 业务逻辑</p>
                        <p>• 数据处理</p>
                        <p>• 安全验证</p>
                        <p>• 接口服务</p>
                    </div>
                    <div style="background: #fdcb6e; color: white; padding: 20px; border-radius: 10px; flex: 1; min-width: 200px;">
                        <h4>数据库 (仓库)</h4>
                        <p>• 数据存储</p>
                        <p>• 数据管理</p>
                        <p>• 数据查询</p>
                        <p>• 数据备份</p>
                    </div>
                </div>
            </div>

            <div class="highlight-box">
                <h3>🔧 Nginx在其中的作用 = 餐厅经理</h3>
                <p>1. <strong>接待顾客</strong>（处理用户请求）</p>
                <p>2. <strong>分配任务</strong>（负载均衡）</p>
                <p>3. <strong>协调前后厅</strong>（反向代理）</p>
                <p>4. <strong>维护秩序</strong>（访问控制）</p>
            </div>

            <div class="diagram">
                <h4>数据流向演示</h4>
                <div class="flow-diagram">
                    <div class="flow-step">用户操作</div>
                    <div class="flow-step">前端界面</div>
                    <div class="flow-step">发送请求</div>
                    <div class="flow-step">后端处理</div>
                    <div class="flow-step">查询数据库</div>
                </div>
                <div style="text-align: center; margin-top: 10px; color: #7f8c8d;">
                    ← 返回路径：数据库 → 后端 → 前端 → 用户
                </div>
            </div>
        </div>

        <!-- 第三章：HTTP服务器 -->
        <div class="chapter" id="chapter-2">
            <h2>📮 第三章：HTTP服务器原理</h2>

            <div class="analogy-box">
                <h3>🏢 HTTP服务器 = 现代化邮局</h3>
                <p><strong>邮局大厅</strong> = HTTP服务器</p>
                <p><strong>邮局工作人员</strong> = 服务器程序</p>
                <p><strong>邮件分拣系统</strong> = 请求处理机制</p>
                <p><strong>投递员</strong> = 响应传输</p>
            </div>

            <div class="diagram">
                <h4>HTTP请求类型说明</h4>
                <table class="comparison-table">
                    <tr>
                        <th>请求类型</th>
                        <th>生活比喻</th>
                        <th>实际用途</th>
                        <th>例子</th>
                    </tr>
                    <tr>
                        <td>GET 📖</td>
                        <td>去商店问"有什么商品？"</td>
                        <td>获取信息</td>
                        <td>打开网页、查看图片</td>
                    </tr>
                    <tr>
                        <td>POST 💰</td>
                        <td>去银行存钱</td>
                        <td>提交信息</td>
                        <td>登录、注册、发评论</td>
                    </tr>
                    <tr>
                        <td>PUT 📝</td>
                        <td>去银行更新个人信息</td>
                        <td>更新数据</td>
                        <td>修改个人资料</td>
                    </tr>
                    <tr>
                        <td>DELETE 🗑️</td>
                        <td>去银行销户</td>
                        <td>删除数据</td>
                        <td>删除文章、注销账户</td>
                    </tr>
                </table>
            </div>

            <div class="warning-box">
                <h3>📊 HTTP状态码 = 邮局回执单</h3>
                <p><strong>200 OK</strong> ✅ = "信件成功送达"</p>
                <p><strong>404 Not Found</strong> ❌ = "查无此人，地址不存在"</p>
                <p><strong>500 Error</strong> 💥 = "邮局内部出故障了"</p>
                <p><strong>301 Moved</strong> 🔄 = "收件人已搬家，新地址是..."</p>
            </div>

            <div class="interactive-demo">
                <h4>🚀 Nginx vs 传统服务器性能对比</h4>
                <button class="demo-button" onclick="showPerformance('apache')">Apache性能</button>
                <button class="demo-button" onclick="showPerformance('nginx')">Nginx性能</button>
                <div id="performance-result" style="margin-top: 15px;"></div>
            </div>
        </div>

        <!-- 第四章：反向代理 -->
        <div class="chapter" id="chapter-3">
            <h2>🚦 第四章：反向代理概念</h2>

            <div class="analogy-box">
                <h3>🏢 反向代理 = 大型购物中心的智能接待员</h3>
                <p>没有接待员：顾客到处乱转，找不到想要的店铺</p>
                <p>有接待员：顾客咨询接待员，直接指路到目标店铺</p>
            </div>

            <div class="diagram">
                <h4>正向代理 vs 反向代理</h4>
                <div style="display: flex; justify-content: space-around; flex-wrap: wrap; gap: 20px;">
                    <div style="background: #a29bfe; color: white; padding: 20px; border-radius: 10px; flex: 1;">
                        <h4>正向代理 = 个人助理</h4>
                        <p>你 → 助理 → 银行</p>
                        <p>银行只看到助理</p>
                        <p>隐藏你的身份</p>
                    </div>
                    <div style="background: #fd79a8; color: white; padding: 20px; border-radius: 10px; flex: 1;">
                        <h4>反向代理 = 公司前台</h4>
                        <p>访客 → 前台 → 经理</p>
                        <p>访客不知道经理位置</p>
                        <p>隐藏内部结构</p>
                    </div>
                </div>
            </div>

            <div class="highlight-box">
                <h3>🎯 反向代理的主要功能</h3>
                <p><strong>1. 负载均衡 ⚖️</strong> = 智能排队系统，合理分配工作</p>
                <p><strong>2. 故障转移 🔄</strong> = 备用通道，自动切换</p>
                <p><strong>3. SSL终端 🔒</strong> = 安全检查站，统一加密处理</p>
                <p><strong>4. 缓存加速 ⚡</strong> = 快递代收点，常用内容快速响应</p>
            </div>

            <div class="diagram">
                <h4>负载均衡演示</h4>
                <div style="text-align: center;">
                    <div style="background: #00b894; color: white; padding: 15px; border-radius: 10px; margin: 10px 0;">
                        10个用户请求 → Nginx智能分发
                    </div>
                    <div style="display: flex; justify-content: space-around; flex-wrap: wrap; gap: 10px;">
                        <div style="background: #74b9ff; color: white; padding: 10px; border-radius: 5px; flex: 1;">服务器1: 3个请求</div>
                        <div style="background: #74b9ff; color: white; padding: 10px; border-radius: 5px; flex: 1;">服务器2: 3个请求</div>
                        <div style="background: #74b9ff; color: white; padding: 10px; border-radius: 5px; flex: 1;">服务器3: 4个请求</div>
                    </div>
                </div>
            </div>

            <div class="interactive-demo">
                <h4>🌍 应用场景演示</h4>
                <button class="demo-button" onclick="showScenario('ecommerce')">电商双11</button>
                <button class="demo-button" onclick="showScenario('video')">视频网站</button>
                <button class="demo-button" onclick="showScenario('enterprise')">企业系统</button>
                <div id="scenario-result" style="margin-top: 15px;"></div>
            </div>
        </div>

        <!-- 第五章：限制功能 -->
        <div class="chapter" id="chapter-4">
            <h2>🛡️ 第五章：Nginx限制功能</h2>

            <div class="analogy-box">
                <h3>🏢 Nginx限制功能 = 高档写字楼安保系统</h3>
                <p><strong>门禁系统</strong> = IP访问控制</p>
                <p><strong>流量控制</strong> = 电梯限载、排队系统</p>
                <p><strong>安全防护</strong> = 智能识别威胁</p>
            </div>

            <div class="diagram">
                <h4>访问控制类型</h4>
                <table class="comparison-table">
                    <tr>
                        <th>控制类型</th>
                        <th>生活比喻</th>
                        <th>实际作用</th>
                    </tr>
                    <tr>
                        <td>IP地址控制</td>
                        <td>门卫检查身份证</td>
                        <td>只允许特定IP访问</td>
                    </tr>
                    <tr>
                        <td>地理位置控制</td>
                        <td>根据护照限制入境</td>
                        <td>不同地区不同权限</td>
                    </tr>
                    <tr>
                        <td>用户代理控制</td>
                        <td>检查访客身份目的</td>
                        <td>阻止恶意爬虫</td>
                    </tr>
                </table>
            </div>

            <div class="warning-box">
                <h3>⚡ 速率限制 = 银行ATM使用限制</h3>
                <p><strong>普通用户：</strong>每分钟最多10次请求</p>
                <p><strong>VIP用户：</strong>每分钟最多50次请求</p>
                <p><strong>恶意用户：</strong>超出限制，暂时拒绝服务</p>
            </div>

            <div class="highlight-box">
                <h3>🚨 安全防护功能</h3>
                <p><strong>DDoS攻击防护：</strong>应对恶意聚众闹事</p>
                <p><strong>恶意爬虫防护：</strong>防止小偷踩点</p>
                <p><strong>智能限制策略：</strong>VIP会员制度</p>
            </div>

            <div class="diagram">
                <h4>分级限制策略</h4>
                <table class="comparison-table">
                    <tr>
                        <th>用户类型</th>
                        <th>请求限制</th>
                        <th>带宽限制</th>
                        <th>特殊权限</th>
                    </tr>
                    <tr>
                        <td>游客</td>
                        <td>10/分钟</td>
                        <td>100KB/s</td>
                        <td>基础访问</td>
                    </tr>
                    <tr>
                        <td>注册用户</td>
                        <td>60/分钟</td>
                        <td>500KB/s</td>
                        <td>完整功能</td>
                    </tr>
                    <tr>
                        <td>VIP用户</td>
                        <td>300/分钟</td>
                        <td>2MB/s</td>
                        <td>优先处理</td>
                    </tr>
                    <tr>
                        <td>企业用户</td>
                        <td>无限制</td>
                        <td>10MB/s</td>
                        <td>专属通道</td>
                    </tr>
                </table>
            </div>

            <div class="interactive-demo">
                <h4>📈 效果监控演示</h4>
                <button class="demo-button" onclick="showStats()">查看今日统计</button>
                <div id="stats-result" style="margin-top: 15px;"></div>
            </div>
        </div>

        <!-- 页脚 -->
        <div class="footer">
            <h3>🎓 培训完成！</h3>
            <p>恭喜您完成了Nginx图文培训教程</p>
            <p id="company-footer">技术让生活更美好</p>
            <p style="margin-top: 20px; font-size: 0.9em; opacity: 0.8;">
                © 2024 Nginx培训教程 | 用生活化比喻理解技术原理
            </p>
        </div>
    </div>

    <script>
        // 当前章节
        let currentChapter = 0;
        const totalChapters = 5;

        // 显示指定章节
        function showChapter(chapterIndex) {
            // 隐藏所有章节
            const chapters = document.querySelectorAll('.chapter');
            chapters.forEach(chapter => chapter.classList.remove('active'));

            // 显示指定章节
            document.getElementById(`chapter-${chapterIndex}`).classList.add('active');

            // 更新导航按钮状态
            const navBtns = document.querySelectorAll('.nav-btn');
            navBtns.forEach(btn => btn.classList.remove('active'));
            navBtns[chapterIndex].classList.add('active');

            // 更新进度条
            currentChapter = chapterIndex;
            updateProgress();
        }

        // 更新进度条
        function updateProgress() {
            const progress = ((currentChapter + 1) / totalChapters) * 100;
            document.getElementById('progress-fill').style.width = progress + '%';
        }

        // 颜色定制功能
        function setupColorCustomization() {
            const primaryColorPicker = document.getElementById('primary-color');
            const secondaryColorPicker = document.getElementById('secondary-color');
            const accentColorPicker = document.getElementById('accent-color');
            const logoTextInput = document.getElementById('logo-text');
            const companyNameInput = document.getElementById('company-name');

            primaryColorPicker.addEventListener('change', function() {
                document.documentElement.style.setProperty('--primary-color', this.value);
            });

            secondaryColorPicker.addEventListener('change', function() {
                document.documentElement.style.setProperty('--secondary-color', this.value);
            });

            accentColorPicker.addEventListener('change', function() {
                document.documentElement.style.setProperty('--accent-color', this.value);
            });

            logoTextInput.addEventListener('input', function() {
                document.getElementById('logo').textContent = this.value || 'N';
            });

            companyNameInput.addEventListener('input', function() {
                const footerText = this.value ? `${this.value} - 技术让生活更美好` : '技术让生活更美好';
                document.getElementById('company-footer').textContent = footerText;

                if (this.value) {
                    document.getElementById('main-title').textContent = `${this.value} - Nginx培训教程`;
                } else {
                    document.getElementById('main-title').textContent = 'Nginx图文培训教程';
                }
            });
        }

        // 演示功能
        function showDemo(type) {
            const resultDiv = document.getElementById('demo-result');
            if (type === 'without-nginx') {
                resultDiv.innerHTML = `
                    <div style="color: #e74c3c;">
                        ❌ 没有Nginx的网站：<br>
                        • 只有1个服务员处理所有顾客<br>
                        • 顾客排长队等待<br>
                        • 服务员累坏了，效率很低<br>
                        • 高峰期经常崩溃
                    </div>
                `;
            } else {
                resultDiv.innerHTML = `
                    <div style="color: #27ae60;">
                        ✅ 有Nginx的网站：<br>
                        • 智能经理协调多个服务员<br>
                        • 顾客分流，井然有序<br>
                        • 效率提升10倍<br>
                        • 稳定可靠，很少出问题
                    </div>
                `;
            }
        }

        // 性能对比演示
        function showPerformance(serverType) {
            const resultDiv = document.getElementById('performance-result');
            if (serverType === 'apache') {
                resultDiv.innerHTML = `
                    <div style="background: #fdcb6e; color: white; padding: 15px; border-radius: 10px;">
                        <h4>Apache服务器 (传统邮局)</h4>
                        <p>并发处理：1,000个请求</p>
                        <p>内存占用：较高</p>
                        <p>响应速度：中等</p>
                        <div style="background: #e17055; height: 20px; width: 30%; border-radius: 10px; margin-top: 10px;"></div>
                    </div>
                `;
            } else {
                resultDiv.innerHTML = `
                    <div style="background: #00b894; color: white; padding: 15px; border-radius: 10px;">
                        <h4>Nginx服务器 (超级邮局)</h4>
                        <p>并发处理：10,000个请求 (提升10倍!)</p>
                        <p>内存占用：节省50%</p>
                        <p>响应速度：极快</p>
                        <div style="background: #00a085; height: 20px; width: 90%; border-radius: 10px; margin-top: 10px;"></div>
                    </div>
                `;
            }
        }

        // 应用场景演示
        function showScenario(scenario) {
            const resultDiv = document.getElementById('scenario-result');
            const scenarios = {
                'ecommerce': {
                    title: '🛒 电商双11场景',
                    content: `
                        <strong>挑战：</strong>瞬间百万用户同时访问<br>
                        <strong>解决：</strong>Nginx智能分发到不同服务器群<br>
                        • 商品服务器群 - 处理商品浏览<br>
                        • 订单服务器群 - 处理下单请求<br>
                        • 支付服务器群 - 处理支付流程<br>
                        <strong>效果：</strong>系统稳定，用户体验流畅
                    `,
                    color: '#e74c3c'
                },
                'video': {
                    title: '🎬 视频网站场景',
                    content: `
                        <strong>挑战：</strong>不同地区用户访问速度差异大<br>
                        <strong>解决：</strong>Nginx就近分发<br>
                        • 北京用户 → 北京服务器<br>
                        • 上海用户 → 上海服务器<br>
                        • 广州用户 → 广州服务器<br>
                        <strong>效果：</strong>视频加载快，不卡顿
                    `,
                    color: '#9b59b6'
                },
                'enterprise': {
                    title: '🏢 企业系统场景',
                    content: `
                        <strong>挑战：</strong>多个内部应用，需要统一入口<br>
                        <strong>解决：</strong>Nginx统一管理<br>
                        • /hr → 人事系统<br>
                        • /finance → 财务系统<br>
                        • /crm → 客户管理系统<br>
                        <strong>效果：</strong>员工一个入口访问所有系统
                    `,
                    color: '#3498db'
                }
            };

            const selected = scenarios[scenario];
            resultDiv.innerHTML = `
                <div style="background: ${selected.color}; color: white; padding: 15px; border-radius: 10px;">
                    <h4>${selected.title}</h4>
                    <p>${selected.content}</p>
                </div>
            `;
        }

        // 统计数据演示
        function showStats() {
            const resultDiv = document.getElementById('stats-result');
            resultDiv.innerHTML = `
                <div style="background: #2c3e50; color: white; padding: 20px; border-radius: 10px;">
                    <h4>📊 今日安全统计</h4>
                    <div style="display: flex; justify-content: space-around; flex-wrap: wrap; gap: 15px; margin-top: 15px;">
                        <div style="text-align: center;">
                            <div style="font-size: 2em; color: #27ae60;">95%</div>
                            <div>正常请求</div>
                        </div>
                        <div style="text-align: center;">
                            <div style="font-size: 2em; color: #f39c12;">4%</div>
                            <div>被限制请求</div>
                        </div>
                        <div style="text-align: center;">
                            <div style="font-size: 2em; color: #e74c3c;">1%</div>
                            <div>被拒绝请求</div>
                        </div>
                    </div>
                    <div style="margin-top: 15px; padding-top: 15px; border-top: 1px solid #34495e;">
                        <p><strong>性能提升：</strong></p>
                        <p>• 服务器负载降低 30%</p>
                        <p>• 响应速度提升 50%</p>
                        <p>• 故障率降低 80%</p>
                    </div>
                </div>
            `;
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            setupColorCustomization();
            updateProgress();

            // 添加键盘导航
            document.addEventListener('keydown', function(e) {
                if (e.key === 'ArrowLeft' && currentChapter > 0) {
                    showChapter(currentChapter - 1);
                } else if (e.key === 'ArrowRight' && currentChapter < totalChapters - 1) {
                    showChapter(currentChapter + 1);
                }
            });

            // 添加平滑滚动
            const navBtns = document.querySelectorAll('.nav-btn');
            navBtns.forEach((btn, index) => {
                btn.addEventListener('click', function() {
                    setTimeout(() => {
                        document.querySelector('.chapter.active').scrollIntoView({
                            behavior: 'smooth',
                            block: 'start'
                        });
                    }, 100);
                });
            });
        });
    </script>
</body>
</html>
