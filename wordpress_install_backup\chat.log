按我的思路帮我写一个wordpress的安装和备份、还原脚本，我有多个wordpress站点在服务器上：
自定义路径变量backup_path
1，架构为：nginx+php+mysql   多个wordpress站点
2，站点目录地址在 /var/www/ 下的多个站点目录
3，以站点 site1 为例：站点site1的目录为/var/www/site1，站点site1的配置文件wp-config.php的路径为/var/www/site1/wp-config.php（检索wp-config.php时只需要检索 /var/www/ 下的子目录最上层），站点site1的数据库配置在/var/www/site1/wp-config.php里:
	数据库名在这一行里 'site1' ：define('DB_NAME', 'site1');
	用户名在这一行里 'site1' : define('DB_USER', 'site1');
	用户密码在这一行里 'password'：define('DB_PASSWORD', 'password');
	数据库链接信息为 define('DB_HOST', '127.0.0.1');
4，nginx配置在/etc/nginx/sites-enabled/下，以site1为例，


主菜单：
1，提供输入数字下载wordpress官方zip包到脚本当前执行目录下，官方压缩包链接为：https://wordpress.org/latest.zip
2，提供扫描当前目录下压缩包并进行选择安装
	{
		第一步先扫描脚本当前执行的目录下是否有zip/tar.gz/tgz压缩包并列出来给到选项选择，没有扫描到则提示用户输入下载链接，下载过程增加进度条显示，下载完成后提示用户输入站点名称并存为临时变量
		第二步根据用户的选择对压缩包进行解压缩zip压缩包用unzip，tar.gz/tgz用tar，解压出来的目录重命名为用户输入的站点名称，路径存为临时变量进行使用
		第三步检查数据库文件，先扫描解压出来站点目录下是否存在zip/sql文件（列出选项，如果都没有则提示用户没有sql文件询问是否继续安装），如果选择的文件为sql文件则sql文件路径存为临时变量，如果选择的文件为zip文件则zip文件解压缩出来得到的sql文件并对sql文件路径保存为临时变量，默认的数据库root登录是无需密码的，如果需要密码则提示用户输入密码，登录成功后执行下一步
		第四步创建数据库及数据库用户及导入数据库文件（上一步如无sql文件且用户继续安装则无需执行导入数据库这一步'source sql文件临时变量路径;'），以site1为例，用户名为 站点名称临时变量 ，密码为随机的16为英文数字，数据库名为 站点名称临时变量 ：
				create user '站点名称临时变量'@'localhost' IDENTIFIED BY 'password';
				create database 站点名称临时变量 character set utf8mb4;
				grant all on 站点名称临时变量.* to "站点名称临时变量"@"localhost" with grant option;
				flush privileges;
				use 站点名称临时变量;
				source sql文件临时变量路径;
				exit;
		第五步配置站点路径下的wp-config.php里的数据库信息
		第六步将站站点目录移动到 /var/www/ 下
		第七步列出 /etc/nginx/sites-enabled/ 的.conf配置文件作为选项，用户选择后复制并修改名称为 站点名称临时变量.conf，并修改里面的信息，修改下面三行的信息
				root /var/www/站点名称临时变量;
				access_log /var/log/nginx/站点名称临时变量.access.log;
				error_log /var/log/nginx/站点名称临时变量.error.log;
	}
3，备份
	{
		先扫描/var/www/下的站点
		1全部备份{
					按顺序一个一个进行，以site1为例，先获取/var/www/site1/wp-config.php里的数据库信息
					mysqldump -usite1 -ppassword site1 > /var/www/site1/site1.sql 使用mysqldump将site1数据库备份至var/www/site1/文件夹里
					然后将site1打包，打包的时候仅需打包site1，前面的/var/www/无需打包进去，打包保存到自定义路径$backup_path/site1-日期时间.tar.gz
					打包完成后删除/var/www/site1/site1.sql文件
					然后备份下一个
				}
		2备份单个{
					列出/var/www/下的所有站点并提供选项供用户选择，以site1为例：
					mysqldump -usite1 -ppassword site1 > /var/www/site1/site1.sql 使用mysqldump将site1数据库备份至var/www/site1/文件夹里
					然后将site1打包，打包的时候仅需打包site1，前面的/var/www/无需打包进去，打包保存到自定义路径$backup_path/site1-日期时间.tar.gz
					打包完成后删除/var/www/site1/site1.sql文件
				}
	}
4，还原
	{
		第一步先扫描$backup_path下的备份文件，由于由多个wordpress站点，需要先进行归类显示如site1则为选项1，选择1进入site1备份文件还原再列出site1的所有备份文件进行选择
		第二步根据用户的选择对压缩包进行解压缩，解压出来的目录重命名为site1
		第三步检查数据库文件，先扫描解压出来站点目录下是否存在sql文件，将sql文件路径存为临时变量，默认的数据库root登录是无需密码的，如果需要密码则提示用户输入密码，登录成功后执行下一步
		第四步创建数据库及数据库用户及导入数据库文件，以site1为例，先检查数据库site1是否存在（存在则删除），再检查用户site1是否存在（存在则删除），再创建数据库及用户：用户名为 site1 ，密码为随机的16为英文数字，数据库名为 site1 ：
				create user 'site1'@'localhost' IDENTIFIED BY 'password';
				create database site1 character set utf8mb4;
				grant all on site1.* to "site1"@"localhost" with grant option;
				flush privileges;
				use site1;
				source sql文件临时变量路径;
				exit;
		第五步配置站点路径下的wp-config.php里的数据库信息
		第六步将站站点目录移动到 /var/www/ 下
	}