<?php
/**
 * Plugin Name: Audit
 * Plugin URI: http://urbangiraffe.com/plugins/audit-trail/
 * Description: 全面详尽的WordPress管理日志系统，记录所有后台操作，包括用户登录时间、内容编辑历史、媒体文件操作等，便于管理员监控与追踪站点变更。
 * 是同时管理多个WordPress网站的理想选择 - 简单、轻量级，具有极低的资源消耗。强化了与Elementor等主流页面构建器的兼容性。
 * 未来功能考虑: 自动日志清理(已实现)，关键操作通知，智能日志聚合，以及对更多页面构建器的支持。
 * Version: 2.5.7
 * Author: <PERSON> / Vince
 * Author URI: http://urbangiraffe.com
 * Text Domain: audit-trail
 * Domain Path: /locale
 * Requires at least: 4.7
 * Tested up to: 6.4
 * Requires PHP: 7.4
 * Supports PHP: 7.4-8.4
 * ============================================================================================================
 * This software is provided "as is" and any express or implied warranties, including, but not limited to, the
 * implied warranties of merchantibility and fitness for a particular purpose are disclaimed. In no event shall
 * the copyright owner or contributors be liable for any direct, indirect, incidental, special, exemplary, or
 * consequential damages(including, but not limited to, procurement of substitute goods or services; loss of
 * use, data, or profits; or business interruption) however caused and on any theory of liability, whether in
 * contract, strict liability, or tort(including negligence or otherwise) arising in any way out of the use of
 * this software, even if advised of the possibility of such damage.
 * ============================================================================================================
 *
 * Available filters are:
 *   - audit_collect        - Passed an array of methods to monitor, return the array with any additions
 * 	- audit_show_operation - Passed an AT_Audit object, return the object with 'message' changed for type of operation
 *   - audit_show_item      - Passed an AT_Audit object, return the object with 'message' changed for type of item
 *   - audit_show_details   - Passed an AT_Audit object, return a message to display when the operation is clicked for more details
 *
 * Available actions are:
 *   - audit_listen - Passed the name of a method to monitor.  Add appropriate filters/actions to monitor the method
 */

define( 'AUDIT_TRAIL_VERSION', '2.5.7' );

/**
 * Audit plugin
 * 
 * 设计理念：保持简洁、轻量、低资源消耗
 * 
 * 未来功能建议：
 * - 日志自动清理：根据时间或数量阈值自动清理
 * - 关键操作通知：仅关键操作发送通知
 * - 智能日志聚合：合并短时间内的重复操作
 * - 轻量级统计：简单图表展示活动情况
 * - 搜索结果导出：仅导出必要记录
 *
 * @package Audit
 **/

class Audit_Trail {
	private static $instance = null;
	private $auditor;

	static function init() {
		if ( is_null( self::$instance ) ) {
			self::$instance = new Audit_Trail();

			// 设置中文为固定的语言
			if (function_exists('switch_to_locale') && strpos($_SERVER['REQUEST_URI'], 'audit-trail') !== false) {
				switch_to_locale('zh_CN');
				
				// 加载语言文件
				$mo_file = WP_PLUGIN_DIR . '/audit-trail/locale/zh_CN.mo';
				if (file_exists($mo_file)) {
					load_textdomain('audit-trail', $mo_file);
				}
			}

			load_plugin_textdomain( 'audit-trail', false, dirname( plugin_basename( __FILE__ ) ).'/locale/' );
			
			// 检查是否需要升级
			self::maybe_upgrade();
		}

		return self::$instance;
	}

	/**
	 * 应用语言设置为中文
	 */
	static function apply_language_setting() {
		// 固定使用中文语言
		if (function_exists('switch_to_locale') && strpos($_SERVER['REQUEST_URI'], 'audit-trail') !== false) {
			switch_to_locale('zh_CN');
			
			// 重新加载语言文件以确保翻译生效
			$mo_file = WP_PLUGIN_DIR . '/audit-trail/locale/zh_CN.mo';
			if (file_exists($mo_file)) {
				load_textdomain('audit-trail', $mo_file);
			}
		}
	}

	/**
	 * Constructor hooks all the appropriate filters and actions for the plugin, as well as creating the auditor
	 * object which monitors everything else
	 *
	 * @return void
	 **/

	function __construct() {
		// Check database is setup
		include( dirname( __FILE__).'/models/auditor.php' );
		include( dirname( __FILE__).'/models/audit.php' );
		include( dirname( __FILE__).'/models/batch-logger.php' );

		if ( is_admin() ) {
			if ( !class_exists( 'WP_List_Table' ) )
			    require_once ABSPATH . 'wp-admin/includes/class-wp-list-table.php';

			include( dirname( __FILE__).'/models/pager.php' );

			add_action( 'admin_menu', array( $this, 'admin_menu' ) );
			add_action( 'load-tools_page_audit-trail', array( $this, 'admin_head' ) );

			// Ajax functions
			if ( defined( 'DOING_AJAX' ) ) {
				include_once dirname( __FILE__ ).'/ajax.php';
				$this->ajax = new AuditAjax();
			}

			// 使用插件基名的正确方式
			$plugin_basename = plugin_basename( __FILE__ );
			add_filter( 'plugin_action_links_' . $plugin_basename, array( $this, 'plugin_settings' ), 10, 1 );
		}

		// 注册自动清理计划任务
		add_action('audit_trail_daily_cleanup', array($this, 'auto_cleanup'));
		
		// 检查是否需要设置自动清理计划任务
		if (!wp_next_scheduled('audit_trail_daily_cleanup')) {
			wp_schedule_event(time(), 'daily', 'audit_trail_daily_cleanup');
		}
		
		// 设置默认值（仅在选项不存在时）
		if (get_option('audit_auto_cleanup_enabled') === false) {
			add_option('audit_auto_cleanup_enabled', true); // 默认启用自动清理
		}
		
		if (get_option('audit_auto_cleanup_days') === false) {
			add_option('audit_auto_cleanup_days', 30); // 默认保留30天
		}
		
		if (get_option('audit_high_volume_mode') === false) {
			add_option('audit_high_volume_mode', true); // 默认启用高流量模式
		}
		
		if (get_option('audit_log_sample_rate') === false) {
			add_option('audit_log_sample_rate', 10); // 默认采样率10%
		}
		
		if (get_option('audit_methods') === false) {
			// 默认监控方法，除了"用户页访问"外都默认勾选
			$methods = array('post', 'attach', 'user', 'theme', 'link', 'category', 'comment', 'audit', 'plugin', 'pagebuilder');
			add_option('audit_methods', $methods);
		}

		// Add ourself to the Audit Trail functions
		$this->auditor = new AT_Auditor;
		$this->plugins_loaded();
	}

	/**
	 * 自动清理过期日志
	 */
	function auto_cleanup() {
		// 检查是否启用了自动清理
		if (!get_option('audit_auto_cleanup_enabled', true)) {
			return;
		}
		
		// 获取保留天数设置
		$days = intval(get_option('audit_auto_cleanup_days', 30));
		if ($days <= 0) {
			return;
		}
		
		// 执行清理
		AT_Audit::expire($days);
		
		// 记录清理时间
		update_option('audit_last_cleanup', current_time('mysql'));
	}
	
	/**
	 * 检查插件版本并执行必要的升级
	 */
	static function maybe_upgrade() {
		$current_version = get_option('audit_trail', '0.0');
		
		if (version_compare($current_version, AUDIT_TRAIL_VERSION, '<')) {
			// 执行升级操作
			self::upgrade_plugin($current_version);
			
			// 更新版本号
			update_option('audit_trail', AUDIT_TRAIL_VERSION);
		}
	}
	
	/**
	 * 根据版本执行不同的升级操作
	 */
	static function upgrade_plugin($from_version) {
		global $wpdb;
		
		// 添加默认设置
		if (!get_option('audit_auto_cleanup_days', false)) {
			add_option('audit_auto_cleanup_days', 30); // 默认保留30天日志
		}
		
		if (!get_option('audit_auto_cleanup_enabled', false)) {
			add_option('audit_auto_cleanup_enabled', true); // 默认启用自动清理
		}
		
		if (!get_option('audit_high_volume_mode', false)) {
			add_option('audit_high_volume_mode', true); // 默认启用高流量模式
		}
		
		if (!get_option('audit_log_sample_rate', false)) {
			add_option('audit_log_sample_rate', 10); // 默认采样率10%
		}

		// 设置默认监控方法，除了"用户页访问"外，其他默认勾选
		if (!get_option('audit_methods', false)) {
			$methods = array('post', 'attach', 'user', 'theme', 'link', 'category', 'comment', 'audit', 'plugin', 'pagebuilder');
			// 注意：没有包含'viewing'（用户页访问）
			add_option('audit_methods', $methods);
		}
		
		// 确保数据库表被创建
		AT_Audit::install_tables();
		
		// 升级表格结构，支持IPv6
		$table_name = $wpdb->prefix . 'audit_trail';
		$ipv6_upgraded = get_option('audit_ipv6_upgraded', false);
		
		if (!$ipv6_upgraded) {
			// 检查ip字段类型
			$row = $wpdb->get_row("SHOW COLUMNS FROM $table_name LIKE 'ip'");
			if ($row && strpos(strtolower($row->Type), 'int') !== false) {
				// 从int升级到varchar(45)以支持IPv6
				$wpdb->query("ALTER TABLE $table_name CHANGE `ip` `ip` VARCHAR(45) NULL DEFAULT NULL");
				update_option('audit_ipv6_upgraded', true);
			}
		}
		
		// 添加索引以提高性能
		$indexes_created = get_option('audit_indexes_created', false);
		
		if (!$indexes_created) {
			$wpdb->query("ALTER TABLE $table_name ADD INDEX `operation_index` (`operation`(32))");
			$wpdb->query("ALTER TABLE $table_name ADD INDEX `user_id_index` (`user_id`)");
			$wpdb->query("ALTER TABLE $table_name ADD INDEX `happened_at_index` (`happened_at`)");
			$wpdb->query("ALTER TABLE $table_name ADD INDEX `ip_index` (`ip`)");
			update_option('audit_indexes_created', true);
		}
	}

	function plugin_settings( $links ) {
		$settings_link = '<a href="tools.php?page=audit-trail.php">'.__('Trail', 'audit-trail' ).'</a>';
		array_unshift( $links, $settings_link );
		return $links;
	}

	/**
	 * After all the plugins have loaded this starts listening for all registered filters/actions
	 *
	 * @return void
	 **/

	function plugins_loaded() {
		$methods = get_option( 'audit_methods' );

		if ( !empty( $methods) && is_array( $methods ) ) {
			foreach( $methods AS $name)
				do_action( 'audit_listen', $name);
		}
	}

	function base_url() {
		return __FILE__;
	}


	/**
	 * Creates the database and upgrades any existing data
	 *
	 * @return void
	 **/

	static function plugin_activated() {
		global $wpdb;

		if ( get_option( 'audit_trail' ) == '0.1' || get_option( 'audit_trail' ) == 'true' )
			$wpdb->query( "DROP TABLE {$wpdb->prefix}audit_trail");

		if ( get_option( 'audit_trail' ) != '0.2' ) {
			$wpdb->query( "CREATE TABLE IF NOT EXISTS `{$wpdb->prefix}audit_trail`(
			  `id` int(11) NOT NULL auto_increment,
			  `operation` varchar(40) NOT NULL default '',
			  `user_id` int(11) NOT NULL,
  			`ip` varchar(45) NOT NULL default '',
			  `happened_at` datetime NOT NULL,
			  `item_id` int(11) default NULL,
			  `data` longtext,
			  `title` varchar(100) default NULL,
			  PRIMARY KEY ( `id`),
			  INDEX `idx_operation` (`operation`),
			  INDEX `idx_user_id` (`user_id`),
			  INDEX `idx_happened_at` (`happened_at`),
			  INDEX `idx_ip` (`ip`)
			)");
		}

		// 设置默认选项
		add_option('audit_auto_cleanup_days', 30); // 默认保留30天日志
		add_option('audit_auto_cleanup_enabled', true); // 默认启用自动清理
		add_option('audit_high_volume_mode', true); // 默认启用高流量模式
		add_option('audit_log_sample_rate', 1); // 默认1%记录
		
		// 优化计划任务，使用站点ID作为唯一标识
		$hook_name = 'audit_trail_daily_cleanup_' . get_current_blog_id();
		if (!wp_next_scheduled($hook_name)) {
			// 随机化执行时间，避免多个站点同时执行
			$random_time = time() + rand(0, 3600); // 随机延迟0-1小时
			wp_schedule_event($random_time, 'daily', $hook_name);
		}
		
		update_option( 'audit_trail', AUDIT_TRAIL_VERSION);
	}
	
	/**
	 * 插件停用时清除计划任务
	 */
	static function plugin_deactivated() {
		wp_clear_scheduled_hook('audit_trail_daily_cleanup');
	}

	/**
	 * Inject Audit Trail into the menu
	 *
	 * @return void
	 **/

	function admin_menu() {
		// 检查当前用户是否被禁止使用插件
		$current_user_id = get_current_user_id();
		$forbidden_users = explode(',', get_option('audit_forbidden_users', ''));
		
		if (in_array($current_user_id, $forbidden_users)) {
			return; // 用户被禁止使用，不添加菜单
		}
		
		if ( current_user_can( 'edit_plugins' ) || current_user_can( 'audit_trail' ) )
  			add_management_page( __("Audit",'audit-trail' ), __("Audit",'audit-trail' ), "publish_posts", basename( __FILE__), array( $this, "admin_screen") );
	}


	/**
	 * Inserts the edit box into the edit post/page area
	 *
	 * @return void
	 **/

	function edit_box() {
		global $post;
		$this->render( 'edit_box', array( 'trail' => AT_Audit::get_by_post( $post->ID) ));
	}

	function edit_box_advanced() {
		global $post;
		$this->render( 'edit_box_25', array( 'trail' => AT_Audit::get_by_post( $post->ID) ));
	}

	function submenu( $inwrap = false) {
		// Decide what to do
		$sub = isset( $_GET['sub']) ? $_GET['sub'] : '';
		if ( !in_array( $sub, array( 'options' ) ) )
			$sub = '';

		if ( $inwrap == true)
			$this->render( 'submenu', array( 'sub' => $sub, 'class' => 'class="subsubsub"', 'trail' => ' | ' ) );

		return $sub;
	}

	/**
	 * Displays the admin screen
	 *
	 * @return void
	 **/

	function admin_screen() {
		// 检查当前用户是否被禁止使用插件
		$current_user_id = get_current_user_id();
		$forbidden_users = explode(',', get_option('audit_forbidden_users', ''));
		
		if (in_array($current_user_id, $forbidden_users)) {
			wp_die(__('You are not allowed to access this page.', 'audit-trail'));
			return;
		}
		
		if ( !current_user_can( 'edit_plugins' ) && !current_user_can( 'audit_trail' ) )
			return;

		// Decide what to do
		$sub = $this->submenu();

		AT_Audit::expire( get_option( 'audit_expiry' ) === false ? 30 : get_option( 'audit_expiry' ) );

		if ( $sub == '' )
			$this->screen_trail();
		else if ( $sub == 'options' )
			$this->screen_options();
	}


	/**
	 * Displays the audit trail log
	 *
	 * @return void
	 **/

	function screen_trail() {
		$table = new Audit_Trail_Table();
		$table->prepare_items();

		$this->render( 'trail', array( 'table' => $table ) );
	}


	/**
	 * Display audit trail options
	 *
	 * @return void
	 **/

	function screen_options() {
		$saved = false;
		
		if ( isset( $_POST['save']) && check_admin_referer( 'audittrail-update_options' ) ) {
			update_option( 'audit_methods',    stripslashes_deep( $_POST['methods'] ) );
			update_option( 'audit_expiry',     intval( $_POST['expiry']) );
			update_option( 'audit_post',       isset( $_POST['post']) ? true : false);
			update_option( 'audit_post_order', isset( $_POST['post_order']) ? true : false);
			update_option( 'audit_version',    isset( $_POST['version']) ? 'true' : 'false' );
			update_option( 'audit_ignore',     preg_replace( '/[^0-9,]/', '', $_POST['ignore_users']) );
			update_option( 'audit_forbidden_users', preg_replace( '/[^0-9,]/', '', $_POST['forbidden_users']) );
			update_option( 'audit_error_log',  isset( $_POST['error_log'] ) ? true : false );
			
			// 保存新增的高流量模式设置
			update_option( 'audit_auto_cleanup_enabled', isset( $_POST['auto_cleanup_enabled'] ) ? true : false );
			update_option( 'audit_high_volume_mode', isset( $_POST['high_volume_mode'] ) ? true : false );
			update_option( 'audit_log_sample_rate', intval( $_POST['log_sample_rate'] ) );
			update_option( 'audit_exclude_paths', sanitize_textarea_field( $_POST['exclude_paths'] ) );
			
			$saved = true;
			$this->render_message( __( 'Options have been updated', 'audit-trail' ) );
		}
		
		// 处理手动清理请求 - 移到这里以确保独立处理
		if (isset($_POST['manual_cleanup']) && check_admin_referer( 'audittrail-update_options' )) {
			$days = intval($_POST['manual_cleanup_days']);
			if ($days > 0) {
				$deleted = AT_Audit::delete_entries_older_than($days);
				$this->render_message(sprintf(__('已清理 %d 条%d天前的日志记录', 'audit-trail'), $deleted, $days));
			}
		}
		
		// 处理按操作类型批量删除 - 移到这里以确保独立处理
		if (isset($_POST['delete_by_operation']) && check_admin_referer( 'audittrail-update_options' )) {
			if (isset($_POST['operation_type']) && !empty($_POST['operation_type'])) {
				$operation = sanitize_text_field($_POST['operation_type']);
				if (!empty($operation)) {
					$deleted = AT_Audit::delete_by_operation($operation);
					$this->render_message(sprintf(__('已删除 %d 条"%s"类型的日志记录', 'audit-trail'), $deleted, $operation));
				}
			} else {
				$this->render_message(__('请选择要删除的操作类型', 'audit-trail'));
			}
		}

		$current = get_option( 'audit_methods' );
		if ( !is_array( $current) )
			$current = array();

		$methods = apply_filters( 'audit_collect', array() );
		if ( is_array( $methods) )
			ksort( $methods);

		$error_log = get_option( 'audit_error_log' );

		$expiry = get_option( 'audit_expiry' );
		if ( $expiry === false)
			$expiry = 30;

		$forbidden_users = get_option( 'audit_forbidden_users', '' );

		$this->render( 'options', array( 
			'methods' => $methods, 
			'current' => $current, 
			'expiry' => $expiry, 
			'error_log' => $error_log, 
			'post' => get_option( 'audit_post' ), 
			'post_order' => get_option( 'audit_post_order' ), 
			'version' => get_option( 'audit_version' ) == 'false' ? false : true, 
			'ignore_users' => get_option( 'audit_ignore' ),
			'forbidden_users' => $forbidden_users,
			'saved' => $saved
		));
	}

	function admin_head() {
		wp_enqueue_style( 'audit-trail', plugin_dir_url( __FILE__ ).'admin.css' );
		
		// 确保在admin页面加载时应用中文语言
		self::apply_language_setting();
		
		// 添加安全头部，防止XSS攻击
		if (!headers_sent()) {
			header('X-XSS-Protection: 1; mode=block');
			header('X-Content-Type-Options: nosniff');
			header('X-Frame-Options: SAMEORIGIN');
			header('Referrer-Policy: strict-origin-when-cross-origin');
			header('Content-Security-Policy: default-src \'self\'; script-src \'self\' \'unsafe-inline\' \'unsafe-eval\' https://cdnjs.cloudflare.com https://code.jquery.com; style-src \'self\' \'unsafe-inline\' https://cdnjs.cloudflare.com; img-src \'self\' data:; font-src \'self\' data:;');
		}
	}

	function version() {
		$plugin_data = implode( '', file( __FILE__) );

		if ( preg_match( '|Version:(.*)|i', $plugin_data, $version) )
			return trim( $version[1]);
		return '';
	}

	private function render( $template, $template_vars = array() ) {
		foreach ( $template_vars AS $key => $val ) {
			$$key = $val;
		}

		if ( file_exists( dirname( __FILE__ )."/view/admin/$template.php" ) )
			include dirname( __FILE__ )."/view/admin/$template.php";
	}

	private function capture( $ug_name, $ug_vars = array() ) {
		ob_start();

		$this->render( $ug_name, $ug_vars );
		$output = ob_get_contents();

		ob_end_clean();
		return $output;
	}

	private function render_message( $message, $timeout = 0 ) {
		?>
<div class="updated" id="message" onclick="this.parentNode.removeChild(this)">
	<p><?php echo esc_html( $message ) ?></p>
</div>
	<?php
	}

	function process_options()
	{
		// 此函数已废弃，为避免混淆，内部逻辑已移至screen_options方法
		return;
	}
}

/**
 * Standard plugin setup
 **/
register_activation_hook( __FILE__, array( 'Audit_Trail', 'plugin_activated' ) );
register_deactivation_hook( __FILE__, array( 'Audit_Trail', 'plugin_deactivated' ) );

// Instantiate
add_action( 'plugins_loaded', array( 'Audit_Trail', 'init' ) );
