<?php

class Audit_Trail_Table extends WP_List_Table {
	function __construct(){
		global $status, $page;

		// 加载语言
		$this->load_translations();

		//Set parent defaults
		parent::__construct( array(
			'singular'  => 'item',     //singular name of the listed records
			'plural'    => 'items',    //plural name of the listed records
			'ajax'      => false        //does this table support ajax?
		) );
	}
	
	/**
	 * 加载翻译文件
	 */
	function load_translations() {
		// 固定使用中文
		if (function_exists('switch_to_locale')) {
			switch_to_locale('zh_CN');
		}
		
		$mo_file = WP_PLUGIN_DIR . '/audit-trail/locale/zh_CN.mo';
		if (file_exists($mo_file)) {
			load_textdomain('audit-trail', $mo_file);
		}
	}

	function column_user_id( $item ){
		// 处理template_redirect操作的访客显示
		if ($item->operation == 'template_redirect') {
			$data = json_decode($item->data);
			if (is_object($data) && isset($data->user_type) && $data->user_type == 'visitor') {
				return '<span class="visitor-label">访客</span>';
			} else if (is_object($data) && isset($data->user_login)) {
				// 显示数据中记录的用户名
				if ($item->user_id > 0) {
					return '<a href="user-edit.php?user_id='.esc_attr($item->user_id).'&amp;wp_http_referer=%2Fsite%2Fwp-admin%2Fusers.php">'.esc_html($data->user_login).'</a>';
				} else {
					return esc_html($data->user_login);
				}
			}
		}
	
		// 常规用户处理
		if ( $item->user_id > 0 )
			return '<a href="user-edit.php?user_id='.esc_attr( $item->user_id ).'&amp;wp_http_referer=%2Fsite%2Fwp-admin%2Fusers.php">'.esc_html( $item->username ).'</a>';
		return '';
	}

	function column_happened_at( $item ) {
		return date_i18n( get_option( 'date_format' ), $item->happened_at ).' '.gmdate( get_option( 'time_format' ), $item->happened_at );
	}

	function column_ip( $item ) {
		// 检查IP值是否为数字（IPv4）
		if (!empty($item->ip) && is_numeric($item->ip)) {
			$ip_address = long2ip($item->ip);
			return '<a href="http://urbangiraffe.com/map/?ip='.esc_attr($ip_address).'&amp;from=audittrail">'.esc_html($ip_address).'</a>';
		} else if ($item->ip == 0) {
			// IPv6或未知IP
			return __('Unknown IP', 'audit-trail');
		}
		
		return esc_html($item->ip);
	}

	function column_item_id( $item ) {
		return $item->get_item();
	}

	function column_operation( $item ) {
		return $item->get_operation();
	}

	function column_cb($item){
		return sprintf(
			'<input type="checkbox" name="%1$s[]" value="%2$s" />',
			/*$1%s*/ $this->_args['singular'],  //Let's simply repurpose the table's singular label ("movie")
			/*$2%s*/ $item->id                //The value of the checkbox should be the record's id
		);
	}

	function get_columns(){
		$columns = array(
			'cb'          => '<input type="checkbox" />', //Render a checkbox instead of text
			'user_id'     => __( 'User', 'audit-trail' ),
			'operation'   => __( '操作类型', 'audit-trail' ),
			'item_id'     => __( 'Target', 'audit-trail' ),
			'happened_at' => __( 'Date', 'audit-trail' ),
			'ip'          => __( 'IP', 'audit-trail' ),
		);
		return $columns;
	}

	function get_sortable_columns() {
		$sortable_columns = array(
			'user_id'     => array( 'user_id',false ),
			'operation'   => array( 'operation',false),
			'item_id'     => array( 'item_id',false ),
			'happened_at' => array( 'item_id',true ),
			'ip'          => array( 'item_id',false ),
		);
		return $sortable_columns;
	}

	function get_bulk_actions() {
		$actions = array(
			'delete' => __( 'Delete', 'audit-trail' )
		);
		return $actions;
	}

	function process_bulk_action() {
		if ( 'delete' === $this->current_action() ) {
			foreach( $_POST['item'] AS $id ) {
				AT_Audit::delete( intval( $id ) );
			}
		}
	}

	function prepare_items() {
		global $wpdb;

		$per_page = 25;
		$hidden   = array();
		$columns  = $this->get_columns();
		$sortable = $this->get_sortable_columns();

		$this->_column_headers = array( $columns, $hidden, $sortable );

		// Process any stuff
		$this->process_bulk_action();

		$orderby = ( ! empty( $_GET['orderby'] ) ) ? $_GET['orderby'] : 'id';
		$order   = ( ! empty($_GET['order'] ) ) ? strtolower( $_GET['order'] ) : 'desc';

		if ( !in_array( $orderby, array_keys( $this->get_sortable_columns() ) ) )
			$orderby = 'id';

		if ( !in_array( $order, array( 'asc', 'desc' ) ) )
			$order = 'desc';

		// 构建搜索查询
		$where = '';
		$search_params = array();
		
		// 用户名搜索
		if (!empty($_GET['search_user'])) {
			$search_user = sanitize_text_field($_GET['search_user']);
			$where .= " AND ({$wpdb->users}.user_login LIKE %s OR {$wpdb->users}.user_nicename LIKE %s)";
			$search_params[] = '%' . $wpdb->esc_like($search_user) . '%';
			$search_params[] = '%' . $wpdb->esc_like($search_user) . '%';
		}
		
		// 操作类型搜索
		if (!empty($_GET['search_operation'])) {
			$search_operation = sanitize_text_field($_GET['search_operation']);
			$where .= " AND {$wpdb->prefix}audit_trail.operation LIKE %s";
			$search_params[] = '%' . $wpdb->esc_like($search_operation) . '%';
		}
		
		// IP地址搜索
		if (!empty($_GET['search_ip'])) {
			$search_ip = sanitize_text_field($_GET['search_ip']);
			// 将IP地址转换为数字处理
			if (filter_var($search_ip, FILTER_VALIDATE_IP, FILTER_FLAG_IPV4)) {
				$ip_num = sprintf('%u', ip2long($search_ip));
				$where .= " AND {$wpdb->prefix}audit_trail.ip = %d";
				$search_params[] = $ip_num;
			} else {
				// 模糊匹配IP地址（以防万一是部分IP）
				$where .= " AND %s LIKE CONCAT('%', INET_NTOA({$wpdb->prefix}audit_trail.ip), '%')";
				$search_params[] = $search_ip;
			}
		}
		
		// 日期范围搜索
		if (!empty($_GET['search_date_from'])) {
			$date_from = sanitize_text_field($_GET['search_date_from']);
			$where .= " AND {$wpdb->prefix}audit_trail.happened_at >= %s";
			$search_params[] = $date_from . ' 00:00:00';
		}
		
		if (!empty($_GET['search_date_to'])) {
			$date_to = sanitize_text_field($_GET['search_date_to']);
			$where .= " AND {$wpdb->prefix}audit_trail.happened_at <= %s";
			$search_params[] = $date_to . ' 23:59:59';
		}
		
		// 排除"访问页面"日志
		if (isset($_GET['exclude_page_views']) && $_GET['exclude_page_views'] == '1') {
			$where .= " AND {$wpdb->prefix}audit_trail.operation != %s";
			$search_params[] = 'template_redirect';
		}

		// 获取总记录数
		$count_query = "SELECT COUNT(*) FROM {$wpdb->prefix}audit_trail 
					  LEFT JOIN {$wpdb->users} ON {$wpdb->users}.ID={$wpdb->prefix}audit_trail.user_id 
					  WHERE 1=1 $where";
					  
		if (!empty($search_params)) {
			$total_items = $wpdb->get_var($wpdb->prepare($count_query, $search_params));
		} else {
			$total_items = $wpdb->get_var($count_query);
		}

		// 构建主查询
		$query = "SELECT {$wpdb->prefix}audit_trail.*,{$wpdb->users}.user_nicename AS username 
				FROM {$wpdb->prefix}audit_trail 
				LEFT JOIN {$wpdb->users} ON {$wpdb->users}.ID={$wpdb->prefix}audit_trail.user_id 
				WHERE 1=1 $where 
				ORDER BY $orderby $order 
				LIMIT %d,%d";
				
		$query_params = $search_params;
		$query_params[] = ($this->get_pagenum() - 1) * $per_page;
		$query_params[] = $per_page;
		
		if (!empty($search_params)) {
			$rows = $wpdb->get_results($wpdb->prepare($query, $query_params));
		} else {
			$rows = $wpdb->get_results($wpdb->prepare("SELECT {$wpdb->prefix}audit_trail.*,{$wpdb->users}.user_nicename AS username FROM {$wpdb->prefix}audit_trail LEFT JOIN {$wpdb->users} ON {$wpdb->users}.ID={$wpdb->prefix}audit_trail.user_id ORDER BY ".$orderby.' '.$order." LIMIT %d,%d", ($this->get_pagenum() - 1) * $per_page, $per_page));
		}
		
		$data = array();
		foreach ((array)$rows AS $row) {
			$data[] = new AT_Audit($row);
		}

		$this->items = $data;

		$this->set_pagination_args(array(
			'total_items' => $total_items,                  //WE have to calculate the total number of items
			'per_page'    => $per_page,                     //WE have to determine how many items to show on a page
			'total_pages' => ceil($total_items/$per_page)   //WE have to calculate the total number of pages
		));
	}

	function single_row( $item ) {
		$row_class = '';
		
		// 添加不同操作类型的样式
		if (in_array($item->operation, array('wp_login_failed', 'delete_post', 'delete_attachment', 'delete_user', 'delete_comment', 'delete_link'))) {
			$row_class = 'log-error';
		} elseif (in_array($item->operation, array('publish_to_trash', 'draft_to_trash', 'pending_to_trash'))) {
			$row_class = 'log-warning';
		} elseif (in_array($item->operation, array('draft_to_publish', 'private_to_published', 'trash_to_publish', 'pending_to_publish'))) {
			$row_class = 'log-success';
		}
		
		echo '<tr class="' . $row_class . '">';
		$this->single_row_columns( $item );
		echo '</tr>';
	}
}
