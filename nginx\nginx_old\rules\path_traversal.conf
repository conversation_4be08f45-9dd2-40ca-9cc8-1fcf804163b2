# 防止路径遍历攻击
# 禁止通过URL路径中的双斜杠访问文件
# 例如: /path//file.php
location ~ //  {
    return 404;
}

# 禁止通过URL路径中的多个点访问文件
# 例如: /path/../../file.php
location ~ /\. {
    return 404;
}

# 明确禁止诸如image.jpg/file.php这样的路径尝试执行PHP
# 注意：这是一种常见的PHP路径解析漏洞利用方式
location ~ "^(.*)\.([a-z]+)\.(php|ph(p|tml|p3|p4|p5|ar|t|s))$" {
    # 排除wp-admin路径，因为有些WordPress后台功能可能使用这种格式
    location ~ ^/wp-admin/ {
        try_files $uri =404;
        fastcgi_pass unix:/var/run/php/php7.4-fpm.sock;
        include fastcgi_params;
        fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
    }
    
    # 拒绝所有其他路径
    return 403;
} 