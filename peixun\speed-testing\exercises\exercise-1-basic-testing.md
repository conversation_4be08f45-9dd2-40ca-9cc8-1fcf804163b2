# 练习1：基础网站测速操作

## 🎯 练习目标
掌握使用主流测速工具进行网站性能测试的基本操作

## 📋 练习准备

### 测试网站列表
我们将测试以下几个不同类型的网站：

1. **国内网站**：https://www.baidu.com
2. **海外网站**：https://www.google.com
3. **电商网站**：https://www.amazon.com
4. **新闻网站**：https://www.cnn.com
5. **客户网站**：（使用您实际客户的网站）

### 需要的工具
- 浏览器（Chrome推荐）
- 网络连接
- 记录表格（Excel或纸质）

## 🛠️ 操作步骤

### 步骤1：使用GTmetrix测试

#### 1.1 基本测试
1. 打开 https://gtmetrix.com
2. 在URL输入框输入：https://www.baidu.com
3. 点击"Test your site"按钮
4. 等待测试完成（约1-2分钟）

#### 1.2 记录结果
创建如下表格记录结果：

| 网站 | Performance Score | Structure Score | Load Time | Page Size | Requests |
|------|-------------------|-----------------|-----------|-----------|----------|
| 百度 |                   |                 |           |           |          |
| Google |                 |                 |           |           |          |
| Amazon |                 |                 |           |           |          |

#### 1.3 高级设置测试
1. 点击"Options"展开高级选项
2. 选择测试地点：
   - Hong Kong, China
   - Singapore
   - Vancouver, Canada
3. 选择设备：Desktop / Mobile
4. 重新测试同一个网站，对比不同地点的结果

### 步骤2：使用PageSpeed Insights测试

#### 2.1 基本操作
1. 打开 https://pagespeed.web.dev
2. 输入网站URL
3. 点击"分析"按钮
4. 等待测试完成

#### 2.2 关注指标
重点关注以下指标：
- **性能评分**（0-100分）
- **FCP**（First Contentful Paint）
- **LCP**（Largest Contentful Paint）
- **FID**（First Input Delay）
- **CLS**（Cumulative Layout Shift）

#### 2.3 移动端vs桌面端
- 分别查看移动端和桌面端评分
- 对比两者差异
- 记录主要问题

### 步骤3：使用17CE测试国内访问

#### 3.1 基本测试
1. 打开 https://www.17ce.com
2. 输入网站URL
3. 选择"HTTP"测试类型
4. 点击"现在测试"

#### 3.2 选择测试节点
建议选择以下城市进行测试：
- 北京（电信/联通/移动）
- 上海（电信/联通/移动）
- 广州（电信/联通/移动）
- 深圳（电信/联通/移动）

#### 3.3 分析结果
关注以下数据：
- **响应时间**：各城市的访问速度
- **网络类型差异**：电信vs联通vs移动
- **失败率**：是否有访问失败的节点

### 步骤4：使用Chrome DevTools分析

#### 4.1 打开开发者工具
1. 在Chrome中打开要测试的网站
2. 按F12打开开发者工具
3. 切换到"Network"标签
4. 刷新页面（Ctrl+F5）

#### 4.2 分析网络请求
观察以下信息：
- **总请求数**：页面加载了多少个文件
- **总大小**：所有文件的总大小
- **加载时间**：各个文件的加载时间
- **最慢的请求**：哪个文件加载最慢

#### 4.3 性能分析
1. 切换到"Performance"标签
2. 点击录制按钮
3. 刷新页面
4. 停止录制
5. 分析性能时间线

## 📊 结果分析练习

### 练习A：对比分析
完成所有测试后，制作对比表格：

| 网站类型 | GTmetrix评分 | PageSpeed评分 | 17CE平均时间 | 主要问题 |
|----------|--------------|---------------|--------------|----------|
| 国内网站 |              |               |              |          |
| 海外网站 |              |               |              |          |
| 电商网站 |              |               |              |          |

### 练习B：问题识别
针对每个测试网站，识别主要性能问题：

1. **百度网站**
   - 主要问题：
   - 优化建议：

2. **Google网站**
   - 主要问题：
   - 优化建议：

3. **Amazon网站**
   - 主要问题：
   - 优化建议：

### 练习C：跨国访问分析
选择一个海外网站，对比国内外访问速度：

**测试网站**：https://www.google.com

| 测试地点 | 工具 | 加载时间 | 主要延迟来源 |
|----------|------|----------|--------------|
| 香港 | GTmetrix |  |  |
| 新加坡 | GTmetrix |  |  |
| 北京 | 17CE |  |  |
| 上海 | 17CE |  |  |

**分析结论**：
- 国内外速度差异：
- 主要原因：
- 解决建议：

## ✅ 检查清单

完成练习后，请确认以下项目：

- [ ] 成功使用GTmetrix测试至少3个网站
- [ ] 成功使用PageSpeed Insights测试至少3个网站
- [ ] 成功使用17CE测试至少2个网站
- [ ] 学会使用Chrome DevTools分析网络请求
- [ ] 能够识别网站的主要性能问题
- [ ] 理解国内外访问速度的差异
- [ ] 能够提出基本的优化建议

## 🚨 常见问题

### Q1：测试结果不一致怎么办？
**A：** 这很正常，因为：
- 网络状况实时变化
- 服务器负载不同
- 测试节点位置不同
建议多次测试取平均值。

### Q2：某些网站无法测试怎么办？
**A：** 可能原因：
- 网站有访问限制
- 网络连接问题
- 测试工具被屏蔽
尝试更换测试工具或网络环境。

### Q3：如何判断测试结果好坏？
**A：** 参考标准：
- 加载时间 < 3秒：优秀
- 加载时间 3-5秒：一般
- 加载时间 > 5秒：需要优化

## 🎯 进阶挑战

1. **自动化测试**：研究如何使用API进行批量测试
2. **监控设置**：设置定期监控重要网站性能
3. **报告制作**：为客户制作专业的测速报告
4. **优化实践**：选择一个慢网站，实际进行优化

## 📚 扩展学习

- 学习更多测速工具的高级功能
- 了解Web性能优化的最佳实践
- 研究不同行业网站的性能标准
- 关注Web性能优化的最新技术趋势

---

**练习完成后，请将结果整理成报告，为下一个练习做准备！** 🚀
