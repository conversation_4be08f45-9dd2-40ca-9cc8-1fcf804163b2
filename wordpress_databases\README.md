# WordPress 数据库扫描工具

这是一个用于扫描 WordPress 数据库中可能存在的垃圾内容、恶意代码和其他异常的工具。

## 功能

- ✅ 自动查找服务器上的 WordPress 站点
- ✅ 自动提取 WordPress 数据库配置
- ✅ 扫描数据库中的可疑内容：
  - ✅ 包含恶意代码的文章（eval、base64_decode 等）
  - ✅ 包含垃圾关键词的文章（色情、成人等内容）
  - ✅ 可疑用户账号
  - ✅ 可疑选项值
  - ✅ 可疑评论内容
  - ✅ 可疑元数据
  - ✅ 日期异常（未来日期）
- ✅ 生成 HTML 格式的扫描报告
- ✅ 支持多站点扫描和汇总报告

## 安装和使用

### 依赖项

- Bash 4.0+
- MySQL 客户端
- grep, awk, sed 等标准 Unix 工具

### 安装

1. 从 GitHub 下载脚本：

```bash
git clone https://github.com/yourusername/wordpress-db-scanner.git
cd wordpress-db-scanner
chmod +x wp_scanner_merged.sh
```

### 运行

```bash
./wp_scanner_merged.sh
```

## 报告输出

根据扫描结果，报告将按以下方式输出：

1. 如果 WordPress 站点位于 /var/www/ 目录下，报告将输出到 /var/www/html/wp_scan_report_日期/ 目录
2. 其他情况下，报告将被打包为 用户主目录/wp_scan_report_日期.tar.gz

## 输出示例

扫描成功完成后，您将获得类似下面的 HTML 报告：

- 总报告（index.html）：包含所有扫描站点的汇总信息
  - 概览统计（WordPress数据库总数、总大小、文章数、用户数等）
  - 站点列表（支持搜索和分页功能）
  - 可疑内容总体分类统计
- 站点报告（site/report.html）：每个站点的详细扫描结果，包括：
  - 基本信息（WordPress 版本、数据库大小等）
  - 可疑文章列表（选项卡式界面）
  - 可疑用户列表
  - 可疑选项列表
  - 可疑评论列表
  - 可疑元数据列表
  - 日期异常列表

## 最近更新

### 版本 1.0.1 (2025-05-12)
- ✅ 修复了可疑项计数逻辑，防止出现负数统计
- ✅ 改进了空结果文件的处理方式
- ✅ 优化了报告生成逻辑
- ✅ 改进了WordPress版本检测方法
- ✅ 添加了选项卡式报告界面，提升用户体验
- ✅ 美化了总报告页面，增加更多统计数据和互动功能

## 开发计划

- ✅ 基础扫描功能
- ✅ HTML报告生成
- ✅ 自动查找WordPress站点
- ✅ 详细的可疑内容扫描
- ✅ 美化HTML报告界面
- ✅ 添加交互式报告功能（搜索、分页）
- ✅ 合并所有功能到单个文件


## 贡献指南

欢迎贡献代码、报告问题或提出改进建议。请提交 issue 或 pull request。

## 许可证

此工具基于 GPL-3.0 许可证开源。 