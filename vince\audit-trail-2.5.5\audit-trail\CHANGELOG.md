# Audit Changelog

## 2.5.5 - 2023-12-15
* **PHP兼容性优化**: 增强与PHP 7.4-8.4的兼容性
  * 优化Elementor相关钩子处理，添加安全检查防止在Elementor未激活时报错
  * 改进JSON数据编码，防止特殊字符导致的JSON编码错误
  * 使用try/catch捕获可能的异常，确保插件功能稳定
  * 完整支持PHP 7.4到PHP 8.4的各个版本
* **数据处理优化**:
  * 完善敏感数据处理机制，防止数据泄露
  * 增强页面构建器集成的错误处理
  * 优化数据库操作，提高插件稳定性
* **其他改进**:
  * 更新作者信息和版本号
  * 统一数组声明语法，提高代码可读性
  * 修复潜在的类型转换问题

## 2.5.4 - 2023-12-11
* **多站点优化**: 改进当多个WordPress站点使用此插件时的资源使用情况
  * 默认启用高流量模式，页面访问日志采样率设为1%
  * 优化计划任务执行，防止多站点同时运行
  * 使用INSERT IGNORE优化数据库插入操作
  * 改进静态资源检查逻辑，提升性能
* **功能修复**:
  * 修复按操作类型批量删除功能
  * 同步操作类型列表
  * 修复SQL查询参数错误
  * 增加删除操作的日志记录
  * 优化表单处理
* **代码优化**:
  * 改进删除方法
  * 分离手动清理功能
  * 更新版本号保持一致
  * 移除重复的表单处理逻辑，避免冲突

## 2.5.3 - 2023-11-27
* 新增功能: 支持记录Elementor、Beaver Builder、Divi等页面构建器操作
* 优化页面性能: 添加高流量模式，避免页面访问记录过多导致数据库过大
* 自动清理机制: 增加自动清理功能，定期清理旧日志
* 安全性改进: 加强SQL查询安全性，防止SQL注入
* 用户体验优化: 改进搜索和过滤功能
* 更新中文翻译文件

## 2.5.2 - 2023-11-15
* 功能改进: 按操作类型批量删除功能
* 优化: 减少不必要的数据库查询
* 修复: 日志详情显示问题
* 改进: 日志采样算法，降低对高流量站点的负载
* 安全性: 增强数据处理安全性

## 2.5.1 - 2023-10-30
* 修复: 与WordPress 6.3兼容性问题
* 添加: 支持新的WordPress钩子
* 改进: 日志条目清理流程

## 2.5.0 - 2023-09-05
* 重要更新: 完全重写内部架构，提高性能
* 添加: 全面的中文支持
* 新增: 高级过滤系统，便于管理大量日志
* 改进: 批量操作功能，简化日志管理
* 优化: 前端界面，更清晰的日志显示
* 添加: 支持记录页面构建器操作

## 2.4.0 - 2015-08-10
* 更新: 列表表格以匹配WP 4.3样式

## 2.3.1 - 2015-02-22
* 修复: 保存文章的错误

## 2.3.0 - 2014-05-30
* 修复: 使插件兼容WP 3.9
* 移除: 对jQuery UI Dialog的依赖

## 2.2.1 - 2013-12-15
* 修复: 表格中某些项目显示的问题

## 2.2.0 - 2013-10-07
* 添加: 显示条目相对于当前时间发生的时间
* 更改: 外观匹配最新的WP样式
* 修复: 日期排序问题

## 2.1.0 - 2012-05-15
* 添加: 搜索功能
* 删除: 对WP < 2.8的支持
* 添加: 记录自身操作的功能

## 2.0.4 - 2011-08-20
* 修复: 由于未定义函数导致的激活错误
* 更新: 插件使用WP2.8表格

## 2.0.3 - 2011-03-10
* 修复: 登录跟踪
* 修复: 自动过期选项
* 修复: IE7中的选项对话框

## 2.0.2 - 2010-11-05
* 添加: 清除所有条目的按钮

## 2.0.1 - 2010-07-18
* 改进: 'happened_at'值的格式
* 修复: 返回审核页面时记住屏幕选项

## 2.0.0 - 2010-04-02
* 完全重写: 使用自定义管理表格类(需要WP 2.5)
* 修复: 在WP 2.7中的显示问题
* 改进: 列宽的计算

## 1.0.8 - 2009-12-10
* 修复: 在WP 2.7中的显示问题

## 1.0.7 - 2009-08-15
* 修复: 批量升级的问题

## 1.0.6 - 2009-03-20
* 更新: 支持WP 2.5

## 1.0.5 - 2008-11-05
* 修复: getUserName中的错误
* 添加: WP-Cron的钩子

## 1.0.4 - 2008-07-22
* 添加: 指定用户ID的过滤器
* 添加: 更多监控函数
* 添加: 审核项目查看时间的记录

## 1.0.3 - 2008-04-15
* 添加: 翻译支持
* 更改: 从美式格式改为国际时间显示
* 添加: 'post-status-changed'事件

## 1.0.2 - 2008-02-10
* 移除: 一些调试代码
* 修复: 几个过滤器

## 1.0.1 - 2008-01-20
* 替换: 使用json替代serialize以便更容易互操作
* 改进: 显示消息的过滤
* 添加: 记住显示过滤器
* 显示: 登录用户信息

## 1.0.0 - 2007-12-15
* 重写: 数据库审核功能 