<?php

/* ============================================================================================================
	 This software is provided "as is" and any express or implied warranties, including, but not limited to, the
	 implied warranties of merchantibility and fitness for a particular purpose are disclaimed. In no event shall
	 the copyright owner or contributors be liable for any direct, indirect, incidental, special, exemplary, or
	 consequential damages (including, but not limited to, procurement of substitute goods or services; loss of
	 use, data, or profits; or business interruption) however caused and on any theory of liability, whether in
	 contract, strict liability, or tort (including negligence or otherwise) arising in any way out of the use of
	 this software, even if advised of the possibility of such damage.

	 This software is provided free-to-use, but is not free software.  The copyright and ownership remains
	 entirely with the author.  Please distribute and use as necessary, in a personal or commercial environment,
	 but it cannot be sold or re-used without express consent from the author.
   ============================================================================================================ */

/**
 * Provide Audit Trail AJAX
 *
 * @package Audit Trail
 * <AUTHOR> Godley
 **/

class AuditAjax {
	function __construct() {
		add_action( 'init', array( &$this, 'init' ) );

		if ( current_user_can( 'manage_options' ) ) {
			add_action( 'wp_ajax_at_view', array( &$this, 'at_view' ) );
			add_action( 'wp_ajax_at_close', array( &$this, 'at_close' ) );
			add_action( 'wp_ajax_at_get_users', array( &$this, 'at_get_users' ) );
		}

		// 添加CSV导出AJAX操作
		add_action( 'wp_ajax_audit_export_csv', array( &$this, 'export_csv' ) );
	}

	function init() {
		// 加载语言
		$this->load_translations();
	}
	
	/**
	 * 加载翻译文件
	 */
	function load_translations() {
		// 固定使用中文
		if (function_exists('switch_to_locale')) {
			switch_to_locale('zh_CN');
		}
		
		$mo_file = WP_PLUGIN_DIR . '/audit-trail/locale/zh_CN.mo';
		if (file_exists($mo_file)) {
			load_textdomain('audit-trail', $mo_file);
		}
	}

	function at_get_users() {
		if (!check_ajax_referer('audit-trail-get-users', 'nonce', false)) {
			wp_send_json_error(array('message' => '安全验证失败'));
			return;
		}
		
		if (!current_user_can('manage_options')) {
			wp_send_json_error(array('message' => '权限不足'));
			return;
		}
		
		$current_user_id = get_current_user_id();
		
		// 获取所有用户，排除当前用户
		$users = get_users(array(
			'exclude' => array($current_user_id),
			'fields' => array('ID', 'user_login', 'display_name')
		));
		
		wp_send_json_success($users);
	}

	function at_view() {
		if ( check_ajax_referer( 'audittrail_view' ) ) {
			$id = intval( $_POST['id'] );

			$item = AT_Audit::get( $id );
			$this->render( 'trail_details', array( 'item' => $item ) );

			die();
		}
	}

	function at_close( $item ) {
		if ( check_ajax_referer( 'audittrail_view' ) ) {
			$id = intval( $_POST['id'] );

			$item = AT_Audit::get ($id);
			$this->render ('trail_item', array ('item' => $item));

			die();
		}
	}

	/**
	 * 处理CSV导出AJAX请求
	 * 这个函数作为中介，将请求重定向到csv.php，以避免NGINX拦截
	 */
	function export_csv() {
		// 验证用户权限
		if (!current_user_can('publish_posts') && !current_user_can('audit_trail') && !current_user_can('edit_plugins')) {
			wp_die(__('您没有足够的权限导出数据', 'audit-trail'), __('权限错误', 'audit-trail'), array('response' => 403));
			return;
		}
		
		// 包含CSV导出文件
		include_once(dirname(__FILE__) . '/csv.php');
		exit;
	}

	private function render( $template, $template_vars = array() ) {
		foreach ( $template_vars AS $key => $val ) {
			$$key = $val;
		}

		if ( file_exists( dirname( __FILE__ )."/view/admin/$template.php" ) )
			include dirname( __FILE__ )."/view/admin/$template.php";
	}
}
