# 更新日志

!!!! 使用 sudo nginx -t 检验配置

本文件记录nginx配置的所有修改和更新。

## [未发布]

### 新增
- 创建README.md文件，全面总结nginx配置功能 ✅
- 创建CHANGELOG.md文件用于记录修改信息 ✅
- 标记已完成功能（在README.md中添加勾选标记）✅
- 添加敏感文件访问限制功能 ✅
  - 创建`rules/sensitive_files.conf`文件，限制访问敏感文件类型 ✅
  - 更新站点配置模板，包含敏感文件访问限制 ✅
  - 在README.md中添加相关功能说明 ✅
- 加强根路径安全配置 ✅
  - 创建`rules/root_path_security.conf`文件，添加limit_except和访问控制配置 ✅
  - 从wordpress_common.conf中移除根路径配置，改为使用新的安全规则 ✅
  - 更新站点配置，使用明确的IP绑定而非默认的0.0.0.0 ✅
  - 在README.md中添加相关功能说明 ✅
- 增强访问日志配置 ✅
  - 在nginx.conf中添加标准日志格式配置，包含所需字段（IP、用户、时间、请求、状态码等）✅
  - 确保日志配置符合安全检查要求 ✅
  - 在README.md中添加日志配置部分 ✅
- 增强SSL/TLS安全配置 ✅
  - 更新nginx.conf中的ssl_ciphers配置为更安全的设置 ✅
  - 添加ssl_session_timeout和ssl_session_cache设置 ✅
  - 创建snippets/ssl-params.conf文件，集中管理SSL/TLS参数 ✅
  - 创建wordpress_ssl_template.conf，支持HTTPS配置 ✅
  - 在README.md中添加SSL/TLS配置指南 ✅
- 模块化规则文件，进一步简化站点配置 ✅
  - 创建`rules/http_server.conf`文件，基本HTTP服务器配置 ✅
  - 创建`rules/https_server.conf`文件，基本HTTPS服务器配置 ✅
  - 创建`rules/https_redirect.conf`文件，HTTP到HTTPS重定向规则 ✅
  - 创建`rules/php_handling.conf`文件，PHP处理规则 ✅
  - 创建`rules/security_headers.conf`文件，安全响应头配置 ✅
  - 创建`rules/static_files_cache.conf`文件，静态文件缓存规则 ✅
  - 简化`sites-enabled/demo.conf`，使用新的模块化规则文件 ✅
- 增强请求限流功能 ✅
  - 在nginx.conf中配置general限流区域（每秒5次请求） ✅
  - 创建`rules/rate_limiting.conf`文件，配置限流规则并排除静态资源 ✅
  - 添加自定义429错误页面，提供友好的用户体验 ✅
  - 更新站点配置，引入限流规则 ✅
- 增强WordPress REST API安全 ✅
  - 在`wordpress_security.conf`中添加REST API保护规则 ✅
  - 阻止未授权访问`/wp-json/wp/v2/users`路径，防止用户信息泄露 ✅
  - 拦截通过`rest_route`参数访问用户信息的请求 ✅
- 增强SQL注入防护 ✅
  - 创建`rules/sql_injection_protection.conf`文件，全面防护SQL注入攻击 ✅
  - 配置防护规则拦截各种恶意请求模式 ✅
  - 限制请求方法、阻止恶意爬虫和特定工具 ✅
  - 过滤包含SQL关键字和特殊字符的请求 ✅
  - 在站点配置文件中优先引入SQL注入防护规则 ✅
- 站点配置文件更新 ✅
  - 将`sites-enabled/lp_yuchai.conf`改名为`sites-enabled/demo.conf` ✅
  - 更新站点配置中的网站根目录路径和日志文件路径 ✅
- 规则文件优化与合并-第一阶段 ✅
  - 创建`rules/basic_security.conf`，合并HTTP配置、安全头和敏感文件访问限制 ✅
  - 创建`rules/wordpress_advanced_security.conf`，合并WordPress安全、路径遍历和根路径配置 ✅
  - 创建`rules/performance_optimization.conf`，合并PHP处理和静态文件缓存规则 ✅
  - 简化`sites-enabled/demo.conf`，使用合并后的规则文件，减少include数量 ✅
- 规则文件优化与合并-第二阶段 ✅
  - 创建`rules/security.conf`，综合合并SQL注入防护、基础安全规则和限流配置 ✅
  - 创建`rules/wordpress.conf`，综合合并WordPress安全规则、PHP处理和多站点支持 ✅
  - 创建`rules/https.conf`，综合合并HTTPS服务器配置、HTTP到HTTPS重定向和SSL参数 ✅
  - 进一步简化`sites-enabled/demo.conf`，仅使用两个主要规则文件 ✅
  - 大幅减少规则文件数量，同时保持功能一致性 ✅
- 从其他nginx配置合并功能 ✅
  - 创建`readip.conf`文件，添加真实客户端IP识别功能 ✅
  - 增强`nginx.conf`，添加工作进程优化、epoll使用和性能设置 ✅
  - 添加更多的请求限流区域，包括搜索请求的专用限流 ✅
  - 优化FastCGI参数，提高超时和缓冲区设置 ✅
  - 增强WordPress配置，添加WebP图片支持和搜索限流功能 ✅
  - 自定义MIME类型，添加字体文件支持 ✅
- 添加了`readip.conf`文件，用于从代理服务器后识别真实客户端IP
- 优化了Nginx的性能设置，包括工作进程、连接处理、TCP设置等
- 修复了FastCGI缓冲区配置，确保`fastcgi_busy_buffers_size`与`fastcgi_buffers`设置兼容
- 添加了WebP Express支持，实现WebP图像的动态转换和按需生成
- 添加了对特定WordPress插件的支持（LiteSpeed缓存、Hero Menu、Tool-Hub等）
- 合并了静态资源和媒体文件处理规则，优化访问控制
- 添加了防止恶意刷流量的规则，仅允许白名单IP下载大文件（zip、pdf、sql等）
- 创建了独立的`ip_whitelist.conf`配置文件，集中管理白名单IP，这些IP不受任何限制
- 移除了HTTP基本认证功能，简化WordPress管理区域的访问
- 优化了WordPress管理区域的访问控制，合并`wp-admin-access.conf`和`wp-admin.conf`到`wordpress.conf`
- 添加了基于主机名的条件认证功能，允许特定网站免除认证
- 加强了REST API的保护，同时允许特定插件API访问
- 删除冗余配置文件，包括：
  - `wp-admin-access.conf` 和 `wp-admin.conf`（已合并至 `wordpress.conf`）
  - `wordpress_security.conf` 和 `wordpress_advanced_security.conf`（已合并至 `wordpress.conf`）
  - `basic_security.conf`（已合并至 `security.conf`）
  - `wordpress_common.conf`（已合并至 `nginx.conf`，全局生效）
  - `static_files.conf` 和 `static_files_cache.conf`（已合并至 `wordpress.conf`）
  - `performance_optimization.conf`（已合并至 `wordpress.conf`）
  - `sql_injection_protection.conf`（已合并至 `security.conf`）
  - `sensitive_files.conf`、`security_headers.conf`、`path_traversal.conf`和`file_access.conf`（已合并至 `security.conf`）
  - `root_path_security.conf`（已删除，因功能已在wordpress.conf中实现）
  - `http_server.conf`、`https_server.conf`和`https_redirect.conf`（内容已在https.conf和nginx.conf中实现）
  - `snippets/ssl-params.conf`（已合并至 `https.conf`）
- 创建了`rules/php_combined.conf`文件，合并`php_handling.conf`和`php_security.conf`的功能
  - 整合了PHP处理规则和安全设置
  - 加入了PHP路径解析漏洞防护
  - 优化了FastCGI参数配置
  - 合并了所有相关的安全响应头
  - 统一了缓冲区设置
- 优化`rules/rate_limiting.conf`，集中管理所有限流规则
  - 从`wordpress.conf`和`security.conf`中移出所有限流规则
  - 将限流规则按功能分组：通用规则、静态资源规则、WordPress规则和下载规则
  - 改进限流超出响应页面
  - 在其他配置文件中添加对`rate_limiting.conf`的引用
  - 优化了搜索请求、WordPress登录页面和静态资源的限流配置
  - 解决了重复指令问题，确保配置语法正确
  - 将实际限流指令转换为注释形式的最佳实践建议
  - 添加了详细的使用说明和注意事项
- 优化统一IP白名单管理系统
  - 创建单一的 `ip_list.conf` 文件替代多个白名单定义
  - 简化 `whitelists.conf` 配置，从单一文件导入所有IP
  - 所有IP地址现在集中在一个地方管理，无需多处修改
  - 提供简单明了的格式，加快IP添加和删除效率
  - 通过include指令实现IP列表在各白名单中的复用

### 变更
- 修复白名单配置不生效的问题
  - 更改真实IP识别使用 X-Real-IP 替代 X-Forwarded-For
  - 添加 real_ip_recursive on 设置以正确处理IP
  - 在WordPress管理后台相关location块中初始化 $auth_basic_enabled 变量
  - 临时添加 0.0.0.0/0 到IP白名单以便于测试（注意：生产环境应移除）

- 优化白名单配置管理
  - 移除独立的 `ip_whitelist.conf` 文件
  - 统一使用 `rules/whitelists.conf` 进行白名单管理
  - 所有白名单IP统一从 `ip_list.conf` 读取

## [1.0.0] - 2023-08-01

### 初始版本
- 基础nginx.conf配置 ✅
- WordPress模板配置 ✅
- 站点配置示例 ✅
- 模块化规则文件结构 ✅
  - wordpress_common.conf ✅
  - php_security.conf ✅
  - wordpress_security.conf ✅
  - static_files.conf ✅
  - file_access.conf ✅
  - wp-admin.conf ✅
  - path_traversal.conf ✅
  - wp-admin-access.conf ✅

### 新增功能
- 新增 `README.md` 文件，汇总Nginx配置文件功能说明
- 新增敏感文件访问限制配置
- 新增根路径安全配置
- 新增防SQL注入防护
- 新增访问日志增强配置
- 新增SSL/TLS安全配置
- 规则文件模块化，简化站点配置
- 站点配置文件重写和优化
- 新增限流规则配置
- 安全规则合并与优化，规则数量从100+减少到40+
- 添加Real IP识别配置文件 `readip.conf`
- 优化WordPress站点配置：新增搜索限流和WebP支持
- 新增主机名条件验证功能
- 增强REST API保护，同时允许特定插件API访问
- 删除冗余配置文件，包括：
  - `wp-admin-access.conf` 和 `wp-admin.conf`（已合并至 `wordpress.conf`）
  - `wordpress_security.conf` 和 `wordpress_advanced_security.conf`（已合并至 `wordpress.conf`）
  - `basic_security.conf`（已合并至 `security.conf`）
  - `wordpress_common.conf`（已合并至 `nginx.conf`，全局生效）
  - `static_files.conf` 和 `static_files_cache.conf`（已合并至 `wordpress.conf`）
  - `performance_optimization.conf`（已合并至 `wordpress.conf`）
  - `sql_injection_protection.conf`（已合并至 `security.conf`）
  - `sensitive_files.conf`、`security_headers.conf`、`path_traversal.conf`和`file_access.conf`（已合并至 `security.conf`）
  - `root_path_security.conf`（已删除，因功能已在wordpress.conf中实现）
  - `http_server.conf`、`https_server.conf`和`https_redirect.conf`（内容已在https.conf和nginx.conf中实现）
  - `snippets/ssl-params.conf`（已合并至 `https.conf`）
- 为Certbot自动SSL配置优化https.conf：
  - 移除手动SSL证书配置部分
  - 保留必要的TLS安全参数
  - 清理冗余配置
- 优化wordpress_ssl_template.conf模板：
  - 添加certbot验证路径配置
  - 移除冗余安全配置，改为引用规则文件
  - 简化监听地址配置
  - 添加SSL证书注释，表明由certbot自动配置 
- 合并PHP处理规则：
  - 创建`rules/php_combined.conf`文件，合并`php_handling.conf`和`php_security.conf`
  - 优化PHP处理配置，增强安全性和性能
  - 添加防止PHP路径解析漏洞的保护
- 优化限流规则管理：
  - 将所有限流规则集中到`rate_limiting.conf`
  - 按功能类别整理限流规则
  - 简化其他配置文件，删除重复的限流代码
  - 解决指令重复和语法冲突问题
  - 添加详细的限流最佳实践和配置建议
- 限流规则激活，防止暴力攻击
  - 针对WordPress登录页面的严格限流（每秒2个请求，突发1个）
  - 搜索功能限流（每秒1个请求，突发1个）
  - WordPress内容目录保护（每秒5个请求，突发5个）
  - 静态资源宽松限流（大突发值1000）
  - 添加友好的限流错误响应页面
- 统一IP白名单管理系统
  - 创建集中式whitelists.conf配置文件
  - 建立多种用途的白名单：内部网络、管理员、下载、API访问和限流豁免
  - 将所有硬编码IP替换为统一管理的白名单变量
  - 提高安全性和可维护性
- 基于主机名的条件认证
- 增强REST API保护，同时允许特定插件API访问
- 限流规则激活，防止暴力攻击
  - 针对WordPress登录页面的严格限流（每秒2个请求，突发1个）
  - 搜索功能限流（每秒1个请求，突发1个）
  - WordPress内容目录保护（每秒5个请求，突发5个）
  - 静态资源宽松限流（大突发值1000）
  - 添加友好的限流错误响应页面
- 删除不必要的规则文件:
  - 将`wp-admin-access.conf`和`wp-admin.conf`合并到`wordpress.conf`
  - 将`sql_injection.conf`、`path_traversal.conf`等合并到`security.conf`
  - 将`root_path_security.conf`合并到`security.conf`
  - 将`http_server.conf`、`https_server.conf`和`https_redirect.conf`整合到`https.conf`和`nginx.conf`
  - 将`ssl-params.conf`合并到`https.conf`
- 创建`readip.conf`文件，实现真实客户端IP识别
- Nginx配置性能优化

### 改进

- 站点配置文件更新
- 优化现有规则
- 减少规则数量，同时保持功能
- 规则文件更加模块化
- 代码注释更加清晰
- 更易于维护的结构

### 修复

- 修复HTTP重定向问题
- 修复WordPress REST API访问问题
- 修复静态资源缓存问题

### Bug修复
- 修复WP插件部分API无法访问的问题
- 修复Nginx配置语法错误
- 修复错误的路径引用
- 修复高CPU使用率问题
- 修复部分规则冲突问题
- 修复限流规则中的重复指令问题，确保配置文件语法正确 