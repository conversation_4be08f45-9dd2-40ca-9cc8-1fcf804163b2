# Nginx 负载均衡配置示例
# 这个配置展示如何把请求分配给多个后端服务器

# 定义后端服务器组 - 就像餐厅的多个厨房
upstream backend_servers {
    # 服务器1 - 权重为3（处理更多请求）
    server ************:8080 weight=3;
    
    # 服务器2 - 权重为2
    server ************:8080 weight=2;
    
    # 服务器3 - 权重为1，备用服务器
    server ************:8080 weight=1 backup;
    
    # 负载均衡方法：
    # - round_robin（默认）：轮流分配
    # - least_conn：分配给连接数最少的服务器
    # - ip_hash：根据客户端IP分配到固定服务器
    
    # 健康检查设置
    # max_fails=3：最多失败3次
    # fail_timeout=30s：失败后30秒内不再尝试
    server ************:8080 max_fails=3 fail_timeout=30s;
}

# Web服务器配置
server {
    listen 80;
    server_name myapp.com;
    
    # 把所有请求转发给后端服务器组
    location / {
        # 代理到后端服务器
        proxy_pass http://backend_servers;
        
        # 传递客户端真实IP
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # 连接超时设置
        proxy_connect_timeout 30s;
        proxy_send_timeout 30s;
        proxy_read_timeout 30s;
    }
    
    # 静态文件直接由 Nginx 处理（不转发给后端）
    location /static/ {
        root /var/www;
        expires 1y;
    }
    
    # API 请求的特殊处理
    location /api/ {
        proxy_pass http://backend_servers;
        
        # API 专用设置
        proxy_set_header Content-Type application/json;
        proxy_buffering off;  # 关闭缓冲，实时传输
    }
    
    # 健康检查页面
    location /health {
        access_log off;
        return 200 "healthy\n";
        add_header Content-Type text/plain;
    }
}

# 管理后台配置（只允许内网访问）
server {
    listen 8080;
    server_name admin.myapp.com;
    
    # 只允许内网IP访问
    allow ***********/24;
    allow 10.0.0.0/8;
    deny all;
    
    location / {
        proxy_pass http://backend_servers;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
}

# 不同路径分配给不同服务器组的示例
upstream web_servers {
    server ************:80;
    server ************:80;
}

upstream api_servers {
    server ************:3000;
    server ************:3000;
}

server {
    listen 80;
    server_name complex.example.com;
    
    # 网站页面请求
    location / {
        proxy_pass http://web_servers;
    }
    
    # API 请求
    location /api/ {
        proxy_pass http://api_servers;
    }
    
    # 文件上传（分配给专门的服务器）
    location /upload/ {
        proxy_pass http://************:8000;
        client_max_body_size 100M;  # 允许上传100MB文件
    }
}
