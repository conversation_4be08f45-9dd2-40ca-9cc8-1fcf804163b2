<?php if (!defined ('ABSPATH')) die (); ?><div class="wrap">
	<?php screen_icon(); ?>
	<h2><?php _e ('Audit Options', 'audit-trail'); ?></h2>

	<?php $this->submenu (true); ?>

	<?php if (isset($saved) && $saved): ?>
	<div class="notice notice-success">
		<p><?php _e('选项已保存。', 'audit-trail'); ?></p>
	</div>
	<?php endif; ?>

	<form action="" method="post" accept-charset="utf-8" style="clear: both" class="audit-options-form">
		<?php wp_nonce_field ('audittrail-update_options'); ?>

		<div class="audit-options-section">
			<h3><?php _e('Actions to monitor', 'audit-trail')?></h3>

			<?php if (count ($methods) > 0) : ?>
				<ul class="options">
					<?php foreach ($methods AS $key => $name) : ?>
					<li><label><input type="checkbox" name="methods[]" value="<?php echo $key ?>"<?php if (in_array ($key, $current)) echo ' checked="checked"' ?>/>
						 <?php echo esc_html( $name ) ?></label>
					</li>
					<?php endforeach; ?>
				</ul>

			<?php else : ?>
			<p><?php _e ('There are no actions to monitor', 'audit-trail'); ?></p>
			<?php endif; ?>
		</div>

		<div class="audit-options-section">
			<h3><?php _e( 'Other Options', 'audit-trail' ); ?></h3>

			<table class="form-table">
				<tr>
					<th><?php _e ('Auto-expire', 'audit-trail'); ?></th>
					<td>
						<div class="auto-cleanup-settings">
							<label class="checkbox-label">
								<input type="checkbox" name="auto_cleanup_enabled" <?php checked(get_option('audit_auto_cleanup_enabled', true)); ?> />
								<?php _e('启用自动清理', 'audit-trail'); ?>
							</label>
							<input size="5" type="text" name="expiry" value="<?php echo esc_attr( $expiry ) ?>" id="expire"/> 
							<?php _e ('days (0 for no expiry)', 'audit-trail'); ?>
						</div>
					</td>
				</tr>
				<tr>
					<th><?php _e ('Ignore users', 'audit-trail');?></th>
					<td><input type="text" name="ignore_users" value="<?php echo esc_attr( $ignore_users ) ?>"/> <span class="description"><?php _e( 'separate user IDs with a comma, use 0 to ignore everyone', 'audit-trail'); ?></span></td>
				</tr>
				<tr>
					<th><?php _e ('禁止用户', 'audit-trail');?></th>
					<td>
						<input type="text" name="forbidden_users" id="forbidden_users" value="<?php echo esc_attr( $forbidden_users ) ?>"/> 
						<button type="button" id="refresh_users" class="button"><?php _e('Select All Users', 'audit-trail'); ?></button>
						<p class="description"><?php _e( 'users who cannot use this plugin, separate IDs with a comma', 'audit-trail'); ?></p>
						<div id="user_selection" style="display:none;">
							<select id="available_users" multiple></select>
							<button type="button" id="add_selected_users" class="button"><?php _e('Add Selected Users', 'audit-trail'); ?></button>
						</div>
						<script>
						jQuery(document).ready(function($) {
							$('#refresh_users').click(function() {
								var button = $(this);
								button.prop('disabled', true);
								button.text('<?php _e('Loading...', 'audit-trail'); ?>');
								
								$.ajax({
									url: ajaxurl,
									type: 'POST',
									dataType: 'json',
									data: {
										action: 'at_get_users',
										nonce: '<?php echo wp_create_nonce('audit-trail-get-users'); ?>'
									},
									success: function(response) {
										if (response.success) {
											$('#available_users').empty();
											$.each(response.data, function(index, user) {
												$('#available_users').append('<option value="' + user.ID + '">' + user.user_login + ' (' + user.display_name + ')</option>');
											});
											$('#user_selection').show();
										} else {
											alert('<?php _e('Error loading users', 'audit-trail'); ?>');
										}
										button.prop('disabled', false);
										button.text('<?php _e('Select All Users', 'audit-trail'); ?>');
									},
									error: function() {
										alert('<?php _e('Error loading users', 'audit-trail'); ?>');
										button.prop('disabled', false);
										button.text('<?php _e('Select All Users', 'audit-trail'); ?>');
									}
								});
							});
							
							$('#add_selected_users').click(function() {
								var selectedUsers = $('#available_users').val();
								if (selectedUsers) {
									var currentUsers = $('#forbidden_users').val();
									var userArray = currentUsers ? currentUsers.split(',') : [];
									
									// Add new selected users
									$.each(selectedUsers, function(index, userId) {
										if ($.inArray(userId, userArray) === -1) {
											userArray.push(userId);
										}
									});
									
									// Update the input field
									$('#forbidden_users').val(userArray.join(','));
								}
							});
						});
						</script>
					</td>
				</tr>
				<tr>
					<th><?php _e('高流量站点优化', 'audit-trail'); ?></th>
					<td>
						<div class="high-volume-settings">
							<label class="checkbox-label">
								<input type="checkbox" name="high_volume_mode" id="high_volume_mode" <?php checked(get_option('audit_high_volume_mode', false)); ?> />
								<?php _e('启用高流量模式', 'audit-trail'); ?>
							</label>
							<p class="description"><?php _e('为高流量网站优化日志记录，减少数据库压力', 'audit-trail'); ?></p>
							
							<div id="high_volume_options" <?php echo get_option('audit_high_volume_mode', false) ? '' : 'style="display:none;"'; ?>>
								<div class="sample-rate-setting">
									<label for="log_sample_rate"><?php _e('页面访问记录采样率：', 'audit-trail'); ?></label>
									<input type="range" name="log_sample_rate" id="log_sample_rate" min="1" max="100" step="1" value="<?php echo esc_attr(get_option('audit_log_sample_rate', 100)); ?>" />
									<span id="sample_rate_display"><?php echo esc_html(get_option('audit_log_sample_rate', 100)); ?>%</span>
									<p class="description"><?php _e('设置记录页面访问的百分比。只影响页面访问记录，其他关键操作始终100%记录。', 'audit-trail'); ?></p>
								</div>
								
								<div class="exclude-paths-setting">
									<label for="exclude_paths"><?php _e('排除路径：', 'audit-trail'); ?></label>
									<textarea name="exclude_paths" id="exclude_paths" rows="3" cols="50"><?php echo esc_textarea(get_option('audit_exclude_paths', 'wp-content/uploads,wp-includes/js,wp-includes/css')); ?></textarea>
									<p class="description"><?php _e('不记录包含这些路径的页面访问，用逗号分隔多个路径', 'audit-trail'); ?></p>
								</div>
							</div>
							
							<script>
							jQuery(document).ready(function($) {
								// 更新采样率显示
								$('#log_sample_rate').on('input change', function() {
									$('#sample_rate_display').text($(this).val() + '%');
								});
								
								// 显示/隐藏高流量选项
								$('#high_volume_mode').change(function() {
									if ($(this).is(':checked')) {
										$('#high_volume_options').slideDown();
									} else {
										$('#high_volume_options').slideUp();
									}
								});
							});
							</script>
						</div>
					</td>
				</tr>
				<tr>
					<th><?php _e('登录失败记录日志', 'audit-trail'); ?></th>
					<td>
						<label class="checkbox-label">
							<input type="checkbox" name="error_log"<?php if ($error_log) echo ' checked="checked"' ?>/>
							<?php _e('调用 <code>error_log( \'WordPress Login Failure: &lt;name&gt; from &lt;ip&gt;\' )</code> 如果启用了"用户配置文件&登录"监控，并且登录失败', 'audit-trail'); ?>
						</label>
						<p class="description"><?php _e('实验性功能：可与<a href="http://www.fail2ban.org/">fail2ban</a>结合使用来封禁IP地址', 'audit-trail'); ?></p>
					</td>
				</tr>
			</table>
		</div>

		<div class="audit-options-section">
			<h3><?php _e('系统状态', 'audit-trail'); ?></h3>
			
			<table class="form-table">
				<tr>
					<th><?php _e('日志统计', 'audit-trail'); ?></th>
					<td>
						<?php 
						global $wpdb;
						$total_logs = $wpdb->get_var("SELECT COUNT(*) FROM {$wpdb->prefix}audit_trail");
						$page_views = $wpdb->get_var("SELECT COUNT(*) FROM {$wpdb->prefix}audit_trail WHERE operation = 'template_redirect'");
						$first_log = $wpdb->get_var("SELECT happened_at FROM {$wpdb->prefix}audit_trail ORDER BY happened_at ASC LIMIT 1");
						$last_cleanup = get_option('audit_last_cleanup', '');
						?>
						<p><?php echo sprintf(__('总共记录了 <strong>%s</strong> 条日志', 'audit-trail'), number_format($total_logs)); ?></p>
						<p><?php echo sprintf(__('其中包含 <strong>%s</strong> 条页面访问记录', 'audit-trail'), number_format($page_views)); ?></p>
						<?php if ($first_log): ?>
						<p><?php echo sprintf(__('最早的日志记录于：%s', 'audit-trail'), date_i18n(get_option('date_format') . ' ' . get_option('time_format'), strtotime($first_log))); ?></p>
						<?php endif; ?>
						<?php if ($last_cleanup): ?>
						<p><?php echo sprintf(__('上次自动清理时间：%s', 'audit-trail'), date_i18n(get_option('date_format') . ' ' . get_option('time_format'), strtotime($last_cleanup))); ?></p>
						<?php endif; ?>
					</td>
				</tr>
				<tr>
					<th><?php _e('手动清理', 'audit-trail'); ?></th>
					<td>
						<div class="manual-cleanup">
							<input size="5" type="text" name="manual_cleanup_days" value="30" id="manual_cleanup_days"/> 
							<?php _e('天前的日志', 'audit-trail'); ?>
							<input type="submit" name="manual_cleanup" class="button" value="<?php esc_attr_e('立即清理', 'audit-trail'); ?>" onclick="return confirm('<?php esc_attr_e('确定要删除指定天数前的所有日志记录吗？此操作不可恢复！', 'audit-trail'); ?>');" />
						</div>
					</td>
				</tr>
				<tr>
					<th><?php _e('按操作类型批量删除', 'audit-trail'); ?></th>
					<td>
						<div class="operation-cleanup">
							<select name="operation_type" id="operation_type">
								<option value=""><?php _e('- 选择操作类型 -', 'audit-trail'); ?></option>
								<?php
								// 获取所有操作类型
								$operations = array(
									'switch_theme'         => __('切换主题', 'audit-trail'),
									'wp_login'             => __('用户登录', 'audit-trail'),
									'wp_logout'            => __('用户退出', 'audit-trail'),
									'wp_login_failed'      => __('登录失败', 'audit-trail'),
									'retrieve_password'    => __('找回密码', 'audit-trail'),
									'delete_user'          => __('删除用户', 'audit-trail'),
									'delete_link'          => __('删除链接', 'audit-trail'),
									'delete_comment'       => __('删除评论', 'audit-trail'),
									'delete_post'          => __('删除文章', 'audit-trail'),
									'private_to_published' => __('私密改为发布', 'audit-trail'),
									'delete_category'      => __('删除分类', 'audit-trail'),
									'delete_attachment'    => __('删除附件', 'audit-trail'),
									'template_redirect'    => __('访问页面', 'audit-trail'),
									'activate_plugin'      => __('激活插件', 'audit-trail'),
									'deactivate_plugin'    => __('停用插件', 'audit-trail'),
									'draft_to_publish'     => __('草稿改为发布', 'audit-trail'),
									'pending_to_publish'   => __('待审改为发布', 'audit-trail'),
									'publish_to_draft'     => __('发布改为草稿', 'audit-trail'),
									'publish_to_private'   => __('发布改为私密', 'audit-trail'),
									'publish_to_pending'   => __('发布改为待审', 'audit-trail'),
									'publish_to_trash'     => __('发布改为回收站', 'audit-trail'),
									'draft_to_pending'     => __('草稿改为待审', 'audit-trail'),
									'draft_to_trash'       => __('草稿改为回收站', 'audit-trail'),
									'pending_to_draft'     => __('待审改为草稿', 'audit-trail'),
									'pending_to_trash'     => __('待审改为回收站', 'audit-trail'),
									'trash_to_publish'     => __('回收站改为发布', 'audit-trail'),
									'trash_to_draft'       => __('回收站改为草稿', 'audit-trail'),
									'trash_to_pending'     => __('回收站改为待审', 'audit-trail'),
									'future_to_publish'    => __('定时发布', 'audit-trail'),
									'profile_update'       => __('更新个人资料', 'audit-trail'),
									'user_register'        => __('注册新用户', 'audit-trail'),
									'add_link'             => __('添加链接', 'audit-trail'),
									'edit_link'            => __('编辑链接', 'audit-trail'),
									'edit_category'        => __('编辑分类', 'audit-trail'),
									'add_category'         => __('添加分类', 'audit-trail'),
									'edit_comment'         => __('编辑评论', 'audit-trail'),
									'save_post'            => __('保存文章/页面', 'audit-trail'),
									'add_attachment'       => __('添加附件', 'audit-trail'),
									'edit_attachment'      => __('编辑附件', 'audit-trail'),
									'bulk_delete_by_operation' => __('批量删除操作', 'audit-trail'),
									'manual_cleanup'       => __('手动清理', 'audit-trail'),
									// Elementor操作
									'elementor/editor/after_save'     => __('Elementor编辑器保存', 'audit-trail'),
									'elementor/document/after_save'   => __('Elementor文档保存', 'audit-trail'),
									'elementor/editor/before_save'    => __('Elementor编辑器保存前', 'audit-trail'),
									// Beaver Builder操作
									'fl_builder_after_save_layout'    => __('Beaver Builder保存', 'audit-trail'),
									'fl_builder_before_save_layout'   => __('Beaver Builder保存前', 'audit-trail'),
									'fl_builder_after_layout_rendered' => __('Beaver Builder渲染后', 'audit-trail'),
									// SiteOrigin操作
									'siteorigin_panels_save_post'     => __('SiteOrigin保存', 'audit-trail'),
									'siteorigin_panels_after_render'  => __('SiteOrigin渲染后', 'audit-trail'),
									// WPBakery操作
									'vc_after_save_post'              => __('WPBakery保存', 'audit-trail'),
									'vc_before_save_post'             => __('WPBakery保存前', 'audit-trail'),
									'vc_after_update'                 => __('WPBakery更新后', 'audit-trail'),
									// Divi操作
									'et_fb_save_layout'               => __('Divi Builder保存', 'audit-trail'),
									'et_fb_ajax_save'                 => __('Divi Builder AJAX保存', 'audit-trail'),
									'et_builder_after_save_layout'    => __('Divi Builder布局保存后', 'audit-trail'),
									// Gutenberg操作
									'blocks_parsed'                   => __('Gutenberg编辑', 'audit-trail'),
									'gutenberg_render_block'          => __('Gutenberg块渲染', 'audit-trail'),
								);
								
								// 动态获取数据库中存在的操作类型
								$existing_operations = $wpdb->get_col("SELECT DISTINCT operation FROM {$wpdb->prefix}audit_trail");
								if ($existing_operations) {
									foreach ($existing_operations as $op) {
										if (!isset($operations[$op]) && !empty($op)) {
											$operations[$op] = $op;
										}
									}
								}
								
								// 按照显示名称排序
								asort($operations);
								
								foreach ($operations as $value => $label) {
									echo '<option value="'.esc_attr($value).'">'.esc_html($label).'</option>';
								}
								?>
							</select>
							<input type="submit" name="delete_by_operation" class="button" value="<?php esc_attr_e('删除所选类型', 'audit-trail'); ?>" onclick="return confirm('<?php esc_attr_e('确定要删除所选操作类型的所有日志记录吗？此操作不可恢复！', 'audit-trail'); ?>');" />
							<p class="description"><?php _e('删除特定操作类型的所有日志记录。常用于清理大量的页面访问日志或登录失败记录。', 'audit-trail'); ?></p>
						</div>
					</td>
				</tr>
			</table>
		</div>

		<p><input class="button-primary" type="submit" name="save" value="<?php esc_attr_e( 'Save Options', 'audit-trail' ); ?>"/></p>
	</form>
</div>

<style>
.audit-options-section {
	background: #fff;
	border: 1px solid #e5e5e5;
	padding: 10px 20px;
	margin-bottom: 20px;
	border-radius: 3px;
}
.audit-options-section h3 {
	margin-top: 5px;
	border-bottom: 1px solid #eee;
	padding-bottom: 10px;
}
.auto-cleanup-settings,
.high-volume-settings,
.manual-cleanup,
.operation-cleanup {
	margin: 10px 0;
}
#high_volume_options {
	background: #f9f9f9;
	padding: 10px;
	margin-top: 10px;
	border: 1px solid #eee;
	border-radius: 3px;
}
.sample-rate-setting,
.exclude-paths-setting {
	margin: 10px 0;
}
#sample_rate_display {
	font-weight: bold;
	margin-left: 5px;
}
.operation-cleanup select {
	min-width: 200px;
	margin-right: 10px;
}
</style>
