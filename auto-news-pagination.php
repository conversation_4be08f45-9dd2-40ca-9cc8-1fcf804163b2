<?php
/**
 * 自动新闻分页系统
 * 自动获取已发布的新闻文章并进行分页显示
 */

// 防止直接访问
if (!defined('ABSPATH')) {
    exit;
}

class AutoNewsPagination {
    
    private $posts_per_page = 6;
    private $category_slug = 'news'; // 默认新闻分类
    
    public function __construct() {
        add_action('wp_enqueue_scripts', array($this, 'enqueue_scripts'));
        add_action('wp_ajax_load_news_page', array($this, 'ajax_load_news_page'));
        add_action('wp_ajax_nopriv_load_news_page', array($this, 'ajax_load_news_page'));
        add_shortcode('auto_news_pagination', array($this, 'render_news_pagination'));
    }
    
    /**
     * 加载必要的脚本和样式
     */
    public function enqueue_scripts() {
        wp_enqueue_script('jquery');
        wp_enqueue_script('auto-news-pagination', get_template_directory_uri() . '/js/auto-news-pagination.js', array('jquery'), '1.0.0', true);
        
        // 传递AJAX URL和nonce
        wp_localize_script('auto-news-pagination', 'autoNewsAjax', array(
            'ajaxurl' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('auto_news_nonce')
        ));
    }
    
    /**
     * 获取已发布的新闻文章
     */
    public function get_published_news($page = 1, $category = null, $posts_per_page = null) {
        if (!$posts_per_page) {
            $posts_per_page = $this->posts_per_page;
        }
        
        if (!$category) {
            $category = $this->category_slug;
        }
        
        $args = array(
            'post_type' => 'post',
            'post_status' => 'publish',
            'posts_per_page' => $posts_per_page,
            'paged' => $page,
            'meta_query' => array(
                'relation' => 'OR',
                array(
                    'key' => '_thumbnail_id',
                    'compare' => 'EXISTS'
                ),
                array(
                    'key' => '_thumbnail_id',
                    'compare' => 'NOT EXISTS'
                )
            )
        );
        
        // 如果指定了分类
        if ($category && $category !== 'all') {
            // 检查是否是分类ID还是slug
            if (is_numeric($category)) {
                $args['cat'] = intval($category);
            } else {
                $args['category_name'] = $category;
            }
        }
        
        // 按发布时间倒序排列
        $args['orderby'] = 'date';
        $args['order'] = 'DESC';
        
        return new WP_Query($args);
    }
    
    /**
     * 获取新闻总数
     */
    public function get_total_news_count($category = null) {
        if (!$category) {
            $category = $this->category_slug;
        }
        
        $args = array(
            'post_type' => 'post',
            'post_status' => 'publish',
            'posts_per_page' => -1,
            'fields' => 'ids'
        );
        
        if ($category && $category !== 'all') {
            if (is_numeric($category)) {
                $args['cat'] = intval($category);
            } else {
                $args['category_name'] = $category;
            }
        }
        
        $query = new WP_Query($args);
        return $query->found_posts;
    }
    
    /**
     * 渲染新闻分页短代码
     */
    public function render_news_pagination($atts) {
        $atts = shortcode_atts(array(
            'posts_per_page' => 6,
            'category' => 'news',
            'columns' => 3,
            'show_excerpt' => 'yes',
            'show_meta' => 'yes',
            'show_pagination' => 'yes'
        ), $atts);
        
        $current_page = (get_query_var('paged')) ? get_query_var('paged') : 1;
        $posts_per_page = intval($atts['posts_per_page']);
        $category = sanitize_text_field($atts['category']);
        
        // 获取新闻文章
        $query = $this->get_published_news($current_page, $category, $posts_per_page);
        $total_posts = $this->get_total_news_count($category);
        $total_pages = ceil($total_posts / $posts_per_page);
        
        ob_start();
        ?>
        <div class="auto-news-pagination-wrapper" data-category="<?php echo esc_attr($category); ?>" data-posts-per-page="<?php echo esc_attr($posts_per_page); ?>">
            
            <!-- 新闻网格容器 -->
            <div class="aobailei-news-blog custom-news-grid" data-columns="<?php echo esc_attr($atts['columns']); ?>">
                <div class="news-grid-container" id="news-content-container">
                    <?php
                    if ($query->have_posts()) {
                        while ($query->have_posts()) {
                            $query->the_post();
                            $this->render_news_item($atts);
                        }
                    } else {
                        echo '<p class="no-news-found">暂无新闻文章</p>';
                    }
                    ?>
                </div>
            </div>
            
            <?php if ($atts['show_pagination'] === 'yes' && $total_pages > 1): ?>
            <!-- 自定义分页 -->
            <div class="custom-pagination-wrapper" style="
                display: flex;
                align-items: center;
                justify-content: center;
                gap: 15px;
                margin: 40px 0;
                padding: 20px;
                background: #f8f9fa;
                border-radius: 8px;
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            ">
                <div class="pagination-info" style="
                    color: #666;
                    font-size: 14px;
                    margin-right: 20px;
                ">
                    总共 <span id="total-posts-count"><?php echo $total_posts; ?></span> 条记录
                </div>
                
                <div class="pagination-buttons" style="
                    display: flex;
                    align-items: center;
                    gap: 5px;
                ">
                    <?php echo $this->render_pagination_buttons($current_page, $total_pages); ?>
                </div>
                
                <div class="goto-page" style="
                    display: flex;
                    align-items: center;
                    gap: 8px;
                    margin-left: 20px;
                ">
                    <label style="color: #666; font-size: 14px;">前往</label>
                    <input type="number" min="1" max="<?php echo $total_pages; ?>" value="<?php echo $current_page; ?>" 
                           class="goto-input" style="
                               width: 50px;
                               height: 32px;
                               padding: 0 8px;
                               border: 1px solid #ddd;
                               border-radius: 4px;
                               text-align: center;
                               font-size: 14px;
                           ">
                    <label style="color: #666; font-size: 14px;">页</label>
                    <button class="goto-btn" style="
                        height: 32px;
                        padding: 0 12px;
                        background: #007cba;
                        color: white;
                        border: none;
                        border-radius: 4px;
                        font-size: 14px;
                        cursor: pointer;
                    ">确定</button>
                </div>
            </div>
            <?php endif; ?>
            
            <!-- 加载指示器 -->
            <div class="loading-indicator" style="display: none; text-align: center; padding: 20px;">
                <span>正在加载...</span>
            </div>
        </div>
        
        <!-- 分页数据 -->
        <script>
        window.autoNewsPaginationData = {
            totalPosts: <?php echo $total_posts; ?>,
            postsPerPage: <?php echo $posts_per_page; ?>,
            currentPage: <?php echo $current_page; ?>,
            totalPages: <?php echo $total_pages; ?>,
            category: '<?php echo esc_js($category); ?>'
        };
        </script>
        <?php
        
        wp_reset_postdata();
        return ob_get_clean();
    }
    
    /**
     * 渲染单个新闻项目
     */
    private function render_news_item($atts) {
        $post_id = get_the_ID();
        $thumbnail = get_the_post_thumbnail($post_id, 'medium');
        $excerpt = get_the_excerpt();
        $date = get_the_date();
        $author = get_the_author();
        ?>
        <div class="news-item">
            <?php if ($thumbnail): ?>
            <div class="news-thumbnail">
                <a href="<?php the_permalink(); ?>">
                    <?php echo $thumbnail; ?>
                </a>
            </div>
            <?php endif; ?>
            
            <div class="news-content">
                <h3 class="news-title">
                    <a href="<?php the_permalink(); ?>"><?php the_title(); ?></a>
                </h3>
                
                <?php if ($atts['show_meta'] === 'yes'): ?>
                <div class="news-meta">
                    <span class="news-date"><?php echo $date; ?></span>
                    <span class="news-author">作者: <?php echo $author; ?></span>
                </div>
                <?php endif; ?>
                
                <?php if ($atts['show_excerpt'] === 'yes' && $excerpt): ?>
                <div class="news-excerpt">
                    <?php echo wp_trim_words($excerpt, 20, '...'); ?>
                </div>
                <?php endif; ?>
                
                <div class="news-read-more">
                    <a href="<?php the_permalink(); ?>" class="read-more-btn">阅读更多</a>
                </div>
            </div>
        </div>
        <?php
    }
    
    /**
     * 渲染分页按钮
     */
    private function render_pagination_buttons($current_page, $total_pages) {
        $html = '';
        
        // 上一页按钮
        $prev_disabled = $current_page <= 1;
        $prev_page = max(1, $current_page - 1);
        $html .= sprintf(
            '<a href="#" data-page="%d" class="pagination-btn prev-btn %s" style="%s">‹</a>',
            $prev_page,
            $prev_disabled ? 'disabled' : '',
            $this->get_button_style($prev_disabled, false)
        );
        
        // 页码按钮
        $page_numbers = $this->get_visible_page_numbers($current_page, $total_pages);
        foreach ($page_numbers as $page) {
            if ($page === '...') {
                $html .= '<span style="padding: 0 8px; color: #999; font-size: 14px;">...</span>';
            } else {
                $is_current = $page == $current_page;
                $html .= sprintf(
                    '<a href="#" data-page="%d" class="pagination-btn %s" style="%s">%d</a>',
                    $page,
                    $is_current ? 'current' : '',
                    $this->get_button_style(false, $is_current),
                    $page
                );
            }
        }
        
        // 下一页按钮
        $next_disabled = $current_page >= $total_pages;
        $next_page = min($total_pages, $current_page + 1);
        $html .= sprintf(
            '<a href="#" data-page="%d" class="pagination-btn next-btn %s" style="%s">›</a>',
            $next_page,
            $next_disabled ? 'disabled' : '',
            $this->get_button_style($next_disabled, false)
        );
        
        return $html;
    }
    
    /**
     * 获取按钮样式
     */
    private function get_button_style($disabled = false, $current = false) {
        $base_style = 'display: inline-flex; align-items: center; justify-content: center; min-width: 32px; height: 32px; padding: 0 8px; text-decoration: none; border-radius: 4px; font-size: 14px; cursor: pointer;';
        
        if ($disabled) {
            return $base_style . ' border: 1px solid #ddd; background: #f5f5f5; color: #ccc; cursor: not-allowed;';
        } elseif ($current) {
            return $base_style . ' border: 1px solid #007cba; background: #007cba; color: white;';
        } else {
            return $base_style . ' border: 1px solid #ddd; background: white; color: #333;';
        }
    }
    
    /**
     * 获取可见的页码数组
     */
    private function get_visible_page_numbers($current_page, $total_pages) {
        $pages = array();
        $max_visible = 7;
        $half = floor($max_visible / 2);
        
        $start = max(1, $current_page - $half);
        $end = min($total_pages, $current_page + $half);
        
        // 调整范围
        if ($end - $start + 1 < $max_visible) {
            if ($start === 1) {
                $end = min($total_pages, $start + $max_visible - 1);
            } else {
                $start = max(1, $end - $max_visible + 1);
            }
        }
        
        // 添加第一页和省略号
        if ($start > 1) {
            $pages[] = 1;
            if ($start > 2) {
                $pages[] = '...';
            }
        }
        
        // 添加中间页码
        for ($i = $start; $i <= $end; $i++) {
            $pages[] = $i;
        }
        
        // 添加省略号和最后一页
        if ($end < $total_pages) {
            if ($end < $total_pages - 1) {
                $pages[] = '...';
            }
            $pages[] = $total_pages;
        }
        
        return $pages;
    }
    
    /**
     * AJAX处理分页请求
     */
    public function ajax_load_news_page() {
        // 验证nonce
        if (!wp_verify_nonce($_POST['nonce'], 'auto_news_nonce')) {
            wp_die('安全验证失败');
        }
        
        $page = intval($_POST['page']);
        $posts_per_page = intval($_POST['posts_per_page']);
        $category = sanitize_text_field($_POST['category']);
        
        // 获取新闻文章
        $query = $this->get_published_news($page, $category, $posts_per_page);
        $total_posts = $this->get_total_news_count($category);
        $total_pages = ceil($total_posts / $posts_per_page);
        
        $response = array(
            'success' => true,
            'html' => '',
            'pagination' => '',
            'current_page' => $page,
            'total_pages' => $total_pages,
            'total_posts' => $total_posts
        );
        
        // 生成新闻HTML
        ob_start();
        if ($query->have_posts()) {
            while ($query->have_posts()) {
                $query->the_post();
                $this->render_news_item(array(
                    'show_excerpt' => 'yes',
                    'show_meta' => 'yes'
                ));
            }
        } else {
            echo '<p class="no-news-found">暂无新闻文章</p>';
        }
        $response['html'] = ob_get_clean();
        
        // 生成分页HTML
        $response['pagination'] = $this->render_pagination_buttons($page, $total_pages);
        
        wp_reset_postdata();
        wp_send_json($response);
    }
}

// 初始化类
new AutoNewsPagination();
?>
