<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Nginx图文交互培训 - 零基础入门</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <!-- Lucide Icons - 2024年最流行的现代图标库 -->
    <script src="https://unpkg.com/lucide@latest/dist/umd/lucide.js"></script>
    <style>
        :root {
            /* 2024年流行的现代配色方案 */
            --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            --secondary-gradient: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            --success-gradient: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            --warning-gradient: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
            --purple-gradient: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
            --orange-gradient: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);

            --primary-color: #6366f1;
            --secondary-color: #8b5cf6;
            --accent-color: #f59e0b;
            --success-color: #06d6a0;
            --danger-color: #f72585;
            --text-dark: #1e293b;
            --text-light: #64748b;
            --bg-light: #f8fafc;
            --white: #ffffff;
            --shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
            --shadow-soft: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', sans-serif;
            background: var(--primary-gradient);
            min-height: 100vh;
            overflow: hidden;
        }

        .slideshow-container {
            position: relative;
            width: 100vw;
            height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .slide {
            display: none;
            width: 90vw;
            max-width: 1200px;
            height: 85vh;
            background: var(--white);
            border-radius: 20px;
            box-shadow: var(--shadow);
            padding: 60px;
            position: relative;
            overflow: hidden;
        }

        .slide.active {
            display: flex;
            flex-direction: column;
            animation: slideIn 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94);
        }

        @keyframes slideIn {
            from {
                opacity: 0;
                transform: translateY(50px) scale(0.95);
            }
            to {
                opacity: 1;
                transform: translateY(0) scale(1);
            }
        }

        .slide-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 40px;
            padding-bottom: 20px;
            border-bottom: 3px solid var(--primary-color);
        }

        .slide-number {
            background: var(--primary-color);
            color: var(--white);
            padding: 10px 20px;
            border-radius: 25px;
            font-weight: 600;
            font-size: 14px;
        }

        .slide-title {
            font-size: 3em;
            font-weight: 700;
            background: var(--primary-gradient);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 20px;
            text-align: center;
            letter-spacing: -0.02em;
        }

        .slide-content {
            flex: 1;
            display: flex;
            flex-direction: column;
            justify-content: center;
        }

        /* 图文布局样式 */
        .content-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 40px;
            height: 100%;
            align-items: center;
        }

        .text-section {
            display: flex;
            flex-direction: column;
            justify-content: center;
        }

        .visual-section {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
        }

        /* 现代化动画图表样式 */
        .animated-diagram {
            width: 100%;
            max-width: 520px;
            height: 420px;
            position: relative;
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            border-radius: 20px;
            border: none;
            box-shadow: var(--shadow-soft);
            overflow: visible; /* 允许箭头超出边界 */
            backdrop-filter: blur(10px);
            margin: 20px auto; /* 增加外边距避免重叠 */
        }

        .diagram-element {
            position: absolute;
            background: var(--white);
            border: none;
            border-radius: 16px;
            padding: 20px;
            text-align: center;
            font-weight: 600;
            color: var(--text-dark);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            cursor: pointer;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            gap: 8px;
        }

        .diagram-element:hover {
            transform: translateY(-4px) scale(1.02);
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
        }

        .diagram-element.highlight {
            background: var(--primary-gradient) !important;
            color: var(--white) !important;
            animation: pulse 2s infinite;
            box-shadow: 0 15px 35px rgba(99, 102, 241, 0.5);
            transform: translateY(-2px) scale(1.05);
            z-index: 10;
        }

        .diagram-element.highlight i {
            color: var(--white) !important;
        }

        .diagram-element.highlight span,
        .diagram-element.highlight small {
            color: var(--white) !important;
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }

        /* 现代化流程箭头 - 指向中心 */
        .arrow {
            position: absolute;
            width: 80px;
            height: 4px;
            background: linear-gradient(90deg, #06d6a0, #4facfe);
            border-radius: 2px;
            transition: all 0.5s ease;
            box-shadow: 0 2px 8px rgba(6, 214, 160, 0.3);
        }

        .arrow::after {
            content: '';
            position: absolute;
            right: -12px;
            top: -8px;
            width: 0;
            height: 0;
            border-left: 20px solid #4facfe;
            border-top: 10px solid transparent;
            border-bottom: 10px solid transparent;
            filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.2));
        }

        .arrow::before {
            content: '';
            position: absolute;
            left: -4px;
            top: 50%;
            transform: translateY(-50%);
            width: 8px;
            height: 8px;
            background: #06d6a0;
            border-radius: 50%;
            box-shadow: 0 0 10px rgba(6, 214, 160, 0.6);
        }

        /* 特殊样式：指向中心的箭头 */
        .arrow.to-center {
            background: linear-gradient(90deg, #06d6a0, #4facfe);
            box-shadow: 0 3px 12px rgba(6, 214, 160, 0.4);
        }

        .arrow.to-center::after {
            border-left-color: #4facfe;
        }

        .arrow.to-center::before {
            background: #06d6a0;
            box-shadow: 0 0 15px rgba(6, 214, 160, 0.8);
        }

        /* 简化箭头样式 */
        .simple-arrow {
            position: absolute;
            border-radius: 2px;
            box-shadow: 0 2px 8px rgba(79, 172, 254, 0.3);
        }

        .simple-arrow::after {
            content: '';
            position: absolute;
            right: -12px;
            top: -8px;
            width: 0;
            height: 0;
            border-left: 20px solid #4facfe;
            border-top: 10px solid transparent;
            border-bottom: 10px solid transparent;
            filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.2));
        }

        .simple-arrow::before {
            content: '';
            position: absolute;
            left: -4px;
            top: 50%;
            transform: translateY(-50%);
            width: 8px;
            height: 8px;
            background: #06d6a0;
            border-radius: 50%;
            box-shadow: 0 0 10px rgba(6, 214, 160, 0.6);
        }

        /* 连接脉冲动画 */
        .connection-pulse {
            animation: pulseRing 2s ease-in-out infinite;
        }

        @keyframes pulseRing {
            0% {
                transform: scale(0.8);
                opacity: 0.8;
            }
            50% {
                transform: scale(1.2);
                opacity: 0.4;
                box-shadow: 0 0 20px rgba(6, 214, 160, 0.6);
            }
            100% {
                transform: scale(0.8);
                opacity: 0.8;
            }
        }

        /* 流动线样式 */
        .flow-line {
            position: absolute;
            background: linear-gradient(90deg, transparent, #06d6a0, #4facfe, transparent);
            background-size: 200% 100%;
            border-radius: 1px;
            z-index: 15;
            animation: flowMove 2s ease-in-out infinite;
        }

        @keyframes flowMove {
            0% {
                background-position: -200% 0;
                opacity: 0.6;
            }
            50% {
                background-position: 0% 0;
                opacity: 1;
            }
            100% {
                background-position: 200% 0;
                opacity: 0.6;
            }
        }

        @keyframes simpleArrowFlow {
            0% {
                opacity: 0.6;
            }
            50% {
                opacity: 1;
                box-shadow: 0 4px 16px rgba(6, 214, 160, 0.6);
            }
            100% {
                opacity: 0.6;
            }
        }

        /* 保持旋转变换的动画 */
        .simple-arrow.animated.arrow-1 {
            animation: simpleArrowFlow 2s ease-in-out infinite;
            transform: rotate(45deg) !important;
        }
        .simple-arrow.animated.arrow-2 {
            animation: simpleArrowFlow 2s ease-in-out infinite;
            transform: rotate(-45deg) !important;
            transform-origin: right center !important;
        }
        .simple-arrow.animated.arrow-3 {
            animation: simpleArrowFlow 2s ease-in-out infinite;
            transform: rotate(-45deg) !important;
        }
        .simple-arrow.animated.arrow-4 {
            animation: simpleArrowFlow 2s ease-in-out infinite;
            transform: rotate(45deg) !important;
            transform-origin: right center !important;
        }

        /* 保持原有旋转角度 */
        .arrow-1 { transform: rotate(45deg) !important; }
        .arrow-2 { transform: rotate(-45deg) !important; transform-origin: right center !important; }
        .arrow-3 { transform: rotate(-45deg) !important; }
        .arrow-4 { transform: rotate(45deg) !important; transform-origin: right center !important; }

        /* SVG箭头样式 */
        .arrow-line {
            stroke: #4facfe;
            stroke-width: 4;
            fill: none;
            stroke-linecap: round;
            filter: drop-shadow(0 2px 4px rgba(79, 172, 254, 0.3));
        }

        .arrow-head {
            fill: #4facfe;
            filter: drop-shadow(0 2px 4px rgba(79, 172, 254, 0.3));
        }

        /* SVG箭头流动动画 */
        .arrow-line.animated,
        .arrow-head.animated {
            animation: svgArrowFlow 2s ease-in-out infinite;
        }

        @keyframes svgArrowFlow {
            0% {
                opacity: 0.6;
                stroke: #06d6a0;
                fill: #06d6a0;
            }
            50% {
                opacity: 1;
                stroke: #4facfe;
                fill: #4facfe;
            }
            100% {
                opacity: 0.6;
                stroke: #06d6a0;
                fill: #06d6a0;
            }
        }

        /* 保留原有箭头样式作为备用 */
        .arrow.animated {
            animation: arrowFlow 2s ease-in-out infinite;
        }

        @keyframes arrowFlow {
            0% {
                opacity: 0.6;
                transform: scale(1);
            }
            50% {
                opacity: 1;
                transform: scale(1.05);
                box-shadow: 0 4px 16px rgba(6, 214, 160, 0.6);
            }
            100% {
                opacity: 0.6;
                transform: scale(1);
            }
        }

        .arrow.animated {
            animation: flowAnimation 3s infinite;
        }

        .arrow.animated::before {
            animation: pulseAnimation 3s infinite;
        }

        @keyframes flowAnimation {
            0% {
                opacity: 0.4;
                transform: scale(1);
            }
            50% {
                opacity: 1;
                transform: scale(1.05);
            }
            100% {
                opacity: 0.4;
                transform: scale(1);
            }
        }

        @keyframes pulseAnimation {
            0% {
                transform: translateY(-50%) scale(1);
                box-shadow: 0 0 10px rgba(6, 214, 160, 0.5);
            }
            50% {
                transform: translateY(-50%) scale(1.3);
                box-shadow: 0 0 20px rgba(6, 214, 160, 0.8);
            }
            100% {
                transform: translateY(-50%) scale(1);
                box-shadow: 0 0 10px rgba(6, 214, 160, 0.5);
            }
        }

        /* 特色框样式 */
        .feature-box {
            background: linear-gradient(135deg, var(--success-color), #059669);
            color: var(--white);
            padding: 30px;
            border-radius: 15px;
            margin: 20px 0;
            text-align: center;
            animation: fadeInUp 0.8s ease;
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .comparison-container {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            margin: 30px 0;
        }

        .comparison-box {
            padding: 30px;
            border-radius: 15px;
            text-align: center;
            transition: all 0.3s ease;
        }

        .comparison-box.before {
            background: linear-gradient(135deg, #fecaca, #ef4444);
            color: var(--white);
        }

        .comparison-box.after {
            background: linear-gradient(135deg, #bbf7d0, #10b981);
            color: var(--white);
        }

        .comparison-box:hover {
            transform: translateY(-10px);
            box-shadow: var(--shadow);
        }

        /* 现代化交互按钮 */
        .interactive-btn {
            background: var(--secondary-gradient);
            color: var(--white);
            border: none;
            padding: 16px 32px;
            border-radius: 50px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            margin: 10px;
            box-shadow: 0 4px 14px 0 rgba(139, 92, 246, 0.3);
            font-size: 14px;
            letter-spacing: 0.5px;
        }

        .interactive-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(139, 92, 246, 0.4);
            background: var(--primary-gradient);
        }

        .interactive-btn:active {
            transform: translateY(-1px);
        }

        /* 导航控制 */
        .navigation {
            position: fixed;
            bottom: 30px;
            left: 50%;
            transform: translateX(-50%);
            display: flex;
            gap: 15px;
            z-index: 1000;
        }

        .nav-btn {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border: none;
            color: var(--primary-color);
            padding: 14px 28px;
            border-radius: 50px;
            cursor: pointer;
            font-weight: 600;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            box-shadow: 0 4px 14px 0 rgba(0, 0, 0, 0.1);
            font-size: 14px;
            letter-spacing: 0.5px;
        }

        .nav-btn:hover {
            background: var(--primary-gradient);
            color: var(--white);
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(99, 102, 241, 0.3);
        }

        .progress-bar {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 4px;
            background: rgba(255,255,255,0.3);
            z-index: 1000;
        }

        .progress-fill {
            height: 100%;
            background: var(--accent-color);
            transition: width 0.3s ease;
        }

        .slide-counter {
            position: fixed;
            top: 30px;
            right: 30px;
            background: rgba(255,255,255,0.9);
            padding: 10px 20px;
            border-radius: 20px;
            font-weight: 600;
            color: var(--primary-color);
            z-index: 1000;
        }

        /* 全屏时隐藏导航元素 */
        :fullscreen .navigation,
        :fullscreen .slide-counter,
        :fullscreen .progress-bar {
            display: none !important;
        }

        /* 兼容性支持 */
        :-webkit-full-screen .navigation,
        :-webkit-full-screen .slide-counter,
        :-webkit-full-screen .progress-bar {
            display: none !important;
        }

        :-moz-full-screen .navigation,
        :-moz-full-screen .slide-counter,
        :-moz-full-screen .progress-bar {
            display: none !important;
        }

        :-ms-fullscreen .navigation,
        :-ms-fullscreen .slide-counter,
        :-ms-fullscreen .progress-bar {
            display: none !important;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .slide {
                width: 95vw;
                height: 90vh;
                padding: 30px;
            }
            
            .slide-title {
                font-size: 2em;
            }
            
            .content-grid {
                grid-template-columns: 1fr;
                gap: 20px;
            }
            
            .animated-diagram {
                height: 300px;
            }
        }

        /* 特殊动画效果 */
        .bounce-in {
            animation: bounceIn 1s ease;
        }

        @keyframes bounceIn {
            0% {
                opacity: 0;
                transform: scale(0.3);
            }
            50% {
                opacity: 1;
                transform: scale(1.05);
            }
            70% {
                transform: scale(0.9);
            }
            100% {
                opacity: 1;
                transform: scale(1);
            }
        }

        .fade-in-left {
            animation: fadeInLeft 0.8s ease;
        }

        @keyframes fadeInLeft {
            from {
                opacity: 0;
                transform: translateX(-50px);
            }
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }

        .fade-in-right {
            animation: fadeInRight 0.8s ease;
        }

        @keyframes fadeInRight {
            from {
                opacity: 0;
                transform: translateX(50px);
            }
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }
    </style>
</head>
<body>
    <!-- 进度条 -->
    <div class="progress-bar">
        <div class="progress-fill" id="progress-fill"></div>
    </div>

    <!-- 幻灯片计数器 -->
    <div class="slide-counter">
        <span id="current-slide">1</span> / <span id="total-slides">10</span>
    </div>

    <div class="slideshow-container">

        <!-- 幻灯片 1: 欢迎页面 -->
        <div class="slide active" id="slide-1">
            <div class="slide-header">
                <div class="slide-number">01</div>
                <h1 class="slide-title bounce-in" style="display: flex; align-items: center; justify-content: center; gap: 15px;">
                    <i data-lucide="rocket" style="width: 48px; height: 48px; color: #6366f1;"></i>
                    Nginx零基础入门
                </h1>
            </div>
            <div class="slide-content">
                <div class="content-grid">
                    <div class="text-section fade-in-left">
                        <h2 style="color: var(--primary-color); margin-bottom: 30px;">欢迎来到Nginx的世界！</h2>
                        <p style="font-size: 1.3em; line-height: 1.6; color: var(--text-dark); margin-bottom: 20px;">
                            🎯 <strong>学习目标：</strong>让完全不懂技术的您，轻松理解Nginx是什么，能做什么
                        </p>
                        <p style="font-size: 1.2em; line-height: 1.6; color: var(--text-light); margin-bottom: 30px;">
                            我们将用最简单的比喻和生动的图解，带您走进Web服务器的奇妙世界
                        </p>
                        <!-- <button class="interactive-btn" onclick="startDemo()" style="display: flex; align-items: center; gap: 8px; justify-content: center;">
                            <i data-lucide="rocket" style="width: 20px; height: 20px;"></i>
                            开始学习之旅
                        </button> -->
                        <div style="margin-top: 20px; padding: 15px; background: rgba(255,255,255,0.1); border-radius: 12px; backdrop-filter: blur(10px);">
                            <h4 style="color: var(--primary-color); margin-bottom: 10px; display: flex; align-items: center; gap: 8px;">
                                <i data-lucide="info" style="width: 16px; height: 16px;"></i>
                                Nginx中心枢纽模式
                            </h4>
                            <p style="font-size: 12px; color: var(--text-light); line-height: 1.4;">
                                🎯 所有流量都汇聚到Nginx中心<br>
                                ⚡ Nginx统一处理：用户请求、互联网流量、网站服务、应用服务<br>
                                🚀 智能分发、负载均衡、安全防护
                            </p>
                        </div>
                    </div>
                    <div class="visual-section fade-in-right">
                        <div class="animated-diagram">
                            <!-- 顶部：互联网和用户 -->
                            <div class="diagram-element" style="top: 30px; left: 60px; width: 110px; height: 80px; background: var(--purple-gradient); z-index: 5;">
                                <i data-lucide="globe" style="width: 24px; height: 24px; color: white;"></i>
                                <span style="color: white; font-weight: 600; font-size: 14px;">互联网</span>
                            </div>
                            <div class="diagram-element" style="top: 30px; right: 60px; width: 110px; height: 80px; background: var(--orange-gradient); z-index: 5;">
                                <i data-lucide="users" style="width: 24px; height: 24px; color: white;"></i>
                                <span style="color: white; font-weight: 600; font-size: 14px;">用户</span>
                            </div>

                            <!-- 中心：Nginx -->
                            <div class="diagram-element highlight" style="top: 160px; left: 35%; transform: translateX(-50%); width: 140px; height: 90px; z-index: 10;">
                                <i data-lucide="zap" style="width: 32px; height: 32px; color: white;"></i>
                                <span style="color: white; font-weight: 700; font-size: 16px;">Nginx</span>
                                <small style="color: rgba(255,255,255,0.9); font-size: 12px;">超级服务器</small>
                            </div>

                            <!-- 底部：网站和应用 -->
                            <div class="diagram-element" style="bottom: 30px; left: 60px; width: 110px; height: 80px; background: var(--success-gradient); z-index: 5;">
                                <i data-lucide="monitor" style="width: 24px; height: 24px; color: white;"></i>
                                <span style="color: white; font-weight: 600; font-size: 14px;">网站</span>
                            </div>
                            <div class="diagram-element" style="bottom: 30px; right: 60px; width: 110px; height: 80px; background: var(--warning-gradient); z-index: 5;">
                                <i data-lucide="smartphone" style="width: 24px; height: 24px; color: white;"></i>
                                <span style="color: white; font-weight: 600; font-size: 14px;">应用</span>
                            </div>

                            <!-- 简化箭头：使用CSS旋转确保正确指向 -->
                            <!-- 连接动画效果 -->
                            <div class="connection-pulse" style="position: absolute; top: 50px; left: 50px; width: 100px; height: 100px; border: 3px solid #06d6a0; border-radius: 50%; opacity: 0.6;"></div>
                            <div class="connection-pulse" style="position: absolute; top: 50px; right: 50px; width: 100px; height: 100px; border: 3px solid #06d6a0; border-radius: 50%; opacity: 0.6;"></div>
                            <div class="connection-pulse" style="position: absolute; bottom: 50px; left: 50px; width: 100px; height: 100px; border: 3px solid #06d6a0; border-radius: 50%; opacity: 0.6;"></div>
                            <div class="connection-pulse" style="position: absolute; bottom: 50px; right: 50px; width: 100px; height: 100px; border: 3px solid #06d6a0; border-radius: 50%; opacity: 0.6;"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 幻灯片 2: 什么是Nginx -->
        <div class="slide" id="slide-2">
            <div class="slide-header">
                <div class="slide-number">02</div>
                <h1 class="slide-title" style="display: flex; align-items: center; justify-content: center; gap: 15px;">
                    <i data-lucide="server" style="width: 48px; height: 48px; color: #6366f1;"></i>
                    什么是Nginx？
                </h1>
            </div>
            <div class="slide-content">
                <div class="content-grid">
                    <div class="text-section fade-in-left">
                        <h2 style="color: var(--primary-color); margin-bottom: 30px;">想象一个超级智能的便利店</h2>
                        <div style="font-size: 1.2em; line-height: 1.8; color: var(--text-dark);">
                            <p style="margin-bottom: 20px;">🏪 <strong>普通便利店：</strong>一个店员，一次只能服务一个顾客</p>
                            <p style="margin-bottom: 20px;">⚡ <strong>Nginx便利店：</strong>一个超级店员，同时服务成千上万个顾客</p>
                            <p style="margin-bottom: 30px;">🎯 <strong>关键特点：</strong></p>
                            <ul style="list-style: none; padding-left: 0;">
                                <li style="margin: 15px 0;">✅ 速度超快 - 像闪电一样</li>
                                <li style="margin: 15px 0;">✅ 永不疲倦 - 24小时不停工作</li>
                                <li style="margin: 15px 0;">✅ 超级节能 - 消耗很少资源</li>
                                <li style="margin: 15px 0;">✅ 聪明分配 - 智能处理请求</li>
                            </ul>
                        </div>
                        <button class="interactive-btn" onclick="showNginxDemo()" style="display: flex; align-items: center; gap: 8px; justify-content: center;">
                            <i data-lucide="gamepad-2" style="width: 20px; height: 20px;"></i>
                            看看Nginx有多厉害
                        </button>
                    </div>
                    <div class="visual-section fade-in-right">
                        <div class="comparison-container">
                            <div class="comparison-box before">
                                <h3 style="display: flex; align-items: center; gap: 10px; justify-content: center;">
                                    <i data-lucide="alert-triangle" style="width: 24px; height: 24px;"></i>
                                    普通服务器
                                </h3>
                                <div style="margin: 20px 0;">
                                    <i data-lucide="turtle" style="width: 48px; height: 48px; margin-bottom: 10px;"></i>
                                    <p>处理1000个用户</p>
                                    <p>经常崩溃</p>
                                    <p>响应慢</p>
                                </div>
                            </div>
                            <div class="comparison-box after">
                                <h3 style="display: flex; align-items: center; gap: 10px; justify-content: center;">
                                    <i data-lucide="rocket" style="width: 24px; height: 24px;"></i>
                                    Nginx服务器
                                </h3>
                                <div style="margin: 20px 0;">
                                    <i data-lucide="zap" style="width: 48px; height: 48px; margin-bottom: 10px;"></i>
                                    <p>处理10万+用户</p>
                                    <p>稳定可靠</p>
                                    <p>超快响应</p>
                                </div>
                            </div>
                        </div>
                        <div class="feature-box">
                            <h3 style="display: flex; align-items: center; gap: 10px; justify-content: center;">
                                <i data-lucide="trophy" style="width: 24px; height: 24px;"></i>
                                全球信任
                            </h3>
                            <p>Netflix、Airbnb、GitHub等顶级公司都在使用Nginx</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 幻灯片 3: 前后端关系 -->
        <div class="slide" id="slide-3">
            <div class="slide-header">
                <div class="slide-number">03</div>
                <h1 class="slide-title" style="display: flex; align-items: center; justify-content: center; gap: 15px;">
                    <i data-lucide="building" style="width: 48px; height: 48px; color: #6366f1;"></i>
                    前端和后端的关系
                </h1>
            </div>
            <div class="slide-content">
                <div class="content-grid">
                    <div class="text-section fade-in-left">
                        <h2 style="color: var(--primary-color); margin-bottom: 30px;">就像一家高档餐厅</h2>
                        <div style="font-size: 1.2em; line-height: 1.8; color: var(--text-dark);">
                            <p style="margin-bottom: 20px;">🍽️ <strong>前端 = 餐厅前厅</strong></p>
                            <ul style="margin-left: 20px; margin-bottom: 20px;">
                                <li>漂亮的装修（网页设计）</li>
                                <li>友好的服务员（用户界面）</li>
                                <li>菜单展示（内容展示）</li>
                            </ul>

                            <p style="margin-bottom: 20px;">👨‍🍳 <strong>后端 = 餐厅后厨</strong></p>
                            <ul style="margin-left: 20px; margin-bottom: 20px;">
                                <li>厨师做菜（处理数据）</li>
                                <li>食材管理（数据库）</li>
                                <li>订单处理（业务逻辑）</li>
                            </ul>

                            <p style="margin-bottom: 20px;">🎯 <strong>Nginx = 餐厅经理</strong></p>
                            <ul style="margin-left: 20px;">
                                <li>协调前厅和后厨</li>
                                <li>分配服务员工作</li>
                                <li>确保服务质量</li>
                            </ul>
                        </div>
                    </div>
                    <div class="visual-section fade-in-right">
                        <div class="animated-diagram">
                            <div class="diagram-element" style="top: 30px; left: 30px; width: 100px; background: var(--orange-gradient);">
                                <i data-lucide="users" style="width: 20px; height: 20px; color: white;"></i>
                                <span style="color: white; font-weight: 600;">用户</span>
                                <small style="color: rgba(255,255,255,0.9);">顾客</small>
                            </div>
                            <div class="diagram-element" style="top: 30px; right: 30px; width: 100px; background: var(--success-gradient);">
                                <i data-lucide="layout" style="width: 20px; height: 20px; color: white;"></i>
                                <span style="color: white; font-weight: 600;">前端</span>
                                <small style="color: rgba(255,255,255,0.9);">前厅</small>
                            </div>
                            <div class="diagram-element highlight" style="top: 100px; left: 40%; transform: translateX(-50%); width: 120px;">
                                <i data-lucide="zap" style="width: 24px; height: 24px; color: white;"></i>
                                <span style="color: white; font-weight: 700;">Nginx</span>
                                <small style="color: rgba(255,255,255,0.9);">经理</small>
                            </div>
                            <div class="diagram-element" style="bottom: 80px; left: 30px; width: 100px; background: var(--warning-gradient);">
                                <i data-lucide="settings" style="width: 20px; height: 20px; color: white;"></i>
                                <span style="color: white; font-weight: 600;">后端</span>
                                <small style="color: rgba(255,255,255,0.9);">后厨</small>
                            </div>
                            <div class="diagram-element" style="bottom: 80px; right: 30px; width: 100px; background: var(--purple-gradient);">
                                <i data-lucide="database" style="width: 20px; height: 20px; color: white;"></i>
                                <span style="color: white; font-weight: 600;">数据库</span>
                                <small style="color: rgba(255,255,255,0.9);">仓库</small>
                            </div>
                            <!-- 简单连接线 -->
                            <div class="flow-line" style="top: 67px; left: 135px; width: 250px; height: 2px;"></div>
                            <div class="flow-line" style="top: 192px; left: 100px; width: 85px; height: 2px; transform: rotate(-25deg);"></div>
                            <div class="flow-line" style="top: 150px; left: 320px; width: 85px; height: 2px; transform: rotate(-25deg);"></div>
                            <!-- <div class="flow-line" style="top: 192px; right: 100px; width: 85px; height: 2px; transform: rotate(25deg);"></div> -->
                            <div class="flow-line" style="bottom: 117px; left: 135px; width: 250px; height: 2px;"></div>
                        </div>
                        <!-- <button class="interactive-btn" onclick="animateFlow()" style="display: flex; align-items: center; gap: 8px; justify-content: center;">
                            <i data-lucide="play-circle" style="width: 20px; height: 20px;"></i>
                            看看数据如何流动
                        </button> -->
                    </div>
                </div>
            </div>
        </div>

        <!-- 幻灯片 4: HTTP服务器原理 -->
        <div class="slide" id="slide-4">
            <div class="slide-header">
                <div class="slide-number">04</div>
                <h1 class="slide-title">📮 HTTP服务器 - 网络邮局</h1>
            </div>
            <div class="slide-content">
                <div class="content-grid">
                    <div class="text-section fade-in-left">
                        <h2 style="color: var(--primary-color); margin-bottom: 30px;">HTTP就像现代化邮局</h2>
                        <div style="font-size: 1.2em; line-height: 1.8; color: var(--text-dark);">
                            <p style="margin-bottom: 20px;">📬 <strong>HTTP请求 = 寄信</strong></p>
                            <ul style="margin-left: 20px; margin-bottom: 20px;">
                                <li><strong>GET</strong> - 去邮局取信（获取网页）</li>
                                <li><strong>POST</strong> - 寄信给朋友（提交表单）</li>
                                <li><strong>PUT</strong> - 更新地址（修改数据）</li>
                                <li><strong>DELETE</strong> - 取消订阅（删除数据）</li>
                            </ul>

                            <p style="margin-bottom: 20px;">📋 <strong>HTTP状态码 = 回执单</strong></p>
                            <ul style="margin-left: 20px; margin-bottom: 20px;">
                                <li><span style="color: var(--success-color);">✅ 200</span> - 信件成功送达</li>
                                <li><span style="color: var(--accent-color);">🔄 301</span> - 收件人搬家了</li>
                                <li><span style="color: var(--danger-color);">❌ 404</span> - 找不到这个地址</li>
                                <li><span style="color: var(--danger-color);">💥 500</span> - 邮局出故障了</li>
                            </ul>
                        </div>
                        <button class="interactive-btn" onclick="showHttpDemo()">📨 体验HTTP请求过程</button>
                    </div>
                    <div class="visual-section fade-in-right">
                        <div class="animated-diagram">
                            <div class="diagram-element" style="top: 30px; left: 30px; width: 100px;">
                                👤<br>用户<br><small>寄信人</small>
                            </div>
                            <div class="diagram-element" style="top: 30px; right: 30px; width: 100px;">
                                🌐<br>浏览器<br><small>邮递员</small>
                            </div>
                            <div class="diagram-element highlight" style="top: 50px; left: 40%; transform: translateX(-50%); width: 120px;">
                                📮<br>HTTP<br><small>邮局</small>
                            </div>
                            <div class="diagram-element" style="bottom: 80px; left: 50%; transform: translateX(-50%); width: 120px;">
                                🖥️<br>Web服务器<br><small>收件人</small>
                            </div>
                            <!-- 简单连接线 -->
                            <div class="flow-line" style="top: 62px; left: 140px; width: 80px; height: 2px;"></div>
                            <div class="flow-line" style="top: 62px; right: 140px; width: 80px; height: 2px;"></div>
                            <div class="flow-line" style="top: 200px; left: 50%; width: 80px; height: 2px; transform: translateX(-50%) rotate(90deg);"></div>
                        </div>
                        <div class="feature-box">
                            <h3>🚀 Nginx的HTTP优势</h3>
                            <p>处理HTTP请求比传统服务器快10倍！</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 幻灯片 5: 反向代理概念 -->
        <div class="slide" id="slide-5">
            <div class="slide-header">
                <div class="slide-number">05</div>
                <h1 class="slide-title">🚦 反向代理 - 智能交通指挥</h1>
            </div>
            <div class="slide-content">
                <div class="content-grid">
                    <div class="text-section fade-in-left">
                        <h2 style="color: var(--primary-color); margin-bottom: 30px;">想象城市交通指挥中心</h2>
                        <div style="font-size: 1.2em; line-height: 1.8; color: var(--text-dark);">
                            <p style="margin-bottom: 20px;">🚗 <strong>没有指挥的路口：</strong></p>
                            <ul style="margin-left: 20px; margin-bottom: 20px;">
                                <li>车辆乱成一团</li>
                                <li>经常发生拥堵</li>
                                <li>效率很低</li>
                            </ul>

                            <p style="margin-bottom: 20px;">🚦 <strong>有智能指挥的路口：</strong></p>
                            <ul style="margin-left: 20px; margin-bottom: 20px;">
                                <li>车流有序分配</li>
                                <li>避免拥堵</li>
                                <li>效率提升10倍</li>
                            </ul>

                            <p style="margin-bottom: 20px;">⚡ <strong>Nginx反向代理就是这个智能指挥员：</strong></p>
                            <ul style="margin-left: 20px;">
                                <li>🎯 智能分配用户请求</li>
                                <li>⚖️ 平衡服务器负载</li>
                                <li>🔄 自动故障转移</li>
                                <li>🚀 提升整体性能</li>
                            </ul>
                        </div>
                    </div>
                    <div class="visual-section fade-in-right">
                        <div class="animated-diagram">
                            <div class="diagram-element" style="top: 20px; left: 20px; width: 80px;">
                                👥<br>用户1
                            </div>
                            <div class="diagram-element" style="top: 20px; left: 50%; transform: translateX(-50%); width: 80px;">
                                👥<br>用户2
                            </div>
                            <div class="diagram-element" style="top: 20px; right: 20px; width: 80px;">
                                👥<br>用户3
                            </div>
                            <div class="diagram-element highlight" style="top: 140px; left: 35%; transform: translateX(-50%); width: 140px;">
                                🚦<br>Nginx代理<br><small>智能指挥</small>
                            </div>
                            <div class="diagram-element" style="bottom: 40px; left: 30px; width: 90px;">
                                🖥️<br>服务器A
                            </div>
                            <div class="diagram-element" style="bottom: 40px; left: 50%; transform: translateX(-50%); width: 90px;">
                                🖥️<br>服务器B
                            </div>
                            <div class="diagram-element" style="bottom: 40px; right: 30px; width: 90px;">
                                🖥️<br>服务器C
                            </div>
                            <!-- 简单连接线 -->
                            <div class="flow-line" style="top: 102px; left: 60px; width: 100px; height: 2px; transform: rotate(45deg);"></div>
                            <div class="flow-line" style="top: 120px; left: 50%; width: 50px; height: 2px; transform: translateX(-50%) rotate(90deg);"></div>
                            <div class="flow-line" style="top: 102px; right: 60px; width: 100px; height: 2px; transform: rotate(-45deg);"></div>
                            <div class="flow-line" style="bottom: 142px; left: 80px; width: 100px; height: 2px; transform: rotate(-45deg);"></div>
                            <div class="flow-line" style="bottom: 142px; left: 50%; width: 50px; height: 2px; transform: translateX(-50%) rotate(90deg);"></div>
                            <div class="flow-line" style="bottom: 142px; right: 80px; width: 100px; height: 2px; transform: rotate(45deg);"></div>
                        </div>
                        <!-- <button class="interactive-btn" onclick="showProxyDemo()">🎮 看看负载均衡效果</button> -->
                    </div>
                </div>
            </div>
        </div>

        <!-- 幻灯片 6: 安全防护功能 -->
        <div class="slide" id="slide-6">
            <div class="slide-header">
                <div class="slide-number">06</div>
                <h1 class="slide-title">🛡️ 安全防护 - 数字保镖</h1>
            </div>
            <div class="slide-content">
                <div class="content-grid">
                    <div class="text-section fade-in-left">
                        <h2 style="color: var(--primary-color); margin-bottom: 30px;">Nginx就像专业保镖</h2>
                        <div style="font-size: 1.2em; line-height: 1.8; color: var(--text-dark);">
                            <p style="margin-bottom: 20px;">🚫 <strong>访问控制 = 门卫检查</strong></p>
                            <ul style="margin-left: 20px; margin-bottom: 20px;">
                                <li>检查访客身份证（IP地址）</li>
                                <li>只允许好人进入</li>
                                <li>拒绝可疑人员</li>
                            </ul>

                            <p style="margin-bottom: 20px;">⚡ <strong>速率限制 = 限流控制</strong></p>
                            <ul style="margin-left: 20px; margin-bottom: 20px;">
                                <li>防止恶意刷屏</li>
                                <li>保护服务器不被压垮</li>
                                <li>确保正常用户体验</li>
                            </ul>

                            <p style="margin-bottom: 20px;">🔒 <strong>SSL加密 = 保险箱</strong></p>
                            <ul style="margin-left: 20px;">
                                <li>数据传输加密保护</li>
                                <li>防止信息泄露</li>
                                <li>确保通信安全</li>
                            </ul>
                        </div>
                        <button class="interactive-btn" onclick="showSecurityDemo()">🔐 体验安全防护</button>
                    </div>
                    <div class="visual-section fade-in-right">
                        <div class="comparison-container">
                            <div class="comparison-box before">
                                <h3>😱 没有保护</h3>
                                <div style="margin: 20px 0;">
                                    <div style="font-size: 2em;">🚨</div>
                                    <p>恶意攻击</p>
                                    <p>数据泄露</p>
                                    <p>服务中断</p>
                                </div>
                            </div>
                            <div class="comparison-box after">
                                <h3>🛡️ Nginx保护</h3>
                                <div style="margin: 20px 0;">
                                    <div style="font-size: 2em;">✅</div>
                                    <p>安全防护</p>
                                    <p>数据加密</p>
                                    <p>稳定运行</p>
                                </div>
                            </div>
                        </div>
                        <div class="feature-box">
                            <h3>🏆 企业级安全</h3>
                            <p>银行、政府、大型企业都信赖Nginx的安全防护</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 幻灯片 7: 性能优化 -->
        <div class="slide" id="slide-7">
            <div class="slide-header">
                <div class="slide-number">07</div>
                <h1 class="slide-title">🚀 性能优化 - 超级加速器</h1>
            </div>
            <div class="slide-content">
                <div class="content-grid">
                    <div class="text-section fade-in-left">
                        <h2 style="color: var(--primary-color); margin-bottom: 30px;">让网站飞起来的秘密武器</h2>
                        <div style="font-size: 1.2em; line-height: 1.8; color: var(--text-dark);">
                            <p style="margin-bottom: 20px;">💨 <strong>缓存技术 = 快递代收点</strong></p>
                            <ul style="margin-left: 20px; margin-bottom: 20px;">
                                <li>常用内容提前准备好</li>
                                <li>用户访问秒级响应</li>
                                <li>减少服务器压力</li>
                            </ul>

                            <p style="margin-bottom: 20px;">📦 <strong>内容压缩 = 真空包装</strong></p>
                            <ul style="margin-left: 20px; margin-bottom: 20px;">
                                <li>文件体积减少70%</li>
                                <li>传输速度大幅提升</li>
                                <li>节省带宽成本</li>
                            </ul>

                            <p style="margin-bottom: 20px;">🔗 <strong>连接复用 = 专用通道</strong></p>
                            <ul style="margin-left: 20px;">
                                <li>一条通道传输多个文件</li>
                                <li>减少建立连接时间</li>
                                <li>整体效率提升50%</li>
                            </ul>
                        </div>
                    </div>
                    <div class="visual-section fade-in-right">
                        <div class="animated-diagram">
                            <div class="diagram-element" style="top: 30px; left: 30px; width: 100px; background: #fef3c7;">
                                ⏱️<br>原来<br><small>5秒加载</small>
                            </div>
                            <div class="diagram-element highlight" style="top: 30px; right: 30px; width: 100px;">
                                ⚡<br>现在<br><small>0.5秒</small>
                            </div>
                            <div class="diagram-element" style="top: 150px; left: 20px; width: 80px; background: #dbeafe;">
                                💨<br>缓存
                            </div>
                            <div class="diagram-element" style="top: 150px; left: 50%; transform: translateX(-50%); width: 80px; background: #dcfce7;">
                                📦<br>压缩
                            </div>
                            <div class="diagram-element" style="top: 150px; right: 20px; width: 80px; background: #fce7f3;">
                                🔗<br>复用
                            </div>
                            <div class="diagram-element" style="bottom: 50px; left: 50%; transform: translateX(-50%); width: 120px;">
                                📈<br>性能提升<br><small>10倍速度</small>
                            </div>
                            <!-- 简单连接线 -->
                            <div class="flow-line" style="top: 200px; left: 120px; width: 80px; height: 2px;"></div>
                            <div class="flow-line" style="top: 200px; left: 320px; width: 80px; height: 2px;"></div>
                            <div class="flow-line" style="top: 242px; left: 50%; width: 40px; height: 2px; transform: translateX(-50%) rotate(90deg);"></div>
                        </div>
                        <button class="interactive-btn" onclick="showPerformanceDemo()">📊 查看性能对比</button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 幻灯片 8: 实际应用案例 -->
        <div class="slide" id="slide-8">
            <div class="slide-header">
                <div class="slide-number">08</div>
                <h1 class="slide-title">🌟 实际应用案例</h1>
            </div>
            <div class="slide-content">
                <div class="content-grid">
                    <div class="text-section fade-in-left">
                        <h2 style="color: var(--primary-color); margin-bottom: 30px;">看看谁在使用Nginx</h2>
                        <div style="font-size: 1.2em; line-height: 1.8; color: var(--text-dark);">
                            <p style="margin-bottom: 20px;">🛒 <strong>电商平台（如淘宝、京东）</strong></p>
                            <ul style="margin-left: 20px; margin-bottom: 20px;">
                                <li>双11处理亿级用户访问</li>
                                <li>确保购物体验流畅</li>
                                <li>防止系统崩溃</li>
                            </ul>

                            <p style="margin-bottom: 20px;">🎬 <strong>视频网站（如Netflix、YouTube）</strong></p>
                            <ul style="margin-left: 20px; margin-bottom: 20px;">
                                <li>全球用户同时观看</li>
                                <li>高清视频不卡顿</li>
                                <li>智能CDN分发</li>
                            </ul>

                            <p style="margin-bottom: 20px;">🏦 <strong>金融服务（银行、支付）</strong></p>
                            <ul style="margin-left: 20px;">
                                <li>毫秒级交易响应</li>
                                <li>银行级安全防护</li>
                                <li>99.99%可用性保障</li>
                            </ul>
                        </div>
                    </div>
                    <div class="visual-section fade-in-right">
                        <div class="animated-diagram">
                            <div class="diagram-element" style="top: 30px; left: 30px; width: 100px; background: #fef3c7;">
                                🛍️<br>电商<br><small>亿级用户</small>
                            </div>
                            <div class="diagram-element" style="top: 30px; right: 30px; width: 100px; background: #dbeafe;">
                                🎥<br>视频<br><small>全球分发</small>
                            </div>
                            <div class="diagram-element highlight" style="top: 150px; left: 40%; transform: translateX(-50%); width: 120px;">
                                ⚡<br>Nginx<br><small>核心引擎</small>
                            </div>
                            <div class="diagram-element" style="bottom: 80px; left: 30px; width: 100px; background: #dcfce7;">
                                🏦<br>金融<br><small>安全可靠</small>
                            </div>
                            <div class="diagram-element" style="bottom: 80px; right: 30px; width: 100px; background: #fce7f3;">
                                🎮<br>游戏<br><small>低延迟</small>
                            </div>
                            <div class="flow-line" style="top: 132px; left: 100px; width: 100px; height: 2px; transform: rotate(45deg);"></div>
                            <div class="flow-line" style="top: 132px; right: 100px; width: 100px; height: 2px; transform: rotate(-45deg);"></div>
                            <div class="flow-line" style="bottom: 142px; left: 100px; width: 100px; height: 2px; transform: rotate(-45deg);"></div>
                            <div class="flow-line" style="bottom: 142px; right: 100px; width: 100px; height: 2px; transform: rotate(45deg);"></div>
                        </div>
                        <div class="feature-box">
                            <h3>🌍 全球信赖</h3>
                            <p>全球前1000万网站中，超过30%使用Nginx</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 幻灯片 9: 学习总结 -->
        <div class="slide" id="slide-9">
            <div class="slide-header">
                <div class="slide-number">09</div>
                <h1 class="slide-title">📚 学习总结</h1>
            </div>
            <div class="slide-content">
                <div class="content-grid">
                    <div class="text-section fade-in-left">
                        <h2 style="color: var(--primary-color); margin-bottom: 30px; text-align: center;">🎉 恭喜！您已经掌握了Nginx的核心概念！</h2>

                        <!-- 知识点回顾卡片 -->
                        <div class="feature-box" style="margin-bottom: 25px; background: linear-gradient(135deg, #dbeafe, #bfdbfe); border: 1px solid #93c5fd;">
                            <h3 style="color: #1e40af; margin-bottom: 15px; font-weight: bold;">🎯 核心知识点回顾</h3>
                            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 8px; font-size: 0.95em; color: #1e3a8a; font-weight: 500;">
                                <div>✅ Nginx是什么 - 超级智能的Web服务器</div>
                                <div>✅ 前后端关系 - 餐厅前厅和后厨的协作</div>
                                <div>✅ HTTP原理 - 网络世界的邮局系统</div>
                                <div>✅ 反向代理 - 智能交通指挥中心</div>
                                <div>✅ 安全防护 - 专业的数字保镖</div>
                                <div>✅ 性能优化 - 让网站飞起来的技术</div>
                            </div>
                        </div>

                        <!-- 能力提升卡片 -->
                        <div class="feature-box" style="background: linear-gradient(135deg, #dcfce7, #bbf7d0); border: 1px solid #86efac;">
                            <h3 style="color: #15803d; margin-bottom: 15px; font-weight: bold;">🚀 现在您可以</h3>
                            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 8px; font-size: 0.95em; color: #14532d; font-weight: 500;">
                                <div>💡 理解网站是如何工作的</div>
                                <div>🎯 知道为什么需要Nginx</div>
                                <div>🗣️ 向别人解释Nginx的价值</div>
                                <div>💬 参与技术讨论</div>
                            </div>
                        </div>
                    </div>
                    <div class="visual-section fade-in-right">
                        <div class="animated-diagram" style="position: relative; height: 400px;">
                            <!-- 中心毕业徽章 -->
                            <div class="diagram-element highlight" style="top: 35%; left: 38%; transform: translate(-50%, -50%); width: 120px; height: 120px; border-radius: 50%; display: flex; flex-direction: column; align-items: center; justify-content: center; font-size: 0.9em;">
                                <br><strong>Nginx</strong><br>
                            </div>

                            <!-- 知识点环绕布局 -->
                            <div class="diagram-element" style="top: 20px; left: 50%; transform: translateX(-50%); width: 70px; height: 70px; background: #fef3c7; border-radius: 50%; display: flex; flex-direction: column; align-items: center; justify-content: center; font-size: 0.8em;">
                                🏪<br><small>基础</small>
                            </div>
                            <div class="diagram-element" style="top: 30%; right: 10px; width: 70px; height: 70px; background: #dbeafe; border-radius: 50%; display: flex; flex-direction: column; align-items: center; justify-content: center; font-size: 0.8em;">
                                🏢<br><small>架构</small>
                            </div>
                            <div class="diagram-element" style="bottom: 20px; right: 30%; width: 70px; height: 70px; background: #dcfce7; border-radius: 50%; display: flex; flex-direction: column; align-items: center; justify-content: center; font-size: 0.8em;">
                                📮<br><small>HTTP</small>
                            </div>
                            <div class="diagram-element" style="bottom: 20px; left: 30%; width: 70px; height: 70px; background: #fce7f3; border-radius: 50%; display: flex; flex-direction: column; align-items: center; justify-content: center; font-size: 0.8em;">
                                🚦<br><small>代理</small>
                            </div>
                            <div class="diagram-element" style="top: 30%; left: 10px; width: 70px; height: 70px; background: #f3e8ff; border-radius: 50%; display: flex; flex-direction: column; align-items: center; justify-content: center; font-size: 0.8em;">
                                🛡️<br><small>安全</small>
                            </div>

                            <!-- 连接线 -->
                            <div class="flow-line" style="top: 40%; left: 20%; width: 80px; height: 2px; transform: rotate(10deg);"></div>
                            <div class="flow-line" style="top: 40%; right: 20%; width: 80px; height: 2px; transform: rotate(-10deg);"></div>
                            <div class="flow-line" style="bottom: 25%; left: 35%; width: 80px; height: 2px; transform: rotate(-55deg);"></div>
                            <div class="flow-line" style="bottom: 25%; right: 35%; width: 80px; height: 2px; transform: rotate(55deg);"></div>
                            <div class="flow-line" style="top: 23%; left: 50%; width: 2px; height: 40px; transform: translateX(-50%);"></div>
                        </div>
                        <div class="feature-box" style="margin-top: 20px;">
                            <h3>🌟 继续学习</h3>
                            <p>这只是开始！继续探索更多Web技术的奥秘</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 幻灯片 10: 感谢页面 -->
        <div class="slide" id="slide-10">
            <div class="slide-header">
                <div class="slide-number">10</div>
                <h1 class="slide-title bounce-in">🎉 感谢您的学习！</h1>
            </div>
            <div class="slide-content">
                <div style="text-align: center; height: 100%; display: flex; flex-direction: column; justify-content: center;">
                    <div class="fade-in-left">
                        <h2 style="color: var(--primary-color); margin-bottom: 40px; font-size: 2.5em;">学习之旅圆满结束！</h2>
                        <p style="font-size: 1.5em; line-height: 1.8; color: var(--text-dark); margin-bottom: 40px;">
                            🎯 您已经从零基础成功入门Nginx<br>
                            🚀 现在可以自信地谈论Web服务器技术<br>
                            💡 继续保持学习的热情，探索更多技术奥秘
                        </p>
                        <div style="margin: 40px 0;">
                            <button class="interactive-btn" onclick="restartPresentation()">🔄 重新学习</button>
                            <!-- <button class="interactive-btn" onclick="showCertificate()">🏆 获取证书</button> -->
                        </div>
                    </div>
                    <div class="fade-in-right" style="margin-top: 40px;">
                        <div class="feature-box">
                            <h3>📞 继续交流</h3>
                            <p>有任何问题欢迎随时讨论<br>技术让世界更美好！</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 导航控制 -->
    <div class="navigation">
        <button class="nav-btn" onclick="previousSlide()" style="display: flex; align-items: center; gap: 8px;">
            <i data-lucide="chevron-left" style="width: 18px; height: 18px;"></i>
            上一页
        </button>
        <button class="nav-btn" onclick="nextSlide()" style="display: flex; align-items: center; gap: 8px;">
            下一页
            <i data-lucide="chevron-right" style="width: 18px; height: 18px;"></i>
        </button>
        <button class="nav-btn" onclick="toggleFullscreen()" style="display: flex; align-items: center; gap: 8px;">
            <i data-lucide="maximize" style="width: 18px; height: 18px;"></i>
            全屏
        </button>
    </div>

    <script>
        let currentSlide = 1;
        const totalSlides = 10;

        // 幻灯片切换功能
        function showSlide(slideNumber) {
            document.querySelectorAll('.slide').forEach(slide => {
                slide.classList.remove('active');
            });

            document.getElementById(`slide-${slideNumber}`).classList.add('active');
            document.getElementById('current-slide').textContent = slideNumber;

            const progress = (slideNumber / totalSlides) * 100;
            document.getElementById('progress-fill').style.width = progress + '%';

            currentSlide = slideNumber;
        }

        function nextSlide() {
            if (currentSlide < totalSlides) {
                showSlide(currentSlide + 1);
            }
        }

        function previousSlide() {
            if (currentSlide > 1) {
                showSlide(currentSlide - 1);
            }
        }

        function toggleFullscreen() {
            const fullscreenBtn = document.querySelector('.nav-btn[onclick="toggleFullscreen()"]');
            const icon = fullscreenBtn.querySelector('i');

            if (!document.fullscreenElement) {
                document.documentElement.requestFullscreen().then(() => {
                    // 全屏后更新图标
                    icon.setAttribute('data-lucide', 'minimize');
                    if (typeof lucide !== 'undefined') {
                        lucide.createIcons();
                    }
                });
            } else {
                document.exitFullscreen().then(() => {
                    // 退出全屏后更新图标
                    icon.setAttribute('data-lucide', 'maximize');
                    if (typeof lucide !== 'undefined') {
                        lucide.createIcons();
                    }
                });
            }
        }

        // 交互演示功能
        function startDemo() {
            // 显示Nginx中心枢纽演示
            const currentSlide = document.querySelector('.slide.active');
            const pulseElements = currentSlide.querySelectorAll('.connection-pulse');
            const elements = currentSlide.querySelectorAll('.diagram-element');

            // 重置所有元素
            elements.forEach(el => el.classList.remove('highlight'));

            // 演示Nginx中心枢纽模式
            setTimeout(() => {
                // 第一步：外部流量汇聚
                elements[0].classList.add('highlight'); // 互联网
                elements[1].classList.add('highlight'); // 用户
                alert('🌐 外部流量汇聚到Nginx中心...');
            }, 500);

            setTimeout(() => {
                // 第二步：Nginx处理
                elements[2].classList.add('highlight'); // Nginx
                alert('⚡ Nginx作为中心枢纽，统一处理所有请求...');
            }, 2000);

            setTimeout(() => {
                // 第三步：连接后端服务
                elements[3].classList.add('highlight'); // 网站
                elements[4].classList.add('highlight'); // 应用
                alert('🚀 Nginx连接所有后端服务，形成完整的服务网络！\n\n点击"下一页"继续学习');
            }, 3500);
        }

        function showNginxDemo() {
            alert('🎮 Nginx性能演示：\n\n🐌 普通服务器：1000用户，5秒响应\n⚡ Nginx服务器：100000用户，0.1秒响应\n🚀 性能提升50倍！');
        }

        function animateFlow() {
            alert('🎬 数据流动：用户→前端→Nginx→后端→数据库→返回结果');
        }

        function showHttpDemo() {
            alert('📨 HTTP演示：发送请求→协议处理→服务器响应→返回结果');
        }

        function showProxyDemo() {
            alert('🚦 负载均衡：1000用户智能分配到3台服务器，避免过载！');
        }

        function showSecurityDemo() {
            alert('🛡️ 安全防护：阻断恶意攻击，加密数据传输，保护网站安全！');
        }

        function showPerformanceDemo() {
            alert('📊 性能提升：页面加载快10倍，并发能力强100倍！');
        }

        function restartPresentation() {
            showSlide(1);
        }

        function showCertificate() {
            alert('🏆 恭喜获得Nginx入门证书！您已掌握核心概念！');
        }

        // 键盘事件
        document.addEventListener('keydown', function(e) {
            switch(e.key) {
                case 'ArrowRight':
                case ' ':
                    e.preventDefault();
                    nextSlide();
                    break;
                case 'ArrowLeft':
                    e.preventDefault();
                    previousSlide();
                    break;
                case 'F11':
                    e.preventDefault();
                    toggleFullscreen();
                    break;
                case 'Escape':
                    if (document.fullscreenElement) {
                        e.preventDefault();
                        document.exitFullscreen();
                    }
                    break;
            }
        });

        // 监听全屏状态变化
        document.addEventListener('fullscreenchange', function() {
            const fullscreenBtn = document.querySelector('.nav-btn[onclick="toggleFullscreen()"]');
            const icon = fullscreenBtn.querySelector('i');

            if (document.fullscreenElement) {
                // 进入全屏
                icon.setAttribute('data-lucide', 'minimize');
            } else {
                // 退出全屏
                icon.setAttribute('data-lucide', 'maximize');
            }

            if (typeof lucide !== 'undefined') {
                lucide.createIcons();
            }
        });

        // 页面初始化
        document.addEventListener('DOMContentLoaded', function() {
            document.getElementById('total-slides').textContent = totalSlides;
            showSlide(1);

            // 初始化Lucide图标
            if (typeof lucide !== 'undefined') {
                lucide.createIcons();
            }
        });
    </script>
</body>
</html>

