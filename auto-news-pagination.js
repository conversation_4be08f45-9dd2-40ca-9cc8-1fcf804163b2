/**
 * 自动新闻分页 JavaScript
 */

jQuery(document).ready(function($) {
    
    // 初始化分页功能
    initAutoNewsPagination();
    
    function initAutoNewsPagination() {
        const wrapper = $('.auto-news-pagination-wrapper');
        if (wrapper.length === 0) return;
        
        // 绑定分页按钮事件
        bindPaginationEvents();
        
        // 绑定前往页面事件
        bindGotoPageEvents();
        
        // 添加悬停效果
        addHoverEffects();
    }
    
    function bindPaginationEvents() {
        // 分页按钮点击事件
        $(document).on('click', '.pagination-btn:not(.disabled)', function(e) {
            e.preventDefault();
            
            const page = parseInt($(this).data('page'));
            if (page && page > 0) {
                loadNewsPage(page);
            }
        });
        
        // 阻止禁用按钮的点击
        $(document).on('click', '.pagination-btn.disabled', function(e) {
            e.preventDefault();
            return false;
        });
    }
    
    function bindGotoPageEvents() {
        // 前往页面按钮
        $(document).on('click', '.goto-btn', function() {
            const input = $(this).siblings('.goto-input');
            const page = parseInt(input.val());
            const maxPage = parseInt(input.attr('max'));
            
            if (page >= 1 && page <= maxPage) {
                loadNewsPage(page);
            } else {
                alert('请输入有效的页码（1-' + maxPage + '）');
                input.focus();
            }
        });
        
        // 回车键支持
        $(document).on('keypress', '.goto-input', function(e) {
            if (e.which === 13) {
                $(this).siblings('.goto-btn').click();
            }
        });
        
        // 输入验证
        $(document).on('input', '.goto-input', function() {
            const value = parseInt($(this).val());
            const max = parseInt($(this).attr('max'));
            
            if (value < 1) {
                $(this).val(1);
            } else if (value > max) {
                $(this).val(max);
            }
        });
    }
    
    function addHoverEffects() {
        // 分页按钮悬停效果
        $(document).on('mouseenter', '.pagination-btn:not(.disabled):not(.current)', function() {
            $(this).css({
                'background': '#f0f0f0',
                'border-color': '#ccc'
            });
        }).on('mouseleave', '.pagination-btn:not(.disabled):not(.current)', function() {
            $(this).css({
                'background': 'white',
                'border-color': '#ddd'
            });
        });
        
        // 前往按钮悬停效果
        $(document).on('mouseenter', '.goto-btn', function() {
            $(this).css('background', '#005a87');
        }).on('mouseleave', '.goto-btn', function() {
            $(this).css('background', '#007cba');
        });
    }
    
    function loadNewsPage(page) {
        const wrapper = $('.auto-news-pagination-wrapper');
        const category = wrapper.data('category');
        const postsPerPage = wrapper.data('posts-per-page');
        const contentContainer = $('#news-content-container');
        const loadingIndicator = $('.loading-indicator');
        const paginationWrapper = $('.custom-pagination-wrapper');
        
        // 显示加载指示器
        loadingIndicator.show();
        contentContainer.css('opacity', '0.5');
        
        // 禁用分页按钮
        $('.pagination-btn').addClass('loading').css('pointer-events', 'none');
        
        // 发送AJAX请求
        $.ajax({
            url: autoNewsAjax.ajaxurl,
            type: 'POST',
            data: {
                action: 'load_news_page',
                page: page,
                category: category,
                posts_per_page: postsPerPage,
                nonce: autoNewsAjax.nonce
            },
            success: function(response) {
                if (response.success) {
                    // 更新内容
                    contentContainer.html(response.html);
                    
                    // 更新分页按钮
                    $('.pagination-buttons').html(response.pagination);
                    
                    // 更新总记录数
                    $('#total-posts-count').text(response.total_posts);
                    
                    // 更新前往页面输入框
                    $('.goto-input').val(response.current_page).attr('max', response.total_pages);
                    
                    // 更新全局数据
                    if (window.autoNewsPaginationData) {
                        window.autoNewsPaginationData.currentPage = response.current_page;
                        window.autoNewsPaginationData.totalPages = response.total_pages;
                        window.autoNewsPaginationData.totalPosts = response.total_posts;
                    }
                    
                    // 滚动到内容顶部
                    $('html, body').animate({
                        scrollTop: wrapper.offset().top - 100
                    }, 500);
                    
                    // 更新URL（可选）
                    updateURL(page);
                    
                } else {
                    alert('加载失败，请重试');
                }
            },
            error: function() {
                alert('网络错误，请重试');
            },
            complete: function() {
                // 隐藏加载指示器
                loadingIndicator.hide();
                contentContainer.css('opacity', '1');
                
                // 重新启用分页按钮
                $('.pagination-btn').removeClass('loading').css('pointer-events', 'auto');
            }
        });
    }
    
    function updateURL(page) {
        if (history.pushState) {
            const url = new URL(window.location);
            
            if (page === 1) {
                url.searchParams.delete('paged');
            } else {
                url.searchParams.set('paged', page);
            }
            
            history.pushState({page: page}, '', url.toString());
        }
    }
    
    // 处理浏览器前进后退
    window.addEventListener('popstate', function(event) {
        if (event.state && event.state.page) {
            loadNewsPage(event.state.page);
        } else {
            // 从URL获取页码
            const urlParams = new URLSearchParams(window.location.search);
            const page = parseInt(urlParams.get('paged')) || 1;
            if (page !== getCurrentPage()) {
                loadNewsPage(page);
            }
        }
    });
    
    function getCurrentPage() {
        return window.autoNewsPaginationData ? window.autoNewsPaginationData.currentPage : 1;
    }
    
    // 键盘导航支持
    $(document).on('keydown', function(e) {
        // 只在新闻分页区域激活时响应
        if (!$('.auto-news-pagination-wrapper').length) return;
        
        const currentPage = getCurrentPage();
        const totalPages = window.autoNewsPaginationData ? window.autoNewsPaginationData.totalPages : 1;
        
        // 左箭头键 - 上一页
        if (e.keyCode === 37 && currentPage > 1) {
            e.preventDefault();
            loadNewsPage(currentPage - 1);
        }
        // 右箭头键 - 下一页
        else if (e.keyCode === 39 && currentPage < totalPages) {
            e.preventDefault();
            loadNewsPage(currentPage + 1);
        }
        // Home键 - 第一页
        else if (e.keyCode === 36) {
            e.preventDefault();
            loadNewsPage(1);
        }
        // End键 - 最后一页
        else if (e.keyCode === 35) {
            e.preventDefault();
            loadNewsPage(totalPages);
        }
    });
    
    // 触摸设备支持
    let touchStartX = 0;
    let touchEndX = 0;
    
    $(document).on('touchstart', '.news-grid-container', function(e) {
        touchStartX = e.originalEvent.changedTouches[0].screenX;
    });
    
    $(document).on('touchend', '.news-grid-container', function(e) {
        touchEndX = e.originalEvent.changedTouches[0].screenX;
        handleSwipe();
    });
    
    function handleSwipe() {
        const swipeThreshold = 50;
        const diff = touchStartX - touchEndX;
        
        if (Math.abs(diff) > swipeThreshold) {
            const currentPage = getCurrentPage();
            const totalPages = window.autoNewsPaginationData ? window.autoNewsPaginationData.totalPages : 1;
            
            if (diff > 0 && currentPage < totalPages) {
                // 向左滑动 - 下一页
                loadNewsPage(currentPage + 1);
            } else if (diff < 0 && currentPage > 1) {
                // 向右滑动 - 上一页
                loadNewsPage(currentPage - 1);
            }
        }
    }
    
    // 自动刷新功能（可选）
    function enableAutoRefresh(intervalMinutes = 5) {
        setInterval(function() {
            const currentPage = getCurrentPage();
            loadNewsPage(currentPage);
        }, intervalMinutes * 60 * 1000);
    }
    
    // 如果需要自动刷新，取消注释下面这行
    // enableAutoRefresh(5);
    
    // 响应式处理
    $(window).on('resize', function() {
        adjustPaginationLayout();
    });
    
    function adjustPaginationLayout() {
        const wrapper = $('.custom-pagination-wrapper');
        const windowWidth = $(window).width();
        
        if (windowWidth < 768) {
            // 移动设备布局调整
            wrapper.css({
                'flex-direction': 'column',
                'gap': '10px'
            });
            
            $('.pagination-info').css('margin-right', '0');
            $('.goto-page').css('margin-left', '0');
        } else {
            // 桌面设备布局
            wrapper.css({
                'flex-direction': 'row',
                'gap': '15px'
            });
            
            $('.pagination-info').css('margin-right', '20px');
            $('.goto-page').css('margin-left', '20px');
        }
    }
    
    // 初始化时调整布局
    adjustPaginationLayout();
    
    // 预加载下一页（可选）
    function preloadNextPage() {
        const currentPage = getCurrentPage();
        const totalPages = window.autoNewsPaginationData ? window.autoNewsPaginationData.totalPages : 1;
        
        if (currentPage < totalPages) {
            const wrapper = $('.auto-news-pagination-wrapper');
            const category = wrapper.data('category');
            const postsPerPage = wrapper.data('posts-per-page');
            
            $.ajax({
                url: autoNewsAjax.ajaxurl,
                type: 'POST',
                data: {
                    action: 'load_news_page',
                    page: currentPage + 1,
                    category: category,
                    posts_per_page: postsPerPage,
                    nonce: autoNewsAjax.nonce
                },
                success: function(response) {
                    // 缓存结果（可以存储在sessionStorage中）
                    if (response.success && window.sessionStorage) {
                        const cacheKey = 'news_page_' + (currentPage + 1) + '_' + category;
                        sessionStorage.setItem(cacheKey, JSON.stringify(response));
                    }
                }
            });
        }
    }
    
    // 如果需要预加载，取消注释下面这行
    // setTimeout(preloadNextPage, 2000);
});
