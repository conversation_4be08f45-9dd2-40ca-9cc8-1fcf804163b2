/**
 * 自动新闻分页样式
 */

/* 主容器 */
.auto-news-pagination-wrapper {
    width: 100%;
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

/* 新闻网格 */
.aobailei-news-blog.custom-news-grid {
    margin-bottom: 40px;
}

.news-grid-container {
    display: grid;
    gap: 30px;
    transition: opacity 0.3s ease;
}

/* 根据列数调整网格 */
.custom-news-grid[data-columns="1"] .news-grid-container {
    grid-template-columns: 1fr;
}

.custom-news-grid[data-columns="2"] .news-grid-container {
    grid-template-columns: repeat(2, 1fr);
}

.custom-news-grid[data-columns="3"] .news-grid-container {
    grid-template-columns: repeat(3, 1fr);
}

.custom-news-grid[data-columns="4"] .news-grid-container {
    grid-template-columns: repeat(4, 1fr);
}

/* 新闻项目 */
.news-item {
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    height: 100%;
    display: flex;
    flex-direction: column;
}

.news-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.15);
}

/* 新闻缩略图 */
.news-thumbnail {
    position: relative;
    overflow: hidden;
    height: 200px;
}

.news-thumbnail img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.news-thumbnail:hover img {
    transform: scale(1.05);
}

.news-thumbnail a {
    display: block;
    height: 100%;
}

/* 新闻内容 */
.news-content {
    padding: 20px;
    flex: 1;
    display: flex;
    flex-direction: column;
}

.news-title {
    margin: 0 0 10px 0;
    font-size: 18px;
    font-weight: 600;
    line-height: 1.4;
}

.news-title a {
    color: #333;
    text-decoration: none;
    transition: color 0.3s ease;
}

.news-title a:hover {
    color: #007cba;
}

/* 新闻元信息 */
.news-meta {
    margin-bottom: 15px;
    font-size: 14px;
    color: #666;
    display: flex;
    gap: 15px;
    flex-wrap: wrap;
}

.news-date,
.news-author {
    display: flex;
    align-items: center;
}

.news-date:before {
    content: "📅";
    margin-right: 5px;
}

.news-author:before {
    content: "👤";
    margin-right: 5px;
}

/* 新闻摘要 */
.news-excerpt {
    margin-bottom: 15px;
    color: #555;
    line-height: 1.6;
    flex: 1;
}

/* 阅读更多按钮 */
.news-read-more {
    margin-top: auto;
}

.read-more-btn {
    display: inline-block;
    padding: 8px 16px;
    background: #007cba;
    color: white;
    text-decoration: none;
    border-radius: 4px;
    font-size: 14px;
    transition: background 0.3s ease;
}

.read-more-btn:hover {
    background: #005a87;
    color: white;
}

/* 无新闻提示 */
.no-news-found {
    text-align: center;
    padding: 60px 20px;
    color: #666;
    font-size: 16px;
    grid-column: 1 / -1;
}

/* 分页容器 */
.custom-pagination-wrapper {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 15px;
    margin: 40px 0;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 8px;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

/* 分页信息 */
.pagination-info {
    color: #666;
    font-size: 14px;
    margin-right: 20px;
    font-weight: 500;
}

#total-posts-count {
    font-weight: 600;
    color: #007cba;
}

/* 分页按钮容器 */
.pagination-buttons {
    display: flex;
    align-items: center;
    gap: 5px;
}

/* 分页按钮 */
.pagination-btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    min-width: 32px;
    height: 32px;
    padding: 0 8px;
    border: 1px solid #ddd;
    background: white;
    color: #333;
    text-decoration: none;
    border-radius: 4px;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.2s ease;
    user-select: none;
}

.pagination-btn:hover:not(.disabled):not(.current) {
    background: #f0f0f0;
    border-color: #ccc;
    transform: translateY(-1px);
}

.pagination-btn.current {
    border-color: #007cba;
    background: #007cba;
    color: white;
    font-weight: 600;
}

.pagination-btn.disabled {
    background: #f5f5f5;
    color: #ccc;
    cursor: not-allowed;
    border-color: #e0e0e0;
}

.pagination-btn.loading {
    opacity: 0.6;
    cursor: wait;
}

/* 前往页面 */
.goto-page {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-left: 20px;
}

.goto-page label {
    color: #666;
    font-size: 14px;
    font-weight: 500;
}

.goto-input {
    width: 50px;
    height: 32px;
    padding: 0 8px;
    border: 1px solid #ddd;
    border-radius: 4px;
    text-align: center;
    font-size: 14px;
    transition: border-color 0.2s ease;
}

.goto-input:focus {
    outline: none;
    border-color: #007cba;
    box-shadow: 0 0 0 2px rgba(0, 124, 186, 0.2);
}

.goto-btn {
    height: 32px;
    padding: 0 12px;
    background: #007cba;
    color: white;
    border: none;
    border-radius: 4px;
    font-size: 14px;
    cursor: pointer;
    transition: background 0.2s ease;
    font-weight: 500;
}

.goto-btn:hover {
    background: #005a87;
}

.goto-btn:active {
    transform: translateY(1px);
}

/* 加载指示器 */
.loading-indicator {
    text-align: center;
    padding: 20px;
    color: #666;
    font-size: 16px;
}

.loading-indicator:before {
    content: "";
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #007cba;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-right: 10px;
    vertical-align: middle;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 响应式设计 */
@media (max-width: 1024px) {
    .custom-news-grid[data-columns="4"] .news-grid-container {
        grid-template-columns: repeat(3, 1fr);
    }
}

@media (max-width: 768px) {
    .auto-news-pagination-wrapper {
        padding: 15px;
    }
    
    .custom-news-grid[data-columns="3"] .news-grid-container,
    .custom-news-grid[data-columns="4"] .news-grid-container {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .news-grid-container {
        gap: 20px;
    }
    
    .custom-pagination-wrapper {
        flex-direction: column;
        gap: 10px;
        padding: 15px;
    }
    
    .pagination-info {
        margin-right: 0;
        text-align: center;
    }
    
    .goto-page {
        margin-left: 0;
        justify-content: center;
    }
    
    .pagination-buttons {
        flex-wrap: wrap;
        justify-content: center;
    }
}

@media (max-width: 480px) {
    .custom-news-grid[data-columns="2"] .news-grid-container,
    .custom-news-grid[data-columns="3"] .news-grid-container,
    .custom-news-grid[data-columns="4"] .news-grid-container {
        grid-template-columns: 1fr;
    }
    
    .news-item {
        margin-bottom: 20px;
    }
    
    .news-thumbnail {
        height: 180px;
    }
    
    .news-content {
        padding: 15px;
    }
    
    .news-title {
        font-size: 16px;
    }
    
    .pagination-btn {
        min-width: 28px;
        height: 28px;
        font-size: 13px;
    }
    
    .goto-input {
        width: 45px;
        height: 28px;
        font-size: 13px;
    }
    
    .goto-btn {
        height: 28px;
        padding: 0 10px;
        font-size: 13px;
    }
}

/* 打印样式 */
@media print {
    .custom-pagination-wrapper,
    .loading-indicator {
        display: none;
    }
    
    .news-item {
        break-inside: avoid;
        box-shadow: none;
        border: 1px solid #ddd;
    }
    
    .news-item:hover {
        transform: none;
    }
    
    .read-more-btn {
        display: none;
    }
}

/* 高对比度模式支持 */
@media (prefers-contrast: high) {
    .news-item {
        border: 2px solid #000;
    }
    
    .pagination-btn {
        border: 2px solid #000;
    }
    
    .pagination-btn.current {
        background: #000;
        border-color: #000;
    }
}

/* 减少动画模式支持 */
@media (prefers-reduced-motion: reduce) {
    .news-item,
    .news-thumbnail img,
    .pagination-btn,
    .read-more-btn,
    .goto-btn {
        transition: none;
    }
    
    .news-item:hover {
        transform: none;
    }
    
    .news-thumbnail:hover img {
        transform: none;
    }
    
    .loading-indicator:before {
        animation: none;
    }
}
