# Nginx 配置说明

！！！！修改前先扫描整个nginx所有配置文件，保证配置简洁易维护

！！！！使用sudo nginx -t 验证配置

nginx配置路径/etc/nginx

本仓库包含了一套完整的Nginx配置，主要用于托管WordPress网站。配置专注于性能优化、安全加固和模块化管理。


## 目录结构

- `nginx.conf` - 主配置文件 ✅
- `readip.conf` - 真实IP识别配置 ✅
- `sites-available/` - 可用站点配置 ✅
- `sites-enabled/` - 已启用站点配置 ✅
- `rules/` - 模块化规则文件 ✅
  - **核心规则文件（推荐使用）：**
  - `security.conf` - 综合安全规则（包含SQL注入防护、基础安全规则、敏感文件保护和路径遍历防护）✅
  - `wordpress.conf` - 综合WordPress规则（包含WP安全、PHP处理、多站点支持、wp-admin控制和静态文件缓存）✅
  - `php_combined.conf` - 综合PHP处理与安全规则（合并了php_handling.conf和php_security.conf）✅
  - `whitelists.conf` - 统一IP白名单管理 ✅
  - `ip_list.conf` - 中央IP列表文件 ✅
- `snippets/` - 配置片段 ✅
- `conf.d/` - 额外的配置文件 ✅
- `modules-available/` - 可用模块 ✅
- `modules-enabled/` - 已启用模块 ✅

## 主要功能

### 基础设置

- 工作进程自动调整 (`worker_processes auto`) ✅
- 错误日志配置 (`error_log /var/log/nginx/error.log`) ✅
- 连接处理优化 (`worker_connections 2048`) ✅
- 隐藏服务器版本信息 (`server_tokens off`) ✅
- MIME类型支持（包括字体文件） ✅
- 指定IP和端口绑定 (`listen ************:80`) ✅
- HTTPS支持 (`listen 443 ssl`) ✅
- 工作进程优化 (`worker_rlimit_nofile 20000`, `worker_cpu_affinity auto`) ✅
- 使用epoll和多连接接受 (`use epoll`, `multi_accept on`) ✅

### 性能优化

- 启用sendfile优化 (`sendfile on`) ✅
- TCP优化 (`tcp_nopush on`, `tcp_nodelay on`) ✅
- Gzip压缩 ✅
  - 支持多种文件类型压缩（包括字体文件） ✅
  - 压缩级别设置为6 ✅
  - 启用压缩缓存 ✅
  - 最小压缩大小设置 ✅
- 静态文件缓存 ✅
  - 最大缓存期限设置 ✅
  - 适用于JS、CSS、图片和字体文件 ✅
  - 添加Cache-Control头 ✅
- SSL会话缓存 ✅
  - 会话超时设置 (`ssl_session_timeout 10m`) ✅
  - 共享会话缓存 (`ssl_session_cache shared:SSL:10m`) ✅
- FastCGI优化 ✅
  - 增加缓冲区大小 (`fastcgi_buffer_size 16k`) ✅
  - 调整缓冲区数量和大小 (`fastcgi_buffers 16 16k`) ✅
  - 增加超时时间 (`fastcgi_read_timeout 600`) ✅
- WebP图片支持 ✅
  - 根据客户端Accept头提供WebP版本图片 ✅
  - 添加Vary头确保正确缓存 ✅
  - WebP Express插件支持，按需生成WebP图像 ✅
  - WebP图像转换路径保护 ✅

### 安全增强

- 真实IP识别 ✅
  - 识别来自代理和CDN的真实客户端IP ✅
  - 支持X-Forwarded-For头解析 ✅
- SSL/TLS设置 ✅
  - 仅支持TLSv1.2和TLSv1.3 ✅
  - 优先使用服务器密码 ✅
  - 配置安全密码套件 ✅
  - HSTS头部设置 ✅
  - 证书和私钥配置 ✅
  - 私钥文件权限控制 ✅
- 请求限制 - 集中管理于`rate_limiting.conf` ✅
  - 一般请求限流（每秒5次请求） ✅
  - 静态资源不限流 ✅
  - WordPress登录页面特别限流（每秒2次请求） ✅
  - 搜索请求专用限流（每秒1次请求） ✅
  - 基于IP的灵活限流 ✅
  - 下载大文件限速（512KB/s） ✅
  - 自定义429错误页面 ✅
  - IP白名单配置（绕过限流） ✅
  - 连接数限制配置 ✅
  - 客户端请求大小限制 ✅
  - 客户端超时设置 ✅
- SQL注入防护 ✅
  - 过滤包含SQL关键字的请求 ✅
  - 检测并阻止特殊字符攻击 ✅
  - 防止路径遍历和XSS攻击 ✅
  - 阻止恶意爬虫和自动化工具 ✅
  - 返回特定状态码以便于监控和分析 ✅
- 根路径访问控制 ✅
  - 使用limit_except限制HTTP请求方法 ✅
  - 可配置的IP访问控制（allow/deny规则） ✅
- 防止WordPress常见攻击 ✅
  - 禁用xmlrpc.php访问 ✅
  - wp-login.php访问限制 ✅
  - wp-admin访问保护 ✅
  - WordPress配置文件保护 ✅
  - 防止PHP文件在上传目录执行 ✅
  - 保护REST API防止用户信息泄露 ✅
  - 拦截通过index.php访问用户信息API的请求 ✅
  - 基于主机名的条件认证 ✅
  - 按域名豁免认证需求 ✅
  - 特定插件API访问规则 ✅
- 安全响应头 ✅
  - X-Frame-Options ✅
  - X-Content-Type-Options ✅
  - X-XSS-Protection ✅
  - Referrer-Policy ✅
- 路径遍历防护 ✅
- 请求方法限制 (仅允许GET、POST和HEAD) ✅
- 敏感文件访问限制 ✅
  - 归档和版本控制文件 (.svn, .git, .sql, .bak, .old, .tar, .gz, .tgz, .zip等) ✅
  - 配置和系统文件 (.csv, .inc, .config, .conf, .sh, .swp, .bash_rc, .tmp等) ✅
- HTTP自动跳转到HTTPS ✅
- 客户端请求验证 ✅
  - 空User-Agent检测 ✅
  - 空Referer检测 ✅
  - HTTP协议版本检测 ✅
- 防止恶意刷流量 ✅
  - 大文件下载IP白名单控制 ✅
  - 下载速率限制 ✅
  - 下载请求独立日志记录 ✅
- 全局IP白名单控制 ✅
  - 独立的IP白名单配置文件 ✅
  - 白名单IP豁免所有请求限制 ✅
  - 白名单IP不受下载限制 ✅
  - 灵活可扩展的设计 ✅

### 日志配置

- 访问日志配置 ✅
  - 标准日志格式 (包含IP、用户、时间、请求、状态码等) ✅
  - 按站点分离日志文件 ✅
  - 静态资源和敏感路径的日志优化 ✅

### WordPress优化

- 固定链接规则配置 ✅
- PHP文件处理优化 ✅
- FastCGI配置优化 ✅
  - 增加超时时间 ✅
  - 防止PHP路径解析漏洞 ✅
  - 优化缓冲区设置 ✅
- WordPress多站点支持 ✅
- REST API访问控制 ✅
  - 防止用户账户信息泄露 ✅
  - 保持必要API功能正常使用 ✅
- 搜索功能优化 ✅
  - 搜索请求重写和限流 ✅
  - 专用搜索路径处理 ✅
- 特定插件支持 ✅
  - LiteSpeed缓存插件集成 ✅
  - WebP Express插件集成 ✅
  - Hero Menu插件支持 ✅
  - Tool-Hub插件支持 ✅

### 模块化配置

- 将常用规则提取为独立文件，便于复用：✅
  - **核心规则文件（推荐使用）：**
  - `security.conf` - 综合安全规则（包含SQL注入防护和基础安全规则）✅
  - `wordpress.conf` - 综合WordPress规则（包含WP安全、PHP处理和wp-admin控制）✅
  - `https.conf` - 综合HTTPS配置（包含TLS安全参数，为Certbot准备）✅
  - `php_combined.conf` - 综合PHP处理规则（包含安全、性能和防漏洞措施）✅
  - `rate_limiting.conf` - 集中管理所有限流规则（按功能分类整理）✅
  - `allinone.conf` - 集成型配置（适用于简单非WordPress网站）✅

## 使用方法

1. 将特定站点配置文件放入`sites-available/`目录
2. 使用模板创建新的站点配置
   - 使用`wordpress_template.conf`作为HTTP的WordPress站点模板
   - 使用`wordpress_ssl_template.conf`作为HTTPS的WordPress站点模板
   - 使用`sites-available/coolnetpower.conf`作为简单站点模板（使用allinone.conf规则）
3. 对于HTTPS站点，推荐使用Certbot自动配置SSL：
   ```
   sudo certbot --nginx -d example.com -d www.example.com
   ```
4. 通过符号链接启用站点：`ln -s /etc/nginx/sites-available/your-site.conf /etc/nginx/sites-enabled/`
5. 测试配置：`nginx -t`
6. 重新加载Nginx：`systemctl reload nginx`

## 安全注意事项

- 确保在生产环境中更新SSL/TLS设置
- 根据实际需要调整请求限制参数
- 为WordPress管理界面配置IP白名单
- 定期检查和更新配置以应对新的安全威胁
- 配置显式的IP绑定，避免使用默认的0.0.0.0绑定
- 根据需要配置根路径的访问控制，限制可访问的IP
- 证书私钥文件应为640权限，仅允许web服务器用户(www-data)读取
- 防止WordPress REST API暴露用户信息，限制敏感API的访问
- 检查SQL注入防护规则可能对正常功能的影响，根据需要调整
- 更新真实IP配置，确保包含您使用的CDN和代理服务提供商的IP范围

## SSL/TLS配置指南

- 推荐使用Certbot自动配置与续期Let's Encrypt证书
  - 配置已优化支持Certbot自动操作
  - 验证路径`/.well-known/acme-challenge/`已正确配置
  - 证书文件将自动放置在`/etc/letsencrypt/live/域名/`目录
- 仅使用TLSv1.2和TLSv1.3协议，禁用旧版不安全协议
- 使用强密码套件，优先选择服务器密码
- 配置适当的SSL会话缓存以提高性能
- 实施HSTS，确保客户端总是使用HTTPS
- 自动续期证书：Certbot会创建定时任务，无需手动管理
- 可选配置OCSP装订以提高性能和隐私

## Certbot集成

- 所有配置文件都已优化，以支持Certbot自动配置
- 站点模板包含必要的验证路径配置
- `https.conf`已精简，只保留TLS安全参数
- 证书路径配置将由Certbot自动处理
- 安装Certbot：
  ```
  sudo apt update
  sudo apt install certbot python3-certbot-nginx
  ```
- 获取证书并自动配置Nginx：
  ```
  sudo certbot --nginx -d example.com -d www.example.com
  ```
- 验证自动续期：
  ```
  sudo certbot renew --dry-run
  ```

## 限流配置指南

- 所有限流规则集中在`rate_limiting.conf`
- 按功能分类组织，便于管理：
  - 通用限流规则 - 适用于大多数请求
  - 静态资源限流规则 - 对静态资源应用更宽松的限制 
  - WordPress特定规则 - 登录页面和搜索请求的专用保护
  - 大文件下载限流规则 - 防止带宽滥用
- 根据流量调整限流参数：
  - 一般流量：`limit_req zone=general burst=10 nodelay;`
  - 登录保护：`limit_req zone=wordpress burst=1 nodelay;`
  - 搜索保护：`limit_req zone=search_limit burst=1 nodelay;`
- 自定义429错误页面提供友好用户体验
- IP白名单可豁免限流规则
- 静态资源自动豁免严格限流

## 性能调优建议

- 根据服务器资源调整worker_processes和worker_connections
- 优化gzip压缩设置以平衡CPU使用和带宽节省
- 监控缓存效果并调整静态资源缓存策略
- 调整SSL会话缓存大小以匹配站点流量
- 使用HTTP/2以提高HTTPS连接性能
- 根据流量和请求模式调整限流参数
- 分析访问日志，确定是否需要针对特定URL路径设置不同的限流规则
- 根据安全和性能需求调整SQL注入防护规则
- 为大文件上传和处理调整client_max_body_size和超时设置
- 启用WebP图片支持以减少带宽使用和提高加载速度

## 规则文件

### 核心规则文件

- `security.conf` - 综合安全规则，包含SQL注入防护、路径遍历防护和敏感文件访问控制
- `wordpress.conf` - WordPress特定规则，包含wp-admin控制、固定链接、PHP处理和安全配置
- `https.conf` - HTTPS/SSL/TLS配置参数

### 辅助规则文件

- `readip.conf` - 真实客户端IP识别
- `rate_limiting.conf` - 请求限流规则参考
- `whitelists.conf` - 统一IP白名单管理
- `php_combined.conf` - PHP处理和安全配置

### 其他配置文件

- `.htpasswd` - HTTP基本认证用户凭据

## 白名单管理

系统采用统一的IP列表管理模式，所有IP白名单从单一来源获取：

1. **中央IP列表文件** (`ip_list.conf`) - 包含所有允许的IP地址，只需修改此文件即可更新所有白名单
2. **白名单变量** (`whitelists.conf`) - 定义了5种不同用途的白名单：
   - **内部网络白名单** (`$internal_whitelist`) - 用于常规访问控制
   - **管理员白名单** (`$admin_whitelist`) - 用于WordPress后台访问
   - **下载白名单** (`$download_whitelist`) - 控制大文件下载权限
   - **API访问白名单** (`$api_whitelist`) - 允许访问REST API
   - **限流豁免白名单** (`$ratelimit_whitelist`) - 不受限流规则影响

### IP列表管理方法

1. 要添加或删除白名单IP，只需编辑`ip_list.conf`文件：
   ```
   # 添加新IP
   *******       1;  # 新办公室IP
   
   # 删除IP (注释掉或删除相应行)
   # *******     1;  # 旧办公室IP
   ```

2. 所有变更将自动应用到所有白名单，无需修改多处配置。

3. 在Nginx配置中使用白名单：
   ```
   # 仅允许管理员白名单访问
   if ($admin_whitelist = 0) {
       return 403;
   }
   
   # 限制非白名单IP的请求率
   if ($ratelimit_whitelist = 0) {
       limit_req zone=perip burst=5 nodelay;
   }
   ```

## 限流配置

系统配置了多层限流策略，保护不同资源：

1. **WordPress登录保护** - 每秒2请求，爆发上限1
2. **搜索功能保护** - 每秒1请求，爆发上限1
3. **一般请求** - 每秒5请求，爆发上限10
4. **静态资源** - 宽松限流，爆发上限1000 