<?php
/**
 * 直接修改现有分页系统 - 自动获取已发布新闻
 * 将此代码添加到你的主题的 functions.php 文件中
 */

// 添加AJAX处理函数
add_action('wp_ajax_get_published_news', 'handle_get_published_news');
add_action('wp_ajax_nopriv_get_published_news', 'handle_get_published_news');

function handle_get_published_news() {
    // 验证nonce（如果有的话）
    // if (!wp_verify_nonce($_POST['nonce'], 'news_nonce')) {
    //     wp_die('安全验证失败');
    // }
    
    $page = intval($_POST['page']) ?: 1;
    $posts_per_page = intval($_POST['posts_per_page']) ?: 6;
    $category = sanitize_text_field($_POST['category']) ?: 'news';
    
    // 查询已发布的新闻
    $args = array(
        'post_type' => 'post',
        'post_status' => 'publish',
        'posts_per_page' => $posts_per_page,
        'paged' => $page,
        'orderby' => 'date',
        'order' => 'DESC'
    );
    
    // 如果指定了分类
    if ($category && $category !== 'all') {
        if (is_numeric($category)) {
            $args['cat'] = intval($category);
        } else {
            $args['category_name'] = $category;
        }
    }
    
    $query = new WP_Query($args);
    
    $posts = array();
    if ($query->have_posts()) {
        while ($query->have_posts()) {
            $query->the_post();
            $posts[] = array(
                'id' => get_the_ID(),
                'title' => get_the_title(),
                'permalink' => get_permalink(),
                'excerpt' => get_the_excerpt(),
                'date' => get_the_date(),
                'author' => get_the_author(),
                'thumbnail' => get_the_post_thumbnail(get_the_ID(), 'medium')
            );
        }
    }
    
    wp_reset_postdata();
    
    $response = array(
        'success' => true,
        'data' => array(
            'posts' => $posts,
            'current_page' => $page,
            'total_pages' => $query->max_num_pages,
            'total_posts' => $query->found_posts
        )
    );
    
    wp_send_json($response);
}

// 修改现有的分页JavaScript
add_action('wp_footer', 'add_enhanced_pagination_script');
function add_enhanced_pagination_script() {
    ?>
    <script>
    jQuery(document).ready(function($) {
        // 等待页面加载完成
        setTimeout(function() {
            enhancePagination();
        }, 1000);
        
        function enhancePagination() {
            const newsContainer = $('.aobailei-news-blog');
            const paginationContainer = $('.aobailei-news-blog .pagination, .fusion-blog-pagination, .page-numbers');
            
            if (newsContainer.length > 0) {
                // 隐藏原有分页
                paginationContainer.hide();
                
                // 获取当前页面并加载数据
                const currentPage = getCurrentPage();
                loadRealNewsData(currentPage);
            }
        }
        
        function getCurrentPage() {
            const urlParams = new URLSearchParams(window.location.search);
            return parseInt(urlParams.get('paged')) || 1;
        }
        
        function loadRealNewsData(page) {
            const newsContainer = $('.aobailei-news-blog');
            
            // 显示加载指示器
            showLoadingIndicator();
            
            $.ajax({
                url: '<?php echo admin_url('admin-ajax.php'); ?>',
                type: 'POST',
                data: {
                    action: 'get_published_news',
                    page: page,
                    posts_per_page: 6,
                    category: 'news'
                },
                success: function(response) {
                    if (response.success && response.data.posts) {
                        // 更新新闻内容
                        updateNewsContent(response.data.posts);
                        
                        // 创建真实分页
                        createRealPagination(page, response.data.total_pages, response.data.total_posts);
                    } else {
                        // 如果失败，创建静态分页
                        createStaticPagination();
                    }
                },
                error: function() {
                    createStaticPagination();
                },
                complete: function() {
                    hideLoadingIndicator();
                }
            });
        }
        
        function updateNewsContent(posts) {
            const newsContainer = $('.aobailei-news-blog');
            let html = '';
            
            if (posts && posts.length > 0) {
                // 创建网格容器（如果不存在）
                if (!newsContainer.find('.news-grid-container').length) {
                    newsContainer.html('<div class="news-grid-container"></div>');
                }
                
                posts.forEach(function(post) {
                    html += createNewsItemHTML(post);
                });
                
                newsContainer.find('.news-grid-container').html(html);
            } else {
                newsContainer.html('<p style="text-align: center; padding: 40px; color: #666;">暂无新闻文章</p>');
            }
        }
        
        function createNewsItemHTML(post) {
            return `
                <div class="news-item" style="background: white; border-radius: 8px; overflow: hidden; box-shadow: 0 2px 10px rgba(0,0,0,0.1); transition: transform 0.3s ease;">
                    ${post.thumbnail ? `
                        <div class="news-thumbnail" style="height: 200px; overflow: hidden;">
                            <a href="${post.permalink}">
                                ${post.thumbnail}
                            </a>
                        </div>
                    ` : ''}
                    
                    <div class="news-content" style="padding: 20px;">
                        <h3 class="news-title" style="margin: 0 0 10px 0; font-size: 18px;">
                            <a href="${post.permalink}" style="color: #333; text-decoration: none;">${post.title}</a>
                        </h3>
                        
                        <div class="news-meta" style="margin-bottom: 15px; font-size: 14px; color: #666;">
                            <span class="news-date">${post.date}</span>
                            ${post.author ? ` | <span class="news-author">作者: ${post.author}</span>` : ''}
                        </div>
                        
                        ${post.excerpt ? `
                            <div class="news-excerpt" style="margin-bottom: 15px; color: #555; line-height: 1.6;">
                                ${post.excerpt}
                            </div>
                        ` : ''}
                        
                        <div class="news-read-more">
                            <a href="${post.permalink}" style="display: inline-block; padding: 8px 16px; background: #007cba; color: white; text-decoration: none; border-radius: 4px; font-size: 14px;">阅读更多</a>
                        </div>
                    </div>
                </div>
            `;
        }
        
        function createRealPagination(currentPage, totalPages, totalPosts) {
            const newsContainer = $('.aobailei-news-blog');
            
            // 移除现有的自定义分页
            $('.custom-pagination-wrapper').remove();
            
            if (totalPages > 1) {
                const paginationHTML = createPaginationHTML(currentPage, totalPages, totalPosts);
                newsContainer.after(paginationHTML);
                
                // 绑定分页事件
                bindPaginationEvents();
            }
        }
        
        function createStaticPagination() {
            const currentPage = getCurrentPage();
            const totalPosts = 180; // 默认值
            const totalPages = Math.ceil(totalPosts / 6);
            
            createRealPagination(currentPage, totalPages, totalPosts);
        }
        
        function createPaginationHTML(currentPage, totalPages, totalPosts) {
            let html = `
                <div class="custom-pagination-wrapper" style="
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    gap: 15px;
                    margin: 40px 0;
                    padding: 20px;
                    background: #f8f9fa;
                    border-radius: 8px;
                    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
                ">
                    <div class="pagination-info" style="color: #666; font-size: 14px; margin-right: 20px;">
                        总共 <span class="total-posts-count">${totalPosts}</span> 条记录
                    </div>
                    
                    <div class="pagination-buttons" style="display: flex; align-items: center; gap: 5px;">
            `;
            
            // 上一页按钮
            const prevDisabled = currentPage <= 1;
            html += `
                <a href="#" data-page="${Math.max(1, currentPage - 1)}" 
                   class="pagination-btn prev-btn ${prevDisabled ? 'disabled' : ''}" 
                   style="display: inline-flex; align-items: center; justify-content: center; min-width: 32px; height: 32px; padding: 0 8px; border: 1px solid #ddd; background: ${prevDisabled ? '#f5f5f5' : 'white'}; color: ${prevDisabled ? '#ccc' : '#333'}; text-decoration: none; border-radius: 4px; font-size: 14px; cursor: ${prevDisabled ? 'not-allowed' : 'pointer'};">‹</a>
            `;
            
            // 页码按钮
            const pageNumbers = getVisiblePageNumbers(currentPage, totalPages);
            pageNumbers.forEach(page => {
                if (page === '...') {
                    html += `<span style="padding: 0 8px; color: #999; font-size: 14px;">...</span>`;
                } else {
                    const isCurrent = page === currentPage;
                    html += `
                        <a href="#" data-page="${page}" 
                           class="pagination-btn ${isCurrent ? 'current' : ''}" 
                           style="display: inline-flex; align-items: center; justify-content: center; min-width: 32px; height: 32px; padding: 0 8px; border: 1px solid ${isCurrent ? '#007cba' : '#ddd'}; background: ${isCurrent ? '#007cba' : 'white'}; color: ${isCurrent ? 'white' : '#333'}; text-decoration: none; border-radius: 4px; font-size: 14px; cursor: pointer;">${page}</a>
                    `;
                }
            });
            
            // 下一页按钮
            const nextDisabled = currentPage >= totalPages;
            html += `
                <a href="#" data-page="${Math.min(totalPages, currentPage + 1)}" 
                   class="pagination-btn next-btn ${nextDisabled ? 'disabled' : ''}" 
                   style="display: inline-flex; align-items: center; justify-content: center; min-width: 32px; height: 32px; padding: 0 8px; border: 1px solid #ddd; background: ${nextDisabled ? '#f5f5f5' : 'white'}; color: ${nextDisabled ? '#ccc' : '#333'}; text-decoration: none; border-radius: 4px; font-size: 14px; cursor: ${nextDisabled ? 'not-allowed' : 'pointer'};">›</a>
            `;
            
            html += `
                    </div>
                    
                    <div class="goto-page" style="display: flex; align-items: center; gap: 8px; margin-left: 20px;">
                        <label style="color: #666; font-size: 14px;">前往</label>
                        <input type="number" min="1" max="${totalPages}" value="${currentPage}" 
                               class="goto-input" style="width: 50px; height: 32px; padding: 0 8px; border: 1px solid #ddd; border-radius: 4px; text-align: center; font-size: 14px;">
                        <label style="color: #666; font-size: 14px;">页</label>
                        <button class="goto-btn" style="height: 32px; padding: 0 12px; background: #007cba; color: white; border: none; border-radius: 4px; font-size: 14px; cursor: pointer;">确定</button>
                    </div>
                </div>
            `;
            
            return html;
        }
        
        function getVisiblePageNumbers(currentPage, totalPages) {
            const pages = [];
            const maxVisible = 7;
            const half = Math.floor(maxVisible / 2);
            
            let start = Math.max(1, currentPage - half);
            let end = Math.min(totalPages, currentPage + half);
            
            // 调整范围
            if (end - start + 1 < maxVisible) {
                if (start === 1) {
                    end = Math.min(totalPages, start + maxVisible - 1);
                } else {
                    start = Math.max(1, end - maxVisible + 1);
                }
            }
            
            // 添加第一页和省略号
            if (start > 1) {
                pages.push(1);
                if (start > 2) {
                    pages.push('...');
                }
            }
            
            // 添加中间页码
            for (let i = start; i <= end; i++) {
                pages.push(i);
            }
            
            // 添加省略号和最后一页
            if (end < totalPages) {
                if (end < totalPages - 1) {
                    pages.push('...');
                }
                pages.push(totalPages);
            }
            
            return pages;
        }
        
        function bindPaginationEvents() {
            // 分页按钮点击事件
            $(document).on('click', '.pagination-btn:not(.disabled)', function(e) {
                e.preventDefault();
                const page = parseInt($(this).data('page'));
                if (page && page > 0) {
                    loadRealNewsData(page);
                    updateURL(page);
                }
            });
            
            // 前往页面按钮
            $(document).on('click', '.goto-btn', function() {
                const input = $(this).siblings('.goto-input');
                const page = parseInt(input.val());
                const maxPage = parseInt(input.attr('max'));
                
                if (page >= 1 && page <= maxPage) {
                    loadRealNewsData(page);
                    updateURL(page);
                } else {
                    alert('请输入有效的页码（1-' + maxPage + '）');
                }
            });
            
            // 回车键支持
            $(document).on('keypress', '.goto-input', function(e) {
                if (e.which === 13) {
                    $(this).siblings('.goto-btn').click();
                }
            });
        }
        
        function updateURL(page) {
            if (history.pushState) {
                const url = new URL(window.location);
                if (page === 1) {
                    url.searchParams.delete('paged');
                } else {
                    url.searchParams.set('paged', page);
                }
                history.pushState({page: page}, '', url.toString());
            }
        }
        
        function showLoadingIndicator() {
            const newsContainer = $('.aobailei-news-blog');
            if (!$('.loading-indicator').length) {
                newsContainer.append('<div class="loading-indicator" style="text-align: center; padding: 20px; color: #666;">正在加载...</div>');
            }
            $('.loading-indicator').show();
        }
        
        function hideLoadingIndicator() {
            $('.loading-indicator').hide();
        }
    });
    </script>
    <?php
}
?>
