# Nginx 基础配置示例
# 这个文件展示了一个简单网站的配置

# 服务器配置块
server {
    # 监听端口 - 就像餐厅的门牌号
    listen 80;
    
    # 服务器名称 - 网站的域名
    server_name example.com www.example.com;
    
    # 网站根目录 - 文件存放的地方
    root /var/www/html;
    
    # 默认首页文件
    index index.html index.htm;
    
    # 访问日志 - 记录谁来过网站
    access_log /var/log/nginx/example.access.log;
    
    # 错误日志 - 记录出现的问题
    error_log /var/log/nginx/example.error.log;
    
    # 根目录的处理规则
    location / {
        # 尝试按顺序查找文件
        try_files $uri $uri/ =404;
    }
    
    # 静态文件处理（图片、CSS、JS等）
    location ~* \.(jpg|jpeg|png|gif|ico|css|js)$ {
        # 设置缓存时间 - 让浏览器记住这些文件
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
    
    # 禁止访问隐藏文件
    location ~ /\. {
        deny all;
    }
}

# HTTPS 配置示例（需要SSL证书）
server {
    listen 443 ssl;
    server_name example.com www.example.com;
    
    # SSL 证书配置
    ssl_certificate /path/to/certificate.crt;
    ssl_certificate_key /path/to/private.key;
    
    # SSL 安全设置
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers HIGH:!aNULL:!MD5;
    
    root /var/www/html;
    index index.html index.htm;
    
    location / {
        try_files $uri $uri/ =404;
    }
}

# HTTP 自动跳转到 HTTPS
server {
    listen 80;
    server_name example.com www.example.com;
    return 301 https://$server_name$request_uri;
}
