# Audit Trail 用户管理与权限控制功能设计

## 功能概述

为Audit Trail插件添加高级用户管理与权限控制功能，使管理员能够:
1. 一键添加用户并指定角色
2. 为用户分配管理员权限但限制特定功能访问
3. 精细控制用户对WordPress功能和资源的访问权限

该功能设计为轻量级实现，低资源占用，同时提供类似Advanced Access Manager插件的核心权限控制能力。

## 功能设计

### 1. 用户管理界面

在Audit Trail插件中添加新的"用户管理"选项卡，包含以下功能：

#### 1.1 一键添加用户
- 简洁的表单界面，包含以下字段:
  - 用户名（必填）
  - 电子邮件（必填）
  - 密码（可选，若为空则自动生成）
  - 角色选择（下拉菜单）
  - 权限模板（预设的权限配置）
- 一键添加多个用户的批量导入功能（CSV导入）
- 自动生成的欢迎邮件，包含登录信息

#### 1.2 用户管理列表
- 显示所有由Audit Trail管理的用户
- 快速编辑用户权限和角色
- 用户状态指示器（活跃/禁用）
- 批量操作功能（删除、更改角色、应用权限模板）

### 2. 权限控制系统

#### 2.1 权限管理界面
- 权限控制矩阵，按功能类别分组:
  - WordPress核心功能（文章、页面、附件等）
  - 插件管理（安装、激活、编辑）
  - 主题管理（切换、编辑）
  - 用户管理（添加、编辑、删除）
  - 工具和设置（导入、导出、设置选项）
  - 媒体管理（上传、编辑、删除）
  - 菜单和小工具
  - 评论管理
  - 自定义文章类型和分类法

#### 2.2 权限模板系统
- 预定义的权限模板，如:
  - "内容编辑者+" (编辑人员+部分管理功能)
  - "有限管理员" (管理员-敏感设置权限)
  - "审核者" (只能审核内容，不能发布)
  - "自定义角色" (完全自定义的权限集)
- 用户可保存自定义权限模板以便重用

#### 2.3 权限继承系统
- 基于用户角色的默认权限
- 可覆盖特定用户的特定权限
- 权限冲突解决机制

## 技术实现

### 1. 数据库结构

添加两个新表:

```sql
CREATE TABLE IF NOT EXISTS `{$wpdb->prefix}audit_user_permissions` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `user_id` bigint(20) NOT NULL,
  `capability` varchar(100) NOT NULL,
  `grant_access` tinyint(1) NOT NULL DEFAULT 0,
  `created_at` datetime NOT NULL,
  `updated_at` datetime NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `user_capability` (`user_id`, `capability`),
  KEY `user_id` (`user_id`)
);

CREATE TABLE IF NOT EXISTS `{$wpdb->prefix}audit_permission_templates` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL,
  `description` text,
  `permissions` longtext NOT NULL,
  `created_at` datetime NOT NULL,
  `updated_at` datetime NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `name` (`name`)
);
```

### 2. 一键添加用户功能实现

```php
/**
 * 一键添加用户，并应用指定的权限模板
 */
function audit_trail_add_user($username, $email, $password = '', $role = 'editor', $template_id = 0) {
    // 生成密码（如果没有提供）
    if (empty($password)) {
        $password = wp_generate_password(12, true, false);
    }
    
    // 创建用户
    $user_id = wp_create_user($username, $password, $email);
    
    if (is_wp_error($user_id)) {
        return $user_id;
    }
    
    // 设置用户角色
    $user = new WP_User($user_id);
    $user->set_role($role);
    
    // 如果指定了权限模板，应用它
    if ($template_id > 0) {
        audit_trail_apply_permission_template($user_id, $template_id);
    }
    
    // 发送欢迎邮件
    if (get_option('audit_send_welcome_email', true)) {
        audit_trail_send_welcome_email($user_id, $password);
    }
    
    // 记录审计日志
    AT_Audit::create('audit_add_user', $user_id, null, $username);
    
    return $user_id;
}

/**
 * 发送欢迎邮件给新用户
 */
function audit_trail_send_welcome_email($user_id, $password) {
    $user = get_userdata($user_id);
    if (!$user) {
        return false;
    }
    
    $subject = sprintf(__('[%s] 您的账号已创建', 'audit-trail'), get_bloginfo('name'));
    
    $message = sprintf(
        __('您好 %s,

您的账号已在 %s 创建。

用户名: %s
密码: %s
登录地址: %s

请尽快登录并修改密码。

此致,
%s', 'audit-trail'),
        $user->display_name,
        get_bloginfo('name'),
        $user->user_login,
        $password,
        wp_login_url(),
        get_bloginfo('name')
    );
    
    return wp_mail($user->user_email, $subject, $message);
}
```

### 3. 权限控制功能实现

```php
/**
 * 检查用户是否有特定权限
 */
function audit_trail_user_has_permission($user_id, $capability) {
    global $wpdb;
    
    // 首先检查用户是否存在
    $user = get_userdata($user_id);
    if (!$user) {
        return false;
    }
    
    // 超级管理员始终拥有所有权限
    if (is_super_admin($user_id)) {
        return true;
    }
    
    // 检查是否有明确的权限设置
    $table_name = $wpdb->prefix . 'audit_user_permissions';
    $permission = $wpdb->get_var($wpdb->prepare(
        "SELECT grant_access FROM $table_name WHERE user_id = %d AND capability = %s",
        $user_id,
        $capability
    ));
    
    // 如果有明确设置，返回结果
    if ($permission !== null) {
        return (bool)$permission;
    }
    
    // 否则，回退到WordPress默认权限
    return user_can($user_id, $capability);
}

/**
 * 设置用户特定权限
 */
function audit_trail_set_user_permission($user_id, $capability, $grant = true) {
    global $wpdb;
    
    $table_name = $wpdb->prefix . 'audit_user_permissions';
    
    // 检查权限是否已存在
    $exists = $wpdb->get_var($wpdb->prepare(
        "SELECT id FROM $table_name WHERE user_id = %d AND capability = %s",
        $user_id,
        $capability
    ));
    
    if ($exists) {
        // 更新现有权限
        $result = $wpdb->update(
            $table_name,
            array(
                'grant_access' => $grant ? 1 : 0,
                'updated_at' => current_time('mysql')
            ),
            array(
                'user_id' => $user_id,
                'capability' => $capability
            )
        );
    } else {
        // 插入新权限
        $result = $wpdb->insert(
            $table_name,
            array(
                'user_id' => $user_id,
                'capability' => $capability,
                'grant_access' => $grant ? 1 : 0,
                'created_at' => current_time('mysql'),
                'updated_at' => current_time('mysql')
            )
        );
    }
    
    if ($result) {
        // 记录审计日志
        $action = $grant ? 'grant_permission' : 'deny_permission';
        AT_Audit::create('audit_' . $action, $user_id, $capability, get_userdata($user_id)->user_login);
    }
    
    return $result;
}

/**
 * 应用权限模板到用户
 */
function audit_trail_apply_permission_template($user_id, $template_id) {
    global $wpdb;
    
    $table_name = $wpdb->prefix . 'audit_permission_templates';
    $template = $wpdb->get_row($wpdb->prepare(
        "SELECT * FROM $table_name WHERE id = %d",
        $template_id
    ));
    
    if (!$template) {
        return false;
    }
    
    // 解析权限数据
    $permissions = json_decode($template->permissions, true);
    if (!$permissions || !is_array($permissions)) {
        return false;
    }
    
    // 应用每个权限设置
    foreach ($permissions as $capability => $grant) {
        audit_trail_set_user_permission($user_id, $capability, (bool)$grant);
    }
    
    // 记录审计日志
    AT_Audit::create('audit_apply_template', $user_id, $template->name, get_userdata($user_id)->user_login);
    
    return true;
}
```

### 4. 钩子与过滤器 

```php
/**
 * 拦截并控制WordPress权限检查
 */
function audit_trail_user_has_cap_filter($allcaps, $caps, $args, $user) {
    // 如果不是检查特定能力，跳过
    if (empty($args[0])) {
        return $allcaps;
    }
    
    $capability = $args[0];
    $user_id = $user->ID;
    
    // 检查Audit Trail权限系统
    $permission = audit_trail_get_user_permission_setting($user_id, $capability);
    
    // 如果在我们的系统中有明确设置
    if ($permission !== null) {
        if ($permission) {
            // 允许访问
            $allcaps[$capability] = true;
        } else {
            // 拒绝访问
            unset($allcaps[$capability]);
        }
    }
    
    return $allcaps;
}
add_filter('user_has_cap', 'audit_trail_user_has_cap_filter', 10, 4);

/**
 * 获取用户特定权限设置
 */
function audit_trail_get_user_permission_setting($user_id, $capability) {
    global $wpdb;
    
    $table_name = $wpdb->prefix . 'audit_user_permissions';
    $permission = $wpdb->get_var($wpdb->prepare(
        "SELECT grant_access FROM $table_name WHERE user_id = %d AND capability = %s",
        $user_id,
        $capability
    ));
    
    if ($permission === null) {
        return null; // 没有明确设置
    }
    
    return (bool)$permission;
}
```

### 5. 用户界面实现

#### 5.1 管理菜单添加

```php
/**
 * 添加用户管理菜单
 */
function audit_trail_add_user_management_menu() {
    add_submenu_page(
        'tools.php', 
        __('用户管理', 'audit-trail'),
        __('用户管理', 'audit-trail'),
        'manage_options',
        'audit-user-management',
        'audit_trail_user_management_page'
    );
}
add_action('admin_menu', 'audit_trail_add_user_management_menu');
```

#### 5.2 用户管理主页面

```php
/**
 * 渲染用户管理页面
 */
function audit_trail_user_management_page() {
    // 处理表单提交
    if (isset($_POST['audit_action']) && check_admin_referer('audit_user_management')) {
        switch ($_POST['audit_action']) {
            case 'add_user':
                audit_trail_process_add_user();
                break;
            case 'edit_permissions':
                audit_trail_process_edit_permissions();
                break;
            case 'save_template':
                audit_trail_process_save_template();
                break;
        }
    }
    
    // 显示当前选项卡内容
    $current_tab = isset($_GET['tab']) ? sanitize_key($_GET['tab']) : 'users';
    ?>
    <div class="wrap">
        <h1><?php echo __('Audit Trail 用户管理', 'audit-trail'); ?></h1>
        
        <nav class="nav-tab-wrapper">
            <a href="?page=audit-user-management&tab=users" class="nav-tab <?php echo $current_tab == 'users' ? 'nav-tab-active' : ''; ?>"><?php echo __('用户列表', 'audit-trail'); ?></a>
            <a href="?page=audit-user-management&tab=add" class="nav-tab <?php echo $current_tab == 'add' ? 'nav-tab-active' : ''; ?>"><?php echo __('添加用户', 'audit-trail'); ?></a>
            <a href="?page=audit-user-management&tab=permissions" class="nav-tab <?php echo $current_tab == 'permissions' ? 'nav-tab-active' : ''; ?>"><?php echo __('权限模板', 'audit-trail'); ?></a>
        </nav>
        
        <div class="tab-content">
            <?php
            switch ($current_tab) {
                case 'add':
                    audit_trail_render_add_user_tab();
                    break;
                case 'permissions':
                    audit_trail_render_permissions_tab();
                    break;
                default:
                    audit_trail_render_users_tab();
            }
            ?>
        </div>
    </div>
    <?php
}
```

#### 5.3 添加用户表单

```php
/**
 * 渲染添加用户表单
 */
function audit_trail_render_add_user_tab() {
    // 获取可用的权限模板
    $templates = audit_trail_get_permission_templates();
    ?>
    <div class="audit-add-user-form">
        <h2><?php echo __('一键添加用户', 'audit-trail'); ?></h2>
        
        <form method="post" action="">
            <?php wp_nonce_field('audit_user_management'); ?>
            <input type="hidden" name="audit_action" value="add_user">
            
            <table class="form-table">
                <tr>
                    <th><label for="username"><?php echo __('用户名', 'audit-trail'); ?> <span class="required">*</span></label></th>
                    <td><input type="text" name="username" id="username" class="regular-text" required></td>
                </tr>
                <tr>
                    <th><label for="email"><?php echo __('电子邮件', 'audit-trail'); ?> <span class="required">*</span></label></th>
                    <td><input type="email" name="email" id="email" class="regular-text" required></td>
                </tr>
                <tr>
                    <th><label for="password"><?php echo __('密码', 'audit-trail'); ?></label></th>
                    <td>
                        <input type="password" name="password" id="password" class="regular-text">
                        <p class="description"><?php echo __('留空则自动生成密码', 'audit-trail'); ?></p>
                    </td>
                </tr>
                <tr>
                    <th><label for="role"><?php echo __('角色', 'audit-trail'); ?></label></th>
                    <td>
                        <select name="role" id="role">
                            <?php
                            // 获取所有可用角色
                            $roles = get_editable_roles();
                            foreach ($roles as $role_key => $role_data) {
                                echo '<option value="' . esc_attr($role_key) . '">' . esc_html($role_data['name']) . '</option>';
                            }
                            ?>
                        </select>
                    </td>
                </tr>
                <tr>
                    <th><label for="template"><?php echo __('权限模板', 'audit-trail'); ?></label></th>
                    <td>
                        <select name="template" id="template">
                            <option value="0"><?php echo __('- 无模板 -', 'audit-trail'); ?></option>
                            <?php
                            foreach ($templates as $template) {
                                echo '<option value="' . esc_attr($template->id) . '">' . esc_html($template->name) . '</option>';
                            }
                            ?>
                        </select>
                        <p class="description"><?php echo __('选择预定义的权限模板以快速配置用户权限', 'audit-trail'); ?></p>
                    </td>
                </tr>
                <tr>
                    <th><label for="send_email"><?php echo __('发送欢迎邮件', 'audit-trail'); ?></label></th>
                    <td>
                        <label>
                            <input type="checkbox" name="send_email" id="send_email" value="1" checked>
                            <?php echo __('发送包含登录信息的欢迎邮件给新用户', 'audit-trail'); ?>
                        </label>
                    </td>
                </tr>
            </table>
            
            <p class="submit">
                <input type="submit" name="submit" id="submit" class="button button-primary" value="<?php echo __('添加用户', 'audit-trail'); ?>">
            </p>
        </form>
        
        <hr>
        
        <h3><?php echo __('批量导入用户', 'audit-trail'); ?></h3>
        <p><?php echo __('使用CSV文件批量添加多个用户。CSV文件格式：用户名,邮箱,密码,角色,模板ID', 'audit-trail'); ?></p>
        
        <form method="post" action="" enctype="multipart/form-data">
            <?php wp_nonce_field('audit_user_management'); ?>
            <input type="hidden" name="audit_action" value="import_users">
            
            <table class="form-table">
                <tr>
                    <th><label for="csv_file"><?php echo __('CSV文件', 'audit-trail'); ?></label></th>
                    <td><input type="file" name="csv_file" id="csv_file" accept=".csv"></td>
                </tr>
            </table>
            
            <p class="submit">
                <input type="submit" name="submit" id="submit" class="button button-primary" value="<?php echo __('导入用户', 'audit-trail'); ?>">
            </p>
        </form>
    </div>
    <?php
}
```

## 权限矩阵设计

为了实现类似Advanced Access Manager的细粒度权限控制，我们定义以下权限矩阵：

### WordPress核心能力

| 权限ID | 说明 | 默认角色 |
|--------|------|----------|
| `edit_posts` | 编辑文章 | 作者及以上 |
| `edit_published_posts` | 编辑已发布文章 | 作者及以上 |
| `publish_posts` | 发布文章 | 作者及以上 |
| `edit_pages` | 编辑页面 | 编辑及以上 |
| `edit_published_pages` | 编辑已发布页面 | 编辑及以上 |
| `publish_pages` | 发布页面 | 编辑及以上 |
| `manage_categories` | 管理分类 | 编辑及以上 |
| `moderate_comments` | 管理评论 | 编辑及以上 |
| `upload_files` | 上传文件 | 作者及以上 |
| `edit_theme_options` | 编辑主题选项 | 管理员 |
| `install_plugins` | 安装插件 | 管理员 |
| `activate_plugins` | 激活插件 | 管理员 |
| `edit_plugins` | 编辑插件 | 管理员 |
| `install_themes` | 安装主题 | 管理员 |
| `switch_themes` | 切换主题 | 管理员 |
| `edit_themes` | 编辑主题 | 管理员 |
| `create_users` | 创建用户 | 管理员 |
| `edit_users` | 编辑用户 | 管理员 |
| `delete_users` | 删除用户 | 管理员 |
| `manage_options` | 管理选项 | 管理员 |
| `import` | 导入内容 | 管理员 |
| `export` | 导出内容 | 管理员 |
| `update_core` | 更新WordPress | 管理员 |

### 自定义权限（插件特有）

| 权限ID | 说明 | 默认角色 |
|--------|------|----------|
| `audit_view_logs` | 查看审计日志 | 管理员 |
| `audit_export_logs` | 导出审计日志 | 管理员 |
| `audit_delete_logs` | 删除审计日志 | 管理员 |
| `audit_manage_settings` | 管理审计设置 | 管理员 |
| `audit_manage_users` | 管理用户和权限 | 管理员 |

## 预定义权限模板

为了简化权限分配，我们将创建以下预定义模板：

### 1. 内容编辑者+

适用于需要额外权限的编辑人员，但不需要完全的管理员权限：
- 继承所有编辑角色权限
- 添加`publish_pages`权限（可发布页面）
- 添加`moderate_comments`权限（可管理评论）
- 添加`upload_files`权限（可上传文件）
- 添加`audit_view_logs`权限（可查看审计日志）

### 2. 有限管理员

拥有大部分管理员功能，但限制某些敏感操作：
- 继承所有管理员权限
- 移除`update_core`权限（不能更新WordPress）
- 移除`install_plugins`权限（不能安装新插件）
- 移除`edit_plugins`权限（不能编辑插件代码）
- 移除`install_themes`权限（不能安装新主题）
- 移除`edit_themes`权限（不能编辑主题文件）

### 3. 内容管理员

专注于内容管理，不能修改系统设置：
- 继承所有编辑权限
- 添加`publish_pages`权限（可发布页面）
- 添加`moderate_comments`权限（可管理评论）
- 添加`create_users`权限（可创建用户）
- 添加`edit_users`权限（可编辑用户，但只能编辑作者和贡献者）
- 但没有其他管理员权限（如插件、主题、设置管理）

## 高级功能选项

为确保权限系统的灵活性，我们将提供以下高级选项：

### 1. 全局权限设置

- 允许/禁止特定用户组查看特定类型的内容（如私密文章）
- 全局权限覆盖（适用于紧急情况）
- 批量应用权限模板到多个用户

### 2. 权限冲突解决策略

当出现权限冲突时，遵循以下优先级：
1. 用户特定权限覆盖所有其他设置
2. 显式拒绝的权限优先于显式允许的权限
3. 如果没有明确设置，则回退到WordPress默认权限

### 3. 权限审计

- 记录所有权限更改操作
- 提供权限变更历史查看
- 支持权限回退到特定时间点

## 与Audit Trail核心功能集成

该权限系统将与Audit Trail的核心审计功能无缝集成：

1. 记录所有权限变更
2. 记录基于自定义权限的访问尝试（包括成功和拒绝）
3. 允许根据权限变更筛选审计日志
4. 提供权限异常报告（如频繁被拒绝的访问请求）

## 安全考虑

1. 实现权限管理的权限检查，确保只有超级管理员或具有`audit_manage_users`权限的用户才能管理权限
2. 添加nonce校验和能力检查，防止未授权修改
3. 防止权限提升漏洞，确保用户不能给自己分配比自己权限更高的权限
4. 保留至少一个超级管理员账号，防止管理员锁定情况

## 性能影响

为保持Audit Trail插件的低资源占用特性，权限系统设计具有以下特点：

1. 使用缓存减少数据库查询次数
2. 只在需要时检查自定义权限
3. 批量操作和权限计算优化
4. 权限检查结果缓存，提高性能

## 实施计划

该功能将作为v2.8.5版本发布，安排在基础性能优化和核心功能改进之后。预计开发周期为3周，包括：

- 设计和规划: 3天
- 核心权限系统实现: 5天
- 用户界面开发: 5天
- 测试和调试: 5天
- 文档编写: 2天

## 结论

用户管理与权限控制功能将大大增强Audit Trail插件的价值，使其不仅能够记录WordPress活动，还能主动控制用户权限，实现更精细的访问控制。这一功能特别适合需要给编辑人员提供部分管理员功能，同时限制某些敏感操作的场景。 