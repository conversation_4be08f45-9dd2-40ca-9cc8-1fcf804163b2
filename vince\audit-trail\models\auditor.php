<?php

function maybe_unjsonize( $thing ) {
	if ( is_serialized( $thing ) ) {
		return unserialize( $thing );
	}

	$json = json_decode( $thing );
	if ( json_last_error() === JSON_ERROR_NONE ) {
		return $json;
	}

	return $thing;
}

class AT_Auditor {
	/**
	 * Register appropriate hooks
	 *
	 * @return void
	 **/

	function __construct() {
		// 确保翻译表已加载
		$this->load_translations();
	
		add_filter( 'audit_collect', array( $this, 'audit_collect' ) );
		add_action( 'audit_listen', array( $this, 'audit_listen' ) );
		add_filter( 'audit_show_operation', array( $this, 'audit_show_operation' ) );
		add_filter( 'audit_show_item', array( $this, 'audit_show_item' ) );
		add_filter( 'audit_show_details', array( $this, 'audit_show_details' ) );
		
		// 直接注册页面构建器相关钩子，确保始终监听这些事件
		$this->register_pagebuilder_hooks();
	}

	/**
	 * 加载翻译文件
	 */
	function load_translations() {
		// 固定使用中文
		$mo_file = WP_PLUGIN_DIR . '/audit-trail/locale/zh_CN.mo';
		if (file_exists($mo_file)) {
			load_textdomain('audit-trail', $mo_file);
		}
	}

	/**
	 * Register the types of data we can collect
	 *
	 * @return array Types to listen for
	 **/

	function audit_collect( $items ) {
		$items['post']     = __( 'Post & page management', 'audit-trail' );
		$items['attach']   = __( 'File attachments', 'audit-trail' );
		$items['user']     = __( 'User profiles & logins', 'audit-trail' );
		$items['theme']    = __( 'Theme switching', 'audit-trail' );
		$items['link']     = __( 'Link management', 'audit-trail' );
		$items['category'] = __( 'Category management', 'audit-trail' );
		$items['comment']  = __( 'Comment management', 'audit-trail' );
		$items['viewing']  = __( 'User page visits', 'audit-trail' );
		$items['audit']    = __( 'Audit Trail actions', 'audit-trail' );
		$items['plugin']   = __( 'Plugin actions', 'audit-trail' );
		$items['pagebuilder'] = __( 'Page Builder operations', 'audit-trail' );

		return $items;
	}


	/**
	 * Insert the hooks to listen for, given a particular area, into the list of actions
	 *
	 * @param string $method The type of area we are listening in
	 * @return void
	 **/

	function audit_listen( $method ) {
		$ignore = get_option( 'audit_ignore' );
		if ( $ignore !== '' ) {
			$current = wp_get_current_user();
			$users   = explode( ',', $ignore );

			if ( in_array( $current->ID, $users ) || in_array( 0, $users ) && $current === false )
				return;
		}

		$map = array(
			'post' => array(
				'delete_post',
				'save_post',
				'private_to_published',
				'draft_to_publish',
				'pending_to_publish',
				'publish_to_draft',
				'publish_to_private',
				'publish_to_pending',
				'publish_to_trash',
				'draft_to_pending',
				'draft_to_trash',
				'pending_to_draft',
				'pending_to_trash',
				'trash_to_publish',
				'trash_to_draft',
				'trash_to_pending',
				'future_to_publish',
			),
			'attach' => array(
				'delete_attachment',
				'add_attachment',
				'edit_attachment'
			),
			'user' => array(
				'wp_login',
				'wp_logout',
				'user_register',
				'profile_update',
				'delete_user',
				'retrieve_password',
				'login_errors',
				'wp_login_failed'
			),
			'theme' => array(
				'switch_theme',
			),
			'link' => array(
				'edit_link',
				'add_link',
				'delete_link'
			),
			'category' => array(
				'edit_category',
				'add_category',
				'delete_category'
			),
			'comment' => array(
				'edit_comment',
				'delete_comment'
			),
			'viewing' => array(
				'template_redirect'
			),
			'plugin' => array(
				'activate_plugin',
				'deactivate_plugin',
			),
			'pagebuilder' => array(
				'elementor/editor/after_save',
				'elementor/document/after_save',
				'elementor/editor/before_save',
				'fl_builder_after_save_layout',
				'fl_builder_before_save_layout',
				'fl_builder_after_layout_rendered',
				'siteorigin_panels_save_post',
				'siteorigin_panels_after_render',
				'vc_after_save_post',
				'vc_before_save_post',
				'vc_after_update',
				'et_fb_save_layout',
				'et_fb_ajax_save',
				'et_builder_after_save_layout',
				'blocks_parsed',
				'save_post'
			)
		);

		if ( isset( $map[$method] ) ) {
			foreach ( $map[$method] AS $name ) {
				// 特殊处理blocks_parsed钩子，需要更高优先级
				if ($name === 'blocks_parsed') {
					add_filter( $name, array( $this, $name ), 20, 2 );
				} 
				// 特殊处理Elementor和其他包含斜杠的钩子名称
				else if (strpos($name, '/') !== false) {
					// 针对不同类型的钩子设置回调函数
					if ($name === 'elementor/editor/after_save') {
						add_action( $name, array( $this, 'elementor_editor_after_save' ), 10, 2 );
					}
					else if ($name === 'elementor/document/after_save') {
						add_action( $name, array( $this, 'elementor_document_after_save' ), 10, 1 );
					}
					else if ($name === 'elementor/editor/before_save') {
						add_action( $name, array( $this, 'elementor_editor_before_save' ), 10, 2 );
					}
					else {
						// 如果有其他斜杠形式的钩子，转换为有效的PHP方法名
						$method_name = str_replace('/', '_', $name);
						add_action( $name, array( $this, $method_name ), 10, 2 );
					}
				}
				else {
					add_action( $name, array( $this, $name ), 10, 2 );
				}
			}
		}
	}


	/**
	 * Given a log item will display the details
	 *
	 * @param AT_Audit $item
	 * @return AT_Audit
	 **/
	function audit_show_details( $item ) {
		switch ( $item->operation ) {
			case 'user_register' :
			case 'profile_update' :
				$user = maybe_unjsonize( $item->data );

				$item->message = '<br/>'.$this->capture( 'details/profile_update', array( 'item' => $item, 'user' => $user ) );
				break;

			case 'add_link' :
			case 'edit_link' :
				$link = maybe_unjsonize( $item->data );

				$item->message = '<br/>'.$this->capture( 'details/edit_link', array( 'item' => $item, 'link' => $link ) );
				break;

			case 'add_category' :
			case 'edit_category' :
				$cat = maybe_unjsonize( $item->data );

				$item->message = '<br/>'.$this->capture( 'details/edit_category', array( 'item' => $item, 'cat' => $cat ) );
				break;

			case 'edit_comment' :
				$original = get_comment( $item->item_id );
				$comment  = maybe_unjsonize( $item->data );

				$item->message = '<br/>'.$this->capture( 'details/'.$item->operation, array( 'item' => $item, 'comment' => $comment ) );
				break;

			case 'save_post' :
				$original = get_post ($item->item_id);
				$post     = maybe_unjsonize ($item->data);

				$item->message = '<br/>'.$this->capture( 'details/'.$item->operation, array( 'item' => $item, 'post' => $post ) );
				break;
				
			case 'elementor/editor/after_save' :
			case 'elementor/document/after_save' :
			case 'elementor/editor/before_save' :
			case 'fl_builder_after_save_layout' :
			case 'fl_builder_before_save_layout' :
			case 'fl_builder_after_layout_rendered' :
			case 'siteorigin_panels_save_post' :
			case 'siteorigin_panels_after_render' :
			case 'vc_after_save_post' :
			case 'vc_before_save_post' :
			case 'vc_after_update' :
			case 'et_fb_save_layout' :
			case 'et_fb_ajax_save' :
			case 'et_builder_after_save_layout' :
			case 'blocks_parsed' :
				$post = maybe_unjsonize( $item->data );
				$builder_data = isset($post->builder_data) ? $post->builder_data : array();

				$item->message = '<br/>'.$this->capture( 'details/pagebuilder_edit', array( 'item' => $item, 'post' => $post, 'builder_data' => $builder_data ) );
				break;

			default:
				break;
		}

		return $item;
	}


	/**
	 * Given a log item will pretty-print the item
	 *
	 * @param AT_Audit $item
	 * @return AT_Audit
	 **/

	function audit_show_item( $item ) {
		switch ( $item->operation )	{
			case 'activate_plugin':
			case 'deactivate_plugin':
				$title = explode( '/', $item->data );
				$title = str_replace( '-', ' ', $title[0] );
				$title = str_replace( '.php', '', $title );
				$title = ucwords( $title );

				$item->message = esc_html( $title ) . ' <span class="detail-data">(' . esc_html( $item->data ) . ')</span>';
				break;

			case 'wp_login_failed' :
				$item->message = '<span class="detail-data">' . esc_html( $item->data ) . '</span>';
				break;

			case 'delete_link' :
				$item->message = '<span class="detail-data">' . esc_html( $item->data ) . '</span>';
				break;

			case 'switch_theme' :
				$theme_data = wp_get_theme($item->data);
				$theme_name = $theme_data->exists() ? $theme_data->get('Name') : $item->data;
				$item->message = '<span class="detail-data">' . esc_html( $theme_name ) . '</span>';
				break;

			case 'profile_update' :
			case 'wp_logout':
			case 'wp_login' :
				$user = get_userdata( $item->item_id );
				if ( $user === false )
					$item->message = intval( $item->item_id );
				else
					$item->message = '<a href="user-edit.php?user_id='.esc_attr( $item->item_id ).'">'.esc_html( $user->user_nicename )."</a> <span class=\"detail-data\">(" . esc_html( $user->user_email ) . ")</span>";
				break;

			case 'user_register' :
				$user = maybe_unjsonize( $item->data );
				if (is_object($user) && isset($user->user_nicename)) {
					$email = isset($user->user_email) ? $user->user_email : '';
					$item->message = '<a href="user-edit.php?user_id='.esc_attr( $user->ID ).'">'.esc_html( $user->user_nicename )."</a> <span class=\"detail-data\">(" . esc_html( $email ) . ")</span>";
				} else {
					$current_user = get_userdata( $item->item_id );
					if ($current_user) {
						$item->message = '<a href="user-edit.php?user_id='.esc_attr( $item->item_id ).'">'.esc_html( $current_user->user_nicename )."</a> <span class=\"detail-data\">(" . esc_html( $current_user->user_email ) . ")</span>";
					} else {
						$item->message = intval( $item->item_id );
					}
				}
				break;

			case 'delete_user' :
			case 'retrieve_password' :
				$user = get_user_by( 'login', $item->data );
				if ( $user === false )
					$item->message = '<span class="detail-data">' . esc_html( $item->data ) . '</span>';
				else
					$item->message = '<a href="user-edit.php?user_id='.esc_attr( $item->item_id ).'">'.esc_html( $user->user_nicename )."</a> <span class=\"detail-data\">(" . esc_html( $user->user_email ) . ")</span>";
				break;

			case 'add_link' :
			case 'edit_link' :
				$link = maybe_unjsonize( $item->data );
				if (is_object($link) && isset($link->link_name)) {
					$link_url = isset($link->link_url) ? $link->link_url : '';
					$item->message = '<a href="link.php?link_id='.esc_attr( $link->link_id ).'&action=edit">'.esc_html( $link->link_name ).'</a> <span class="detail-data">(' . esc_html( $link_url ) . ')</span>';
				} else {
					$item->message = 'ID: ' . intval($item->item_id);
				}
				break;

			case 'edit_category' :
			case 'add_category' :
				$cat = maybe_unjsonize( $item->data );
				if (is_object($cat) && isset($cat->cat_name)) {
					$cat_desc = isset($cat->category_description) && !empty($cat->category_description) ? 
						$cat->category_description : (isset($cat->cat_description) ? $cat->cat_description : '');
					$desc_text = !empty($cat_desc) ? ' - ' . substr(strip_tags($cat_desc), 0, 50) . (strlen($cat_desc) > 50 ? '...' : '') : '';
					
					$item->message = '<a href="edit-tags.php?action=edit&amp;taxonomy=category&amp;tag_ID='.esc_attr( $cat->cat_ID ).'">'.
						esc_html( $cat->cat_name ).'</a> <span class="detail-data">' . esc_html( $desc_text ) . '</span>';
				} else {
					$current_cat = get_category($item->item_id);
					if ($current_cat && !is_wp_error($current_cat)) {
						$item->message = '<a href="edit-tags.php?action=edit&amp;taxonomy=category&amp;tag_ID='.esc_attr($item->item_id).'">'.
							esc_html($current_cat->name).'</a>';
					} else {
						$item->message = 'ID: ' . intval($item->item_id);
					}
				}
				break;

			case 'delete_category' :
				$item->message = '<span class="detail-data">ID: ' . intval($item->item_id) . ' - ' . esc_html($item->data) . '</span>';
				break;

			case 'edit_comment' :
				$comment = get_comment($item->item_id);
				if ($comment) {
					$post_title = get_the_title($comment->comment_post_ID);
					$item->message = '<a href="comment.php?action=editcomment&amp;c='.esc_attr($item->item_id).'">'.
						esc_html( '评论 #' . $item->item_id ).'</a> <span class="detail-data">(文章: ' . 
						esc_html($post_title) . ')</span>';
				} else {
					// 使用序列化数据获取信息
					$comment_data = maybe_unjsonize($item->data);
					if (is_object($comment_data) && isset($comment_data->comment_post_ID)) {
						$post_title = get_the_title($comment_data->comment_post_ID);
						$post_title = $post_title ? $post_title : 'ID: ' . $comment_data->comment_post_ID;
						$item->message = '<span class="detail-data">评论 #' . intval($item->item_id) . ' (文章: ' . 
							esc_html($post_title) . ')</span>';
					} else {
						$item->message = '<span class="detail-data">评论 #' . intval($item->item_id) . '</span>';
					}
				}
				break;

			case 'delete_comment' :
				$item->message = '<span class="detail-data">评论 #' . intval($item->item_id) . '</span>';
				break;

			case 'save_post' :
				$post = maybe_unjsonize( $item->data );
				if ( $post ) {
					$post_type_obj = get_post_type_object($post->post_type);
					$post_type_name = $post_type_obj ? $post_type_obj->labels->singular_name : $post->post_type;
					
					$item->message = '<a href="post.php?action=edit&amp;post='.esc_attr( $post->ID ).'">'.
						esc_html( $post->post_title ).'</a> <span class="detail-data">(' . 
						esc_html( $post_type_name ) . ' #' . $post->ID . ')</span>';
				} else {
					$current_post = get_post($item->item_id);
					if ($current_post) {
						$post_type_obj = get_post_type_object($current_post->post_type);
						$post_type_name = $post_type_obj ? $post_type_obj->labels->singular_name : $current_post->post_type;
						
						$item->message = '<a href="post.php?action=edit&amp;post='.esc_attr($item->item_id).'">'.
							esc_html($current_post->post_title).'</a> <span class="detail-data">(' . 
							esc_html($post_type_name) . ' #' . $item->item_id . ')</span>';
					} else {
						$item->message = '<span class="detail-data">ID: ' . intval($item->item_id) . '</span>';
					}
				}
				break;

			case 'delete_post' :
				$title = !empty($item->title) ? $item->title : 'ID: ' . intval($item->item_id);
				$item->message = '<span class="detail-data">' . esc_html($title) . '</span>';
				break;

			case 'private_to_published':
			case 'draft_to_publish':
			case 'pending_to_publish':
			case 'publish_to_draft':
			case 'publish_to_private':
			case 'publish_to_pending':
			case 'publish_to_trash':
			case 'draft_to_pending':
			case 'draft_to_trash':
			case 'pending_to_draft':
			case 'pending_to_trash':
			case 'trash_to_publish':
			case 'trash_to_draft':
			case 'trash_to_pending':
			case 'future_to_publish':
				$post = get_post( $item->item_id );
				if ( $post ) {
					$post_type_obj = get_post_type_object($post->post_type);
					$post_type_name = $post_type_obj ? $post_type_obj->labels->singular_name : $post->post_type;
					
					$item->message = '<a href="post.php?action=edit&amp;post='.esc_attr( $post->ID ).'">'.
						esc_html( $post->post_title ).'</a> <span class="detail-data">(' . 
						esc_html( $post_type_name ) . ' #' . $post->ID . ')</span>';
				} else {
					// 尝试从数据中获取信息
					$post_data = maybe_unjsonize($item->data);
					if (is_object($post_data) && isset($post_data->post_title)) {
						$post_type = isset($post_data->post_type) ? $post_data->post_type : '';
						$post_type_obj = get_post_type_object($post_type);
						$post_type_name = $post_type_obj ? $post_type_obj->labels->singular_name : $post_type;
						
						$item->message = esc_html($post_data->post_title) . ' <span class="detail-data">(' . 
							esc_html($post_type_name) . ' #' . intval($item->item_id) . ')</span>';
					} else {
						$title = !empty($item->title) ? $item->title : 'ID: ' . intval($item->item_id);
						$item->message = '<span class="detail-data">' . esc_html($title) . '</span>';
					}
				}
				break;

			case 'add_attachment' :
			case 'edit_attachment' :
				$post = get_post( $item->item_id );
				$file_name = basename($item->data);
				$file_type = wp_check_filetype($file_name);
				$file_type_text = $file_type['ext'] ? strtoupper($file_type['ext']) : '';
				
				$text = '<a href="media.php?action=edit&amp;attachment_id='.esc_attr( $item->item_id ).'">' . 
					esc_html( $file_name ) . '</a>';
				
				if (!empty($post)) {
					$details = '<span class="detail-data">';
					if ($file_type_text) {
						$details .= '(' . $file_type_text . ')';
					}
					
					if ($post->post_parent > 0) {
						$parent_post = get_post($post->post_parent);
						if ($parent_post) {
							$details .= ' 附属于: <a href="post.php?action=edit&amp;post='.esc_attr($post->post_parent).'">'.
								esc_html($parent_post->post_title).'</a>';
						} else {
							$details .= ' 附属于文章 #'.esc_html($post->post_parent);
						}
					}
					
					$details .= '</span>';
					$text .= $details;
				}
				
				$item->message = $text;
				break;

			case 'delete_attachment':
				// 尝试从数据中获取文件信息
				$data = json_decode($item->data);
				if (is_object($data) && isset($data->filename)) {
					$filename = $data->filename;
					$title = isset($data->title) && !empty($data->title) ? $data->title : $filename;
					$mimetype = isset($data->mimetype) ? $data->mimetype : '';
					
					$file_type = '';
					if ($mimetype) {
						if (strpos($mimetype, 'image/') === 0) {
							$file_type = '图片';
						} elseif (strpos($mimetype, 'video/') === 0) {
							$file_type = '视频';
						} elseif (strpos($mimetype, 'audio/') === 0) {
							$file_type = '音频';
						} elseif (strpos($mimetype, 'application/pdf') === 0) {
							$file_type = 'PDF';
						} elseif (strpos($mimetype, 'text/') === 0) {
							$file_type = '文本';
						} else {
							$file_type = pathinfo($filename, PATHINFO_EXTENSION);
							$file_type = $file_type ? strtoupper($file_type) : '文件';
						}
					} else {
						$ext = pathinfo($filename, PATHINFO_EXTENSION);
						$file_type = $ext ? strtoupper($ext) : '文件';
					}
					
					$item->message = '<span class="detail-data">' . esc_html($title) . 
									 ' (' . esc_html($file_type) . ' #' . intval($item->item_id) . ')</span>';
				} else if (!empty($item->title)) {
					$item->message = '<span class="detail-data">' . esc_html($item->title) . ' (#' . intval($item->item_id) . ')</span>';
				} else if (!empty($item->data) && !is_object(json_decode($item->data))) {
					$file_name = basename($item->data);
					$item->message = '<span class="detail-data">' . esc_html($file_name) . ' (#' . intval($item->item_id) . ')</span>';
				} else {
					$item->message = '<span class="detail-data">附件 #' . intval($item->item_id) . '</span>';
				}
				break;

			case 'template_redirect':
				if ( $item->item_id > 0 ) {
					$post = get_post($item->item_id);
					$data = json_decode($item->data);
					$url = is_object($data) && isset($data->url) ? $data->url : $item->data;
					
					if ($post) {
						$post_type_obj = get_post_type_object($post->post_type);
						$post_type_name = $post_type_obj ? $post_type_obj->labels->singular_name : $post->post_type;
						
						$item->message = '<a href="post.php?action=edit&amp;post='.esc_attr( $item->item_id ).'">'.
							esc_html( $post->post_title ).'</a> <span class="detail-data">(' . 
							esc_html( $post_type_name ) . ' #' . $post->ID . ' - ' . esc_html($url) . ')</span>';
					} else {
						$item->message = '<span class="detail-data">ID: ' . intval($item->item_id) . ' - ' . esc_html($url) . '</span>';
					}
				} else {
					$data = json_decode($item->data);
					$url = is_object($data) && isset($data->url) ? $data->url : $item->data;
					$item->message = '<span class="detail-data">' . esc_html($url) . '</span>';
				}
				break;
		}

		return $item;
	}


	/**
	 * Given a log item will pretty-print the operation
	 *
	 * @param AT_Audit $item
	 * @return AT_Audit
	 **/

	function audit_show_operation( $item ) {
		$operations = array(
			'draft_to_publish'      => __( '草稿改为发布', 'audit-trail' ),
			'pending_to_publish'    => __( '待审改为发布', 'audit-trail' ),
			'private_to_published'  => __( '私密改为发布', 'audit-trail' ),
			'publish_to_draft'      => __( '发布改为草稿', 'audit-trail' ),
			'publish_to_pending'    => __( '发布改为待审', 'audit-trail' ),
			'publish_to_private'    => __( '发布改为私密', 'audit-trail' ),
			'publish_to_trash'      => __( '发布改为回收站', 'audit-trail' ),
			'draft_to_pending'      => __( '草稿改为待审', 'audit-trail' ),
			'draft_to_trash'        => __( '草稿改为回收站', 'audit-trail' ),
			'pending_to_draft'      => __( '待审改为草稿', 'audit-trail' ),
			'pending_to_trash'      => __( '待审改为回收站', 'audit-trail' ),
			'trash_to_publish'      => __( '回收站改为发布', 'audit-trail' ),
			'trash_to_draft'        => __( '回收站改为草稿', 'audit-trail' ),
			'trash_to_pending'      => __( '回收站改为待审', 'audit-trail' ),
			'future_to_publish'     => __( '定时发布', 'audit-trail' ),
			'save_post'             => __( '保存文章/页面', 'audit-trail' ),
			'delete_post'           => __( '删除文章', 'audit-trail' ),
			'elementor/editor/after_save'     => __( 'Elementor编辑器保存', 'audit-trail' ),
			'elementor/document/after_save'   => __( 'Elementor文档保存', 'audit-trail' ),
			'fl_builder_after_save_layout'    => __( 'Beaver Builder保存', 'audit-trail' ),
			'siteorigin_panels_save_post'     => __( 'SiteOrigin保存', 'audit-trail' ),
			'vc_after_save_post'              => __( 'WPBakery保存', 'audit-trail' ),
			'et_fb_save_layout'               => __( 'Divi Builder保存', 'audit-trail' ),
			'blocks_parsed'                   => __( 'Gutenberg编辑', 'audit-trail' ),
			'wp_login'              => sprintf( __( '<a href="#" class="audit-view">%s</a>', 'audit-trail' ), __( '用户登录', 'audit-trail' ) ),
			'wp_login_failed'       => sprintf( __( '<a href="#" class="audit-view">%s</a>', 'audit-trail' ), __( '登录失败', 'audit-trail' ) ),
			'wp_logout'             => __( '用户退出', 'audit-trail' ),
			'user_register'         => sprintf( __( '<a href="#" class="audit-view">%s</a>', 'audit-trail' ), __( '注册新用户', 'audit-trail' ) ),
			'retrieve_password'     => __( '找回密码', 'audit-trail' ),
			'profile_update'        => sprintf( __( '<a href="#" class="audit-view">%s</a>', 'audit-trail' ), __( '更新个人资料', 'audit-trail' ) ),
			'delete_user'           => __( '删除用户', 'audit-trail' ),
			'switch_theme'          => __( '切换主题', 'audit-trail' ),
			'delete_category'       => __( '删除分类', 'audit-trail' ),
			'edit_category'         => sprintf( __( '<a href="#" class="audit-view">%s</a>', 'audit-trail' ), __( '编辑分类', 'audit-trail' ) ),
			'add_category'          => sprintf( __( '<a href="#" class="audit-view">%s</a>', 'audit-trail' ), __( '添加分类', 'audit-trail' ) ),
			'create_category'       => sprintf( __( '<a href="#" class="audit-view">%s</a>', 'audit-trail' ), __( '创建分类', 'audit-trail' ) ),
			'add_link'              => sprintf( __( '<a href="#" class="audit-view">%s</a>', 'audit-trail' ), __( '添加链接', 'audit-trail' ) ),
			'edit_link'             => sprintf( __( '<a href="#" class="audit-view">%s</a>', 'audit-trail' ), __( '编辑链接', 'audit-trail' ) ),
			'delete_link'           => __( '删除链接', 'audit-trail' ),
			'delete_attachment'     => __( '删除附件', 'audit-trail' ),
			'add_attachment'        => sprintf( __( '<a href="#" class="audit-view">%s</a>', 'audit-trail' ), __( '添加附件', 'audit-trail' ) ),
			'edit_attachment'       => sprintf( __( '<a href="#" class="audit-view">%s</a>', 'audit-trail' ), __( '编辑附件', 'audit-trail' ) ),
			'edit_comment'          => sprintf( __( '<a href="#" class="audit-view">%s</a>', 'audit-trail' ), __( '编辑评论', 'audit-trail' ) ),
			'delete_comment'        => __( '删除评论', 'audit-trail' ),
			'template_redirect'     => __( '访问页面', 'audit-trail' ),
			'activate_plugin'       => __( '激活插件', 'audit-trail' ),
			'deactivate_plugin'     => __( '停用插件', 'audit-trail' )
		);

		if ( isset( $operations[$item->operation] ) )
			$item->message = $operations[$item->operation];

		return apply_filters( 'audit_operation', $item );
	}


	/**
	 * Default listening methods
	 **/

	function activate_plugin( $plugin ) {
		AT_Audit::create( 'activate_plugin', 0, $plugin );

	}

	function deactivate_plugin( $plugin ) {
		AT_Audit::create( 'deactivate_plugin', 0, $plugin );
	}

	// Actions to track
	function delete_post( $post_id ) {
		AT_Audit::create( 'delete_post', $post_id );
	}

	function private_to_published( $post_id ) {
		$post = get_post( $post_id );
		AT_Audit::create( 'private_to_published', $post_id, $post, $post->post_title );
	}

	function draft_to_publish( $post_id ) {
		$post = get_post($post_id);
		AT_Audit::create( 'draft_to_publish', $post_id, $post, $post->post_title );
	}

	function pending_to_publish( $post_id ) {
		$post = get_post($post_id);
		AT_Audit::create( 'pending_to_publish', $post_id, $post, $post->post_title );
	}

	function publish_to_draft( $post_id ) {
		$post = get_post($post_id);
		AT_Audit::create( 'publish_to_draft', $post_id, $post, $post->post_title );
	}

	function publish_to_private( $post_id ) {
		$post = get_post($post_id);
		AT_Audit::create( 'publish_to_private', $post_id, $post, $post->post_title );
	}

	function publish_to_pending( $post_id ) {
		$post = get_post($post_id);
		AT_Audit::create( 'publish_to_pending', $post_id, $post, $post->post_title );
	}

	function publish_to_trash( $post_id ) {
		$post = get_post($post_id);
		AT_Audit::create( 'publish_to_trash', $post_id, $post, $post->post_title );
	}

	function draft_to_pending( $post_id ) {
		$post = get_post($post_id);
		AT_Audit::create( 'draft_to_pending', $post_id, $post, $post->post_title );
	}

	function draft_to_trash( $post_id ) {
		$post = get_post($post_id);
		AT_Audit::create( 'draft_to_trash', $post_id, $post, $post->post_title );
	}

	function pending_to_draft( $post_id ) {
		$post = get_post($post_id);
		AT_Audit::create( 'pending_to_draft', $post_id, $post, $post->post_title );
	}

	function pending_to_trash( $post_id ) {
		$post = get_post($post_id);
		AT_Audit::create( 'pending_to_trash', $post_id, $post, $post->post_title );
	}

	function trash_to_publish( $post_id ) {
		$post = get_post($post_id);
		AT_Audit::create( 'trash_to_publish', $post_id, $post, $post->post_title );
	}

	function trash_to_draft( $post_id ) {
		$post = get_post($post_id);
		AT_Audit::create( 'trash_to_draft', $post_id, $post, $post->post_title );
	}

	function trash_to_pending( $post_id ) {
		$post = get_post($post_id);
		AT_Audit::create( 'trash_to_pending', $post_id, $post, $post->post_title );
	}

	function future_to_publish( $post_id ) {
		$post = get_post($post_id);
		AT_Audit::create( 'future_to_publish', $post_id, $post, $post->post_title );
	}

	function save_post( $post_id ) {
		if ( !defined( 'DOING_AJAX' ) ) {
			$post = get_post( $post_id ) ;

			if ( $post && $post->post_status !== 'inherit' )
				AT_Audit::create( 'save_post', $post_id, $post );
		}
	}

	function wp_login ($user) {
		$data = get_user_by( 'login', $user );
		AT_Audit::create( 'wp_login', $data->ID, '', '', $data->ID );
	}

	function wp_logout () {
		global $user_ID;
		AT_Audit::create( 'wp_logout', $user_ID );
	}

	function login_errors ($errors) {
		if ( strpos( $errors, __( '<strong>ERROR</strong>: Incorrect password.' ) ) !== false )	{
			$login = get_user_by( 'login', sanitize_user( $_POST['log'] ) );
			AT_Audit::create( 'wp_login_failed', $login->ID, sanitize_user( $_POST['log'] ) );
		}

		return $errors;
	}

	/**
	 * Login failed
	 * Called from wp_authenticate()
	 */
	function wp_login_failed( $username ) {
		AT_Audit::create( 'wp_login_failed', 0, $username );

		if ( get_option( 'audit_error_log' ) )
			error_log( 'WordPress Login Failure: '.$username.' from '.AT_Audit::get_ip() );
	}

	function switch_theme ($newtheme) {
		AT_Audit::create( 'switch_theme', '', $newtheme );
	}

	function edit_link( $link_id ) {
		global $wpdb;

		$link = $wpdb->get_row( $wpdb->prepare( "SELECT * FROM {$wpdb->links} WHERE link_id=%d", $link_id ) );
		if ( $link )
			AT_Audit::create( 'edit_link', $link_id, serialize( $link ) );
	}

	function delete_link( $link_id ) {
		global $wpdb;

		$link = $wpdb->get_row( $wpdb->prepare( "SELECT * FROM {$wpdb->links} WHERE link_id=%d", $link_id ) );
		if ( $link )
			AT_Audit::create( 'delete_link', $link_id, $link->link_name );
	}

	function add_link( $link_id ) {
		global $wpdb;

		$link = $wpdb->get_row( $wpdb->prepare( "SELECT * FROM {$wpdb->links} WHERE link_id=%d", $link_id ) );
		if ( $link )
			AT_Audit::create( 'add_link', $link_id, serialize( $link ) );
	}

	function edit_category( $cat_id ) {
		// We filter here otherwise we get a lot of annoying messages whenever the admin does anything useful
		if ( strpos( $_SERVER['REQUEST_URI'], 'categories.php') !== false )
			AT_Audit::create( 'edit_category', $cat_id, serialize( get_category( $cat_id ) ) );
	}

	function create_category( $cat_id ) {
		AT_Audit::create( 'create_category', $cat_id, serialize( get_category( $cat_id ) ) );
	}

	function add_category( $cat_id ) {
		AT_Audit::create( 'add_category', $cat_id, serialize( get_category( $cat_id ) ) );
	}

	function delete_category( $cat_id ) {
		$cat = get_category ($cat_id);
		AT_Audit::create( 'delete_category', $cat_id, $cat->cat_nicename );
	}

	function user_register( $user_id ) {
		AT_Audit::create( 'user_register', $user_id, serialize( get_userdata( $user_id ) ) );
	}

	function profile_update( $user_id ) {
		AT_Audit::create( 'profile_update', $user_id, serialize( get_userdata( $user_id ) ) );
	}

	function delete_user( $user_id ) {
		$user = get_userdata ($user_id);
		AT_Audit::create( 'delete_user', $user_id, $user->user_nicename );
	}

	function retrieve_password( $name ) {
		AT_Audit::create( 'retrieve_password', '', $name );
	}

	function delete_comment( $comment_id ) {
		AT_Audit::create( 'delete_comment', $comment_id );
	}

	function edit_comment( $comment_id ) {
		global $wpdb;

		$comment = $wpdb->get_row( $wpdb->prepare( "SELECT * FROM {$wpdb->comments} WHERE comment_ID=%d", $comment_id ) );
		if ( $comment )
			AT_Audit::create( 'edit_comment', $comment_id, serialize( $comment ) );
	}

	function delete_attachment( $postid ) {
		// 在删除前获取附件信息
		$post = get_post($postid);
		if ($post) {
			// 获取附件文件路径
			$file = get_attached_file($postid);
			$filename = basename($file);
			
			// 创建审计记录，包含文件名和标题
			$data = array(
				'filename' => $filename,
				'title' => $post->post_title,
				'mimetype' => $post->post_mime_type
			);
			AT_Audit::create('delete_attachment', $postid, json_encode($data), $post->post_title);
		} else {
			// 如果无法获取附件信息，只记录ID
			AT_Audit::create('delete_attachment', $postid);
		}
	}

	function add_attachment( $postid ) {
		global $wpdb;

		$attach = $wpdb->get_row( $wpdb->prepare( "SELECT * FROM {$wpdb->postmeta} WHERE post_id=%d AND meta_key='_wp_attached_file' ORDER BY meta_id DESC LIMIT 1", $postid ) );
		AT_Audit::create( 'add_attachment', $postid, $attach->meta_value );
	}

	function edit_attachment( $postid ) {
		global $wpdb;

		$attach = $wpdb->get_row( $wpdb->prepare( "SELECT * FROM {$wpdb->postmeta} WHERE post_id=%d AND meta_key='_wp_attached_file' ORDER BY meta_id DESC LIMIT 1", $postid ) );
		AT_Audit::create( 'edit_attachment', $postid, $attach->meta_value );
	}

	/**
	 * 优化版页面访问记录函数
	 * 支持高流量站点的性能优化
	 */
	function template_redirect() {
		// 不记录404页面
		if (is_404()) {
			return;
		}
		
		// 不记录预览
		if (isset($_GET['preview']) && $_GET['preview'] == 'true') {
			return;
		}
		
		global $post, $posts, $user_ID;
		
		// 获取请求URI
		$request_uri = isset($_SERVER['REQUEST_URI']) ? $_SERVER['REQUEST_URI'] : '';
		
		// 通过AT_Audit类的should_log_operation方法检查是否应记录此访问
		if (!AT_Audit::should_log_operation('template_redirect', $request_uri)) {
			return;
		}
		
		// 收集访问数据
		$current_user_id = get_current_user_id();
		$post_id = count($posts) > 1 ? 0 : (isset($post->ID) ? $post->ID : 0);
		$title = isset($post->post_title) ? $post->post_title : $request_uri;
		
		// 构建日志数据
		$data = array(
			'url' => $request_uri,
			'user_agent' => isset($_SERVER['HTTP_USER_AGENT']) ? $_SERVER['HTTP_USER_AGENT'] : '',
			'referer' => isset($_SERVER['HTTP_REFERER']) ? $_SERVER['HTTP_REFERER'] : '',
		);
		
		// 区分访客和登录用户
		if ($current_user_id === 0) {
			$data['user_type'] = 'visitor';
		} else {
			$user = get_userdata($current_user_id);
			$data['user_type'] = 'logged_in';
			$data['user_login'] = $user ? $user->user_login : '未知用户';
		}
		
		// 根据高流量模式配置决定记录方式
		if (get_option('audit_high_volume_mode', false) && class_exists('AuditTrailBatchLogger')) {
			// 使用批量记录器
			AuditTrailBatchLogger::add_page_view($post_id, $data, $title, $current_user_id);
		} else {
			// 直接记录
			AT_Audit::create('template_redirect', $post_id, $data, $title, $current_user_id);
		}
	}

	private function render( $template, $template_vars = array() ) {
		foreach ( $template_vars AS $key => $val ) {
			$$key = $val;
		}

		if ( file_exists( dirname( __FILE__ )."/view/admin/$template.php" ) )
			include dirname( __FILE__ )."/view/admin/$template.php";
	}

	private function capture( $ug_name, $ug_vars = array() ) {
		ob_start();

		$this->render( $ug_name, $ug_vars );
		$output = ob_get_contents();

		ob_end_clean();
		return $output;
	}

	function column_user_id( $item ){
		// 处理template_redirect操作的访客显示
		if ($item->operation == 'template_redirect') {
			$data = json_decode($item->data);
			if (is_object($data) && isset($data->user_type) && $data->user_type == 'visitor') {
				return '<span class="visitor-label">访客</span>';
			} else if (is_object($data) && isset($data->user_login)) {
				// 显示数据中记录的用户名
				if ($item->user_id > 0) {
					return '<a href="user-edit.php?user_id='.esc_attr($item->user_id).'&amp;wp_http_referer=%2Fsite%2Fwp-admin%2Fusers.php">'.esc_html($data->user_login).'</a>';
				} else {
					return esc_html($data->user_login);
				}
			}
		}
		
		// 常规用户处理
		if ( $item->user_id > 0 )
			return '<a href="user-edit.php?user_id='.esc_attr( $item->user_id ).'&amp;wp_http_referer=%2Fsite%2Fwp-admin%2Fusers.php">'.esc_html( $item->username ).'</a>';
		return '';
	}

	/**
	 * 监听Elementor保存编辑器内容的钩子
	 */
	function elementor_editor_after_save( $post_id, $editor_data = null ) {
		$post = get_post( $post_id );
		
		if ( $post && $post->post_status !== 'inherit' ) {
			// 获取Elementor编辑器数据，先检查Elementor类是否存在
			$builder_data = array(
				'builder_type' => 'elementor',
				'editor_data_summary' => 'Elementor编辑器数据(内容过大不记录详情)',
				'edit_time' => current_time('mysql')
			);
			
			// 安全检查Elementor是否激活
			if (class_exists('\Elementor\Plugin') && isset(\Elementor\Plugin::$instance) && is_object(\Elementor\Plugin::$instance) && isset(\Elementor\Plugin::$instance->documents)) {
				try {
					$document = \Elementor\Plugin::$instance->documents->get( $post_id );
					if (is_object($document)) {
						$builder_data['is_built_with_elementor'] = $document->is_built_with_elementor();
					}
				} catch (Exception $e) {
					// 捕获任何可能的异常，避免中断执行
					$builder_data['error'] = '获取Elementor数据时出错';
				}
			}
			
			// 为数据创建一个对象，包含post和builder_data
			$data = clone $post;
			$data->builder_data = $builder_data;
			
			AT_Audit::create( 'elementor/editor/after_save', $post_id, $data, $post->post_title );
		}
	}
	
	/**
	 * 监听Elementor保存文档的钩子
	 */
	function elementor_document_after_save( $document ) {
		// 防止非对象参数导致的错误
		if (!is_object($document)) {
			return;
		}
		
		try {
			$post_id = $document->get_main_id();
			$post = get_post( $post_id );
			
			if ( $post && $post->post_status !== 'inherit' ) {
				$builder_data = array(
					'builder_type' => 'elementor_document',
					'document_type' => get_class($document),
					'edit_time' => current_time('mysql')
				);
				
				// 安全检查方法是否存在
				if (method_exists($document, 'is_built_with_elementor')) {
					$builder_data['is_built_with_elementor'] = $document->is_built_with_elementor();
				}
				
				// 为数据创建一个对象，包含post和builder_data
				$data = clone $post;
				$data->builder_data = $builder_data;
				
				AT_Audit::create( 'elementor/document/after_save', $post_id, $data, $post->post_title );
			}
		} catch (Exception $e) {
			// 捕获任何可能的异常，避免中断执行
			return;
		}
	}

	/**
	 * 监听Elementor编辑器保存前的钩子
	 */
	function elementor_editor_before_save( $post_id, $editor_data = null ) {
		$post = get_post( $post_id );
		
		if ( $post && $post->post_status !== 'inherit' ) {
			// 获取Elementor编辑器数据，先检查Elementor类是否存在
			$builder_data = array(
				'builder_type' => 'elementor',
				'editor_data_summary' => 'Elementor编辑器数据(内容过大不记录详情)',
				'edit_time' => current_time('mysql')
			);
			
			// 安全检查Elementor是否激活
			if (class_exists('\Elementor\Plugin') && isset(\Elementor\Plugin::$instance) && is_object(\Elementor\Plugin::$instance) && isset(\Elementor\Plugin::$instance->documents)) {
				try {
					$document = \Elementor\Plugin::$instance->documents->get( $post_id );
					if (is_object($document)) {
						$builder_data['is_built_with_elementor'] = $document->is_built_with_elementor();
					}
				} catch (Exception $e) {
					// 捕获任何可能的异常，避免中断执行
					$builder_data['error'] = '获取Elementor数据时出错';
				}
			}
			
			// 为数据创建一个对象，包含post和builder_data
			$data = clone $post;
			$data->builder_data = $builder_data;
			
			AT_Audit::create( 'elementor/editor/before_save', $post_id, $data, $post->post_title );
		}
	}

	/**
	 * 监听Beaver Builder保存布局的钩子
	 */
	function fl_builder_after_save_layout( $post_id, $layout_data = null ) {
		$post = get_post( $post_id );
		
		if ( $post && $post->post_status !== 'inherit' ) {
			$builder_data = array(
				'builder_type' => 'beaver_builder',
				'layout_data_summary' => 'Beaver Builder布局数据(内容过大不记录详情)',
				'edit_time' => current_time('mysql')
			);
			
			// 为数据创建一个对象，包含post和builder_data
			$data = clone $post;
			$data->builder_data = $builder_data;
			
			AT_Audit::create( 'fl_builder_after_save_layout', $post_id, $data, $post->post_title );
		}
	}
	
	/**
	 * 监听SiteOrigin Page Builder保存的钩子
	 */
	function siteorigin_panels_save_post( $post_id, $panels_data = null ) {
		$post = get_post( $post_id );
		
		if ( $post && $post->post_status !== 'inherit' ) {
			$builder_data = array(
				'builder_type' => 'siteorigin_panels',
				'panels_data_summary' => 'SiteOrigin面板数据(内容过大不记录详情)',
				'edit_time' => current_time('mysql')
			);
			
			// 为数据创建一个对象，包含post和builder_data
			$data = clone $post;
			$data->builder_data = $builder_data;
			
			AT_Audit::create( 'siteorigin_panels_save_post', $post_id, $data, $post->post_title );
		}
	}
	
	/**
	 * 监听WPBakery页面构建器保存的钩子
	 */
	function vc_after_save_post( $post_id ) {
		$post = get_post( $post_id );
		
		if ( $post && $post->post_status !== 'inherit' ) {
			$builder_data = array(
				'builder_type' => 'wpbakery',
				'edit_time' => current_time('mysql')
			);
			
			// 为数据创建一个对象，包含post和builder_data
			$data = clone $post;
			$data->builder_data = $builder_data;
			
			AT_Audit::create( 'vc_after_save_post', $post_id, $data, $post->post_title );
		}
	}
	
	/**
	 * 监听Divi Builder保存布局的钩子
	 */
	function et_fb_save_layout( $post_id ) {
		$post = get_post( $post_id );
		
		if ( $post && $post->post_status !== 'inherit' ) {
			$builder_data = array(
				'builder_type' => 'divi_builder',
				'edit_time' => current_time('mysql')
			);
			
			// 为数据创建一个对象，包含post和builder_data
			$data = clone $post;
			$data->builder_data = $builder_data;
			
			AT_Audit::create( 'et_fb_save_layout', $post_id, $data, $post->post_title );
		}
	}
	
	/**
	 * 监听Gutenberg块编辑的钩子
	 */
	function blocks_parsed( $blocks, $source_content = null ) {
		// 只在保存文章时处理，避免在加载页面时重复记录
		if ( !defined( 'DOING_AUTOSAVE' ) && !wp_is_json_request() && !wp_doing_ajax() && !defined( 'REST_REQUEST' ) ) {
			return $blocks;
		}
		
		global $post;
		
		if ( isset($post) && is_object($post) && $post->ID > 0 && $post->post_status !== 'inherit' && $post->post_status !== 'auto-draft' ) {
			// 安全检查函数是否存在
			if ( function_exists('use_block_editor_for_post') && use_block_editor_for_post( $post ) ) {
				$builder_data = array(
					'builder_type' => 'gutenberg',
					'blocks_count' => count($blocks),
					'edit_time' => current_time('mysql')
				);
				
				// 为数据创建一个对象，包含post和builder_data
				$data = clone $post;
				$data->builder_data = $builder_data;
				
				AT_Audit::create( 'blocks_parsed', $post->ID, $data, $post->post_title );
			}
		}
		
		return $blocks;
	}
	
	/**
	 * 监听SiteOrigin面板渲染后的钩子
	 */
	function siteorigin_panels_after_render( $post_id ) {
		$post = get_post( $post_id );
		
		if ( $post && $post->post_status !== 'inherit' ) {
			$builder_data = array(
				'builder_type' => 'siteorigin_panels',
				'action_type' => 'after_render',
				'edit_time' => current_time('mysql')
			);
			
			// 为数据创建一个对象，包含post和builder_data
			$data = clone $post;
			$data->builder_data = $builder_data;
			
			AT_Audit::create( 'siteorigin_panels_after_render', $post_id, $data, $post->post_title );
		}
	}
	
	/**
	 * 监听WPBakery页面构建器保存前的钩子
	 */
	function vc_before_save_post( $post_id ) {
		$post = get_post( $post_id );
		
		if ( $post && $post->post_status !== 'inherit' ) {
			$builder_data = array(
				'builder_type' => 'wpbakery',
				'action_type' => 'before_save',
				'edit_time' => current_time('mysql')
			);
			
			// 为数据创建一个对象，包含post和builder_data
			$data = clone $post;
			$data->builder_data = $builder_data;
			
			AT_Audit::create( 'vc_before_save_post', $post_id, $data, $post->post_title );
		}
	}
	
	/**
	 * 监听WPBakery页面构建器更新后的钩子
	 */
	function vc_after_update( $post_id ) {
		$post = get_post( $post_id );
		
		if ( $post && $post->post_status !== 'inherit' ) {
			$builder_data = array(
				'builder_type' => 'wpbakery',
				'action_type' => 'after_update',
				'edit_time' => current_time('mysql')
			);
			
			// 为数据创建一个对象，包含post和builder_data
			$data = clone $post;
			$data->builder_data = $builder_data;
			
			AT_Audit::create( 'vc_after_update', $post_id, $data, $post->post_title );
		}
	}
	
	/**
	 * 监听Divi Builder Ajax保存的钩子
	 */
	function et_fb_ajax_save( $post_id ) {
		$post = get_post( $post_id );
		
		if ( $post && $post->post_status !== 'inherit' ) {
			$builder_data = array(
				'builder_type' => 'divi_builder',
				'action_type' => 'ajax_save',
				'edit_time' => current_time('mysql')
			);
			
			// 为数据创建一个对象，包含post和builder_data
			$data = clone $post;
			$data->builder_data = $builder_data;
			
			AT_Audit::create( 'et_fb_ajax_save', $post_id, $data, $post->post_title );
		}
	}
	
	/**
	 * 监听Divi Builder保存布局后的钩子
	 */
	function et_builder_after_save_layout( $post_id ) {
		$post = get_post( $post_id );
		
		if ( $post && $post->post_status !== 'inherit' ) {
			$builder_data = array(
				'builder_type' => 'divi_builder',
				'action_type' => 'after_save_layout',
				'edit_time' => current_time('mysql')
			);
			
			// 为数据创建一个对象，包含post和builder_data
			$data = clone $post;
			$data->builder_data = $builder_data;
			
			AT_Audit::create( 'et_builder_after_save_layout', $post_id, $data, $post->post_title );
		}
	}

	/**
	 * 注册页面构建器相关钩子
	 */
	function register_pagebuilder_hooks() {
		// 不再需要直接注册这些钩子，它们已经通过audit_listen函数注册
		// 保留此函数以保证向后兼容性
		return;
	}

	/**
	 * 监听Beaver Builder保存布局前的钩子
	 */
	function fl_builder_before_save_layout( $post_id, $layout_data = null ) {
		$post = get_post( $post_id );
		
		if ( $post && $post->post_status !== 'inherit' ) {
			$builder_data = array(
				'builder_type' => 'beaver_builder',
				'action_type' => 'before_save',
				'layout_data_summary' => 'Beaver Builder布局数据(内容过大不记录详情)',
				'edit_time' => current_time('mysql')
			);
			
			// 为数据创建一个对象，包含post和builder_data
			$data = clone $post;
			$data->builder_data = $builder_data;
			
			AT_Audit::create( 'fl_builder_before_save_layout', $post_id, $data, $post->post_title );
		}
	}
	
	/**
	 * 监听Beaver Builder渲染布局后的钩子
	 */
	function fl_builder_after_layout_rendered( $post_id, $layout_data = null ) {
		$post = get_post( $post_id );
		
		if ( $post && $post->post_status !== 'inherit' ) {
			$builder_data = array(
				'builder_type' => 'beaver_builder',
				'action_type' => 'layout_rendered',
				'layout_data_summary' => 'Beaver Builder布局渲染(内容过大不记录详情)',
				'edit_time' => current_time('mysql')
			);
			
			// 为数据创建一个对象，包含post和builder_data
			$data = clone $post;
			$data->builder_data = $builder_data;
			
			AT_Audit::create( 'fl_builder_after_layout_rendered', $post_id, $data, $post->post_title );
		}
	}

	/**
	 * 跟踪渲染的Gutenberg块
	 */
	function track_rendered_block( $block_content, $block ) {
		// 不在前台记录块渲染，只在管理界面记录
		if (!is_admin() || !current_user_can('edit_posts')) {
			return $block_content;
		}
		
		global $post;
		
		if ( isset($post) && is_object($post) && $post->ID > 0 && 
			$post->post_status !== 'inherit' && $post->post_status !== 'auto-draft' ) {
			
			// 只跟踪核心块，避免记录太多细节
			if (isset($block['blockName']) && strpos($block['blockName'], 'core/') === 0) {
				$builder_data = array(
					'builder_type' => 'gutenberg',
					'action_type' => 'render_block',
					'block_name' => $block['blockName'],
					'edit_time' => current_time('mysql')
				);
				
				// 使用静态变量避免重复记录同一页面的块
				static $tracked_posts = array();
				
				if (!isset($tracked_posts[$post->ID])) {
					$tracked_posts[$post->ID] = true;
					
					// 为数据创建一个对象，包含post和builder_data
					$data = clone $post;
					$data->builder_data = $builder_data;
					
					AT_Audit::create( 'gutenberg_render_block', $post->ID, $data, $post->post_title );
				}
			}
		}
		
		return $block_content;
	}
}
