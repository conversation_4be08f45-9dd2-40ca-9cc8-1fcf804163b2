{"log": {"loglevel": "warning"}, "inbounds": [{"tag": "socks10829", "port": 10829, "listen": "127.0.0.1", "protocol": "socks"}], "outbounds": [{"tag": "proxy10829", "protocol": "vmess", "settings": {"vnext": [{"address": "c21s4.portablesubmarines.com", "port": 19965, "users": [{"id": "e6aa1800-17c7-4444-ac6b-209d20d8a100", "alterId": 0, "email": "<EMAIL>", "security": "auto"}]}]}, "streamSettings": {"network": "tcp"}, "mux": {"enabled": false, "concurrency": -1}}], "routing": {"domainStrategy": "IPIfNonMatch", "rules": [{"type": "field", "inboundTag": ["socks10829"], "outboundTag": "proxy10829"}]}}