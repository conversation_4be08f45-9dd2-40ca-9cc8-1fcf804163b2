<?php

public function render_admin_page() {
    // 处理清除统计数据的请求
    if (isset($_POST['clear_404_stats']) && check_admin_referer('clear_404_stats')) {
        delete_option('audit_404_ip_stats');
        delete_transient('audit_404_ip_stats');
        echo '<div class="notice notice-success"><p>404统计数据已清除</p></div>';
    }
    
    // 获取统计数据
    $stats = get_option('audit_404_ip_stats', array());
    if (empty($stats)) {
        // 尝试从transient获取临时数据
        $stats = get_transient('audit_404_ip_stats');
        if (empty($stats)) {
            echo '<div class="notice notice-info"><p>暂无404统计数据。</p></div>';
            return;
        }
    }
} 