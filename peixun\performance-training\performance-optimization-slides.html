<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>网站性能优化培训 - 让你的网站飞起来</title>
    <script src="https://unpkg.com/lucide@latest/dist/umd/lucide.js"></script>
    <style>
        :root {
            --primary-color: #6366f1;
            --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            --secondary-color: #8b5cf6;
            --accent-color: #06d6a0;
            --success-color: #10b981;
            --warning-color: #f59e0b;
            --danger-color: #ef4444;
            --text-dark: #1f2937;
            --text-light: #6b7280;
            --white: #ffffff;
            --bg-light: #f8fafc;
            --purple-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            --orange-gradient: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
            --success-gradient: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
            --warning-gradient: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', 'PingFang SC', 'Helvetica Neue', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            overflow: hidden;
        }

        .slideshow-container {
            position: relative;
            width: 100%;
            height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .slide {
            display: none;
            width: 90vw;
            max-width: 1200px;
            height: 85vh;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            overflow-y: auto;
        }

        .slide.active {
            display: block;
            animation: slideIn 0.6s ease-out;
        }

        @keyframes slideIn {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .slide-header {
            display: flex;
            align-items: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid var(--bg-light);
        }

        .slide-number {
            background: var(--primary-gradient);
            color: var(--white);
            width: 50px;
            height: 50px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            font-size: 1.2em;
            margin-right: 20px;
        }

        .slide-title {
            color: var(--text-dark);
            font-size: 2.5em;
            font-weight: 700;
            flex: 1;
        }

        .slide-content {
            height: calc(100% - 120px);
        }

        .content-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 40px;
            height: 100%;
        }

        .text-section {
            display: flex;
            flex-direction: column;
            justify-content: center;
        }

        .visual-section {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
        }

        .animated-diagram {
            position: relative;
            width: 100%;
            height: 350px;
            background: linear-gradient(135deg, #f0f9ff, #e0f2fe);
            border-radius: 15px;
            border: 2px solid #bfdbfe;
            margin-bottom: 20px;
            overflow: hidden;
        }

        .diagram-element {
            position: absolute;
            background: var(--primary-gradient);
            color: var(--white);
            padding: 15px;
            border-radius: 12px;
            text-align: center;
            font-weight: 600;
            box-shadow: 0 8px 25px rgba(99, 102, 241, 0.3);
            transition: all 0.3s ease;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            gap: 5px;
        }

        .diagram-element.highlight {
            background: var(--accent-color);
            transform: scale(1.05);
            box-shadow: 0 12px 35px rgba(6, 214, 160, 0.4);
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1.05); }
            50% { transform: scale(1.1); }
        }

        .feature-box {
            background: rgba(255, 255, 255, 0.9);
            padding: 20px;
            border-radius: 12px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
            text-align: center;
            border: 1px solid rgba(99, 102, 241, 0.2);
        }

        .feature-box h3 {
            color: var(--primary-color);
            margin-bottom: 10px;
            font-size: 1.2em;
        }

        .feature-box p {
            color: var(--text-light);
            line-height: 1.5;
        }

        .comparison-container {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-bottom: 20px;
        }

        .comparison-box {
            padding: 20px;
            border-radius: 12px;
            text-align: center;
            color: var(--white);
            font-weight: 600;
        }

        .comparison-box.before {
            background: linear-gradient(135deg, #ff6b6b, #ee5a24);
        }

        .comparison-box.after {
            background: linear-gradient(135deg, #4ecdc4, #44bd87);
        }

        .interactive-btn {
            background: var(--primary-gradient);
            color: var(--white);
            border: none;
            padding: 15px 30px;
            border-radius: 25px;
            font-size: 1.1em;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(99, 102, 241, 0.3);
            margin-top: 20px;
        }

        .interactive-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(99, 102, 241, 0.4);
        }

        .navigation {
            position: fixed;
            bottom: 30px;
            left: 50%;
            transform: translateX(-50%);
            display: flex;
            gap: 15px;
            z-index: 1000;
        }

        .nav-btn {
            background: rgba(255, 255, 255, 0.9);
            color: var(--primary-color);
            border: none;
            padding: 12px 20px;
            border-radius: 25px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(99, 102, 241, 0.2);
        }

        .nav-btn:hover {
            background: var(--primary-gradient);
            color: var(--white);
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(99, 102, 241, 0.3);
        }

        .progress-bar {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 4px;
            background: rgba(255,255,255,0.3);
            z-index: 1000;
        }

        .progress-fill {
            height: 100%;
            background: var(--accent-color);
            transition: width 0.3s ease;
        }

        .slide-counter {
            position: fixed;
            top: 30px;
            right: 30px;
            background: rgba(255,255,255,0.9);
            padding: 10px 20px;
            border-radius: 20px;
            font-weight: 600;
            color: var(--primary-color);
            z-index: 1000;
        }

        /* 全屏时隐藏导航元素 */
        :fullscreen .navigation,
        :fullscreen .slide-counter,
        :fullscreen .progress-bar {
            display: none !important;
        }

        /* 兼容性支持 */
        :-webkit-full-screen .navigation,
        :-webkit-full-screen .slide-counter,
        :-webkit-full-screen .progress-bar {
            display: none !important;
        }

        :-moz-full-screen .navigation,
        :-moz-full-screen .slide-counter,
        :-moz-full-screen .progress-bar {
            display: none !important;
        }

        :-ms-fullscreen .navigation,
        :-ms-fullscreen .slide-counter,
        :-ms-fullscreen .progress-bar {
            display: none !important;
        }

        /* 流动连接线 */
        .flow-line {
            position: absolute;
            background: linear-gradient(90deg, transparent, #06d6a0, #4facfe, transparent);
            background-size: 200% 100%;
            border-radius: 1px;
            z-index: 15;
            animation: flowMove 2s ease-in-out infinite;
        }

        @keyframes flowMove {
            0% {
                background-position: -200% 0;
                opacity: 0.6;
            }
            50% {
                background-position: 0% 0;
                opacity: 1;
            }
            100% {
                background-position: 200% 0;
                opacity: 0.6;
            }
        }

        /* 连接脉冲动画 */
        .connection-pulse {
            animation: pulseRing 2s ease-in-out infinite;
        }

        @keyframes pulseRing {
            0% {
                transform: scale(0.8);
                opacity: 0.8;
            }
            50% {
                transform: scale(1.2);
                opacity: 0.4;
                box-shadow: 0 0 20px rgba(6, 214, 160, 0.6);
            }
            100% {
                transform: scale(0.8);
                opacity: 0.8;
            }
        }

        .fade-in-left {
            animation: fadeInLeft 0.8s ease;
        }

        @keyframes fadeInLeft {
            from {
                opacity: 0;
                transform: translateX(-50px);
            }
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }

        .fade-in-right {
            animation: fadeInRight 0.8s ease;
        }

        @keyframes fadeInRight {
            from {
                opacity: 0;
                transform: translateX(50px);
            }
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }

        .bounce-in {
            animation: bounceIn 1s ease;
        }

        @keyframes bounceIn {
            0% {
                opacity: 0;
                transform: scale(0.3);
            }
            50% {
                opacity: 1;
                transform: scale(1.05);
            }
            70% {
                transform: scale(0.9);
            }
            100% {
                opacity: 1;
                transform: scale(1);
            }
        }

        @keyframes shake {
            0%, 100% { transform: translateX(0); }
            25% { transform: translateX(-5px); }
            75% { transform: translateX(5px); }
        }
    </style>
</head>
<body>
    <!-- 进度条 -->
    <div class="progress-bar">
        <div class="progress-fill" id="progress-fill"></div>
    </div>

    <!-- 幻灯片计数器 -->
    <div class="slide-counter">
        <span id="current-slide">1</span> / <span id="total-slides">12</span>
    </div>

    <div class="slideshow-container">
        <!-- 幻灯片 1: 欢迎页面 -->
        <div class="slide active" id="slide-1">
            <div class="slide-header">
                <div class="slide-number">01</div>
                <h1 class="slide-title bounce-in" style="display: flex; align-items: center; justify-content: center; gap: 15px;">
                    <i data-lucide="zap" style="width: 48px; height: 48px; color: #6366f1;"></i>
                    网站性能优化入门
                </h1>
            </div>
            <div class="slide-content">
                <div class="content-grid">
                    <div class="text-section fade-in-left">
                        <h2 style="color: var(--primary-color); margin-bottom: 30px;">WordPress建站性能优化实战！</h2>
                        <p style="font-size: 1.3em; line-height: 1.6; color: var(--text-dark); margin-bottom: 20px;">
                            🎯 <strong>学习目标：</strong>掌握从建站到上线的完整性能优化流程
                        </p>
                        <p style="font-size: 1.2em; line-height: 1.6; color: var(--text-light); margin-bottom: 30px;">
                            结合我们公司实际建站流程，学会在每个环节进行性能优化
                        </p>
                        <div style="margin-top: 20px; padding: 15px; background: rgba(255,255,255,0.1); border-radius: 12px; backdrop-filter: blur(10px);">
                            <h4 style="color: var(--primary-color); margin-bottom: 10px; display: flex; align-items: center; gap: 8px;">
                                <i data-lucide="info" style="width: 16px; height: 16px;"></i>
                                我们的建站流程
                            </h4>
                            <p style="font-size: 12px; color: var(--text-light); line-height: 1.4;">
                                🔧 测试环境：preview.yhct.site (香港服务器)<br>
                                🌐 Nginx代理：https://preview.yhct.site/site1/<br>
                                🚀 上线流程：域名解析 → 换链接 → 插件优化 → 性能检测
                            </p>
                        </div>
                    </div>
                    <div class="visual-section fade-in-right">
                        <div class="animated-diagram">
                            <!-- 慢网站 -->
                            <div class="diagram-element" style="top: 30px; left: 30px; width: 120px; height: 80px; background: var(--danger-color);">
                                <i data-lucide="turtle" style="width: 24px; height: 24px; color: white;"></i>
                                <span style="color: white; font-weight: 600; font-size: 14px;">慢网站</span>
                                <small style="color: rgba(255,255,255,0.9);">用户流失</small>
                            </div>
                            
                            <!-- 优化过程 -->
                            <div class="diagram-element highlight" style="top: 50%; left: 50%; transform: translate(-50%, -50%); width: 140px; height: 90px;">
                                <i data-lucide="zap" style="width: 32px; height: 32px; color: white;"></i>
                                <span style="color: white; font-weight: 700; font-size: 16px;">性能优化</span>
                                <small style="color: rgba(255,255,255,0.9);">魔法变身</small>
                            </div>

                            <!-- 快网站 -->
                            <div class="diagram-element" style="top: 30px; right: 30px; width: 120px; height: 80px; background: var(--success-color);">
                                <i data-lucide="rocket" style="width: 24px; height: 24px; color: white;"></i>
                                <span style="color: white; font-weight: 600; font-size: 14px;">快网站</span>
                                <small style="color: rgba(255,255,255,0.9);">用户喜爱</small>
                            </div>

                            <!-- 连接线 -->
                            <div class="flow-line" style="top: 50%; left: 25%; width: 20%; height: 3px; transform: translateY(-50%);"></div>
                            <div class="flow-line" style="top: 50%; right: 25%; width: 20%; height: 3px; transform: translateY(-50%);"></div>
                        </div>
                        <div class="feature-box">
                            <h3>🎯 课程收获</h3>
                            <p>掌握性能优化核心原理，学会使用Google PageSpeed工具</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 幻灯片 2: 我们的建站流程 -->
        <div class="slide" id="slide-2">
            <div class="slide-header">
                <div class="slide-number">02</div>
                <h1 class="slide-title" style="display: flex; align-items: center; justify-content: center; gap: 15px;">
                    <i data-lucide="workflow" style="width: 48px; height: 48px; color: #6366f1;"></i>
                    WordPress建站到上线流程
                </h1>
            </div>
            <div class="slide-content">
                <div class="content-grid">
                    <div class="text-section fade-in-left">
                        <h2 style="color: var(--primary-color); margin-bottom: 30px;">从测试到上线的完整流程</h2>
                        <div style="font-size: 1.1em; line-height: 1.8; color: var(--text-dark);">

                            <div style="margin-bottom: 25px; padding: 15px; background: rgba(99, 102, 241, 0.1); border-radius: 10px;">
                                <h4 style="color: var(--primary-color); margin-bottom: 10px;">🔧 测试环境搭建</h4>
                                <ul style="margin-left: 15px; font-size: 0.95em;">
                                    <li><strong>香港服务器：</strong>preview.yhct.site</li>
                                    <li><strong>Nginx代理：</strong>统一SSL证书管理</li>
                                    <li><strong>站点路径：</strong>/site1/, /site2/, /site3/...</li>
                                    <li><strong>优势：</strong>无需每次申请SSL，提高效率</li>
                                </ul>
                            </div>

                            <div style="margin-bottom: 25px; padding: 15px; background: rgba(6, 214, 160, 0.1); border-radius: 10px;">
                                <h4 style="color: var(--accent-color); margin-bottom: 10px;">🌐 域名解析流程</h4>
                                <ul style="margin-left: 15px; font-size: 0.95em;">
                                    <li><strong>第1步：</strong>登录客户阿里云账号（手机验证）</li>
                                    <li><strong>第2步：</strong>检查域名实名认证状态</li>
                                    <li><strong>第3步：</strong>确认DNS服务商（阿里云/Cloudflare）</li>
                                    <li><strong>第4步：</strong>添加/修改A记录解析</li>
                                </ul>
                            </div>

                            <div style="margin-bottom: 25px; padding: 15px; background: rgba(245, 158, 11, 0.1); border-radius: 10px;">
                                <h4 style="color: var(--warning-color); margin-bottom: 10px;">⚡ 上线后优化</h4>
                                <ul style="margin-left: 15px; font-size: 0.95em;">
                                    <li><strong>换链接：</strong>测试域名 → 正式域名</li>
                                    <li><strong>插件安装：</strong>WebP Express + Lazy Load</li>
                                    <li><strong>性能检测：</strong>Google PageSpeed测试</li>
                                    <li><strong>持续优化：</strong>根据检测结果调整</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                    <div class="visual-section fade-in-right">
                        <div class="animated-diagram">
                            <!-- 建站流程图 -->
                            <div style="position: absolute; top: 20px; left: 20px; right: 20px; height: 300px; background: white; border-radius: 10px; padding: 20px; border: 1px solid #e5e7eb;">
                                <h4 style="text-align: center; margin-bottom: 20px; color: var(--primary-color);">建站到上线完整流程</h4>

                                <!-- 流程步骤 -->
                                <div style="display: flex; flex-direction: column; gap: 15px; height: 100%;">
                                    <!-- 测试环境 -->
                                    <div style="display: flex; align-items: center; gap: 15px; padding: 10px; background: #f8fafc; border-radius: 8px;">
                                        <div style="width: 30px; height: 30px; background: var(--primary-color); border-radius: 50%; display: flex; align-items: center; justify-content: center; color: white; font-weight: 600; font-size: 12px;">1</div>
                                        <div style="flex: 1;">
                                            <div style="font-weight: 600; color: var(--primary-color); font-size: 14px;">测试环境开发</div>
                                            <div style="font-size: 12px; color: var(--text-light);">preview.yhct.site/site1/</div>
                                        </div>
                                        <div style="font-size: 20px;">🔧</div>
                                    </div>

                                    <!-- 域名解析 -->
                                    <div style="display: flex; align-items: center; gap: 15px; padding: 10px; background: #f0fdf4; border-radius: 8px;">
                                        <div style="width: 30px; height: 30px; background: var(--success-color); border-radius: 50%; display: flex; align-items: center; justify-content: center; color: white; font-weight: 600; font-size: 12px;">2</div>
                                        <div style="flex: 1;">
                                            <div style="font-weight: 600; color: var(--success-color); font-size: 14px;">域名解析配置</div>
                                            <div style="font-size: 12px; color: var(--text-light);">阿里云/Cloudflare DNS</div>
                                        </div>
                                        <div style="font-size: 20px;">🌐</div>
                                    </div>

                                    <!-- 换链接 -->
                                    <div style="display: flex; align-items: center; gap: 15px; padding: 10px; background: #fefce8; border-radius: 8px;">
                                        <div style="width: 30px; height: 30px; background: var(--warning-color); border-radius: 50%; display: flex; align-items: center; justify-content: center; color: white; font-weight: 600; font-size: 12px;">3</div>
                                        <div style="flex: 1;">
                                            <div style="font-weight: 600; color: var(--warning-color); font-size: 14px;">换链接上线</div>
                                            <div style="font-size: 12px; color: var(--text-light);">测试域名 → 正式域名</div>
                                        </div>
                                        <div style="font-size: 20px;">🔗</div>
                                    </div>

                                    <!-- 性能优化 -->
                                    <div style="display: flex; align-items: center; gap: 15px; padding: 10px; background: #f0f9ff; border-radius: 8px;">
                                        <div style="width: 30px; height: 30px; background: var(--accent-color); border-radius: 50%; display: flex; align-items: center; justify-content: center; color: white; font-weight: 600; font-size: 12px;">4</div>
                                        <div style="flex: 1;">
                                            <div style="font-weight: 600; color: var(--accent-color); font-size: 14px;">性能优化</div>
                                            <div style="font-size: 12px; color: var(--text-light);">插件安装 + PageSpeed检测</div>
                                        </div>
                                        <div style="font-size: 20px;">⚡</div>
                                    </div>
                                </div>
                            </div>

                            <!-- 关键提示 -->
                            <div style="position: absolute; bottom: 20px; left: 20px; right: 20px; background: var(--bg-light); border-radius: 10px; padding: 15px;">
                                <h4 style="color: var(--primary-color); margin-bottom: 10px; text-align: center;">⚡ 效率提升关键</h4>
                                <div style="font-size: 12px; text-align: center; color: var(--text-dark);">
                                    Nginx统一代理 → 无需重复申请SSL → 10分钟内完成解析生效
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 幻灯片 3: Google PageSpeed是什么 -->
        <div class="slide" id="slide-3">
            <div class="slide-header">
                <div class="slide-number">03</div>
                <h1 class="slide-title" style="display: flex; align-items: center; justify-content: center; gap: 15px;">
                    <i data-lucide="search" style="width: 48px; height: 48px; color: #6366f1;"></i>
                    Google PageSpeed - 网站体检师
                </h1>
            </div>
            <div class="slide-content">
                <div class="content-grid">
                    <div class="text-section fade-in-left">
                        <h2 style="color: var(--primary-color); margin-bottom: 30px;">想象一个专业的汽车检测站</h2>
                        <div style="font-size: 1.2em; line-height: 1.8; color: var(--text-dark);">
                            <p style="margin-bottom: 20px;">🚗 <strong>汽车检测站：</strong>检查车辆性能、安全、排放</p>
                            <p style="margin-bottom: 20px;">🔍 <strong>PageSpeed：</strong>检查网站速度、用户体验、SEO</p>
                            <p style="margin-bottom: 30px;">📊 <strong>检测报告包含：</strong></p>
                            <ul style="list-style: none; padding-left: 0;">
                                <li style="margin: 15px 0;">⚡ <strong>性能分数</strong> - 0-100分，越高越好</li>
                                <li style="margin: 15px 0;">📱 <strong>移动端/桌面端</strong> - 分别测试</li>
                                <li style="margin: 15px 0;">🎯 <strong>核心指标</strong> - LCP、FID、CLS</li>
                                <li style="margin: 15px 0;">💡 <strong>优化建议</strong> - 具体改进方案</li>
                            </ul>
                        </div>
                        <button class="interactive-btn" onclick="showPageSpeedDemo()" style="display: flex; align-items: center; gap: 8px; justify-content: center;">
                            <i data-lucide="activity" style="width: 20px; height: 20px;"></i>
                            看看PageSpeed怎么工作
                        </button>
                    </div>
                    <div class="visual-section fade-in-right">
                        <div class="comparison-container">
                            <div class="comparison-box before">
                                <h3 style="display: flex; align-items: center; gap: 10px; justify-content: center;">
                                    <i data-lucide="alert-triangle" style="width: 24px; height: 24px;"></i>
                                    检测前
                                </h3>
                                <div style="margin: 20px 0;">
                                    <div style="font-size: 2em; margin-bottom: 10px;">❓</div>
                                    <p>不知道网站快慢</p>
                                    <p>用户抱怨加载慢</p>
                                    <p>搜索排名下降</p>
                                </div>
                            </div>
                            <div class="comparison-box after">
                                <h3 style="display: flex; align-items: center; gap: 10px; justify-content: center;">
                                    <i data-lucide="check-circle" style="width: 24px; height: 24px;"></i>
                                    检测后
                                </h3>
                                <div style="margin: 20px 0;">
                                    <div style="font-size: 2em; margin-bottom: 10px;">📊</div>
                                    <p>精确性能数据</p>
                                    <p>明确优化方向</p>
                                    <p>持续监控改进</p>
                                </div>
                            </div>
                        </div>
                        <div class="feature-box">
                            <h3 style="display: flex; align-items: center; gap: 10px; justify-content: center;">
                                <i data-lucide="target" style="width: 24px; height: 24px;"></i>
                                PageSpeed评分标准
                            </h3>
                            <div style="text-align: left; margin-top: 15px;">
                                <p style="color: #10b981; margin: 5px 0;">🟢 90-100分：优秀</p>
                                <p style="color: #f59e0b; margin: 5px 0;">🟡 50-89分：需要改进</p>
                                <p style="color: #ef4444; margin: 5px 0;">🔴 0-49分：较差</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 幻灯片 4: LCP - 最大内容绘制 -->
        <div class="slide" id="slide-4">
            <div class="slide-header">
                <div class="slide-number">04</div>
                <h1 class="slide-title" style="display: flex; align-items: center; justify-content: center; gap: 15px;">
                    <i data-lucide="image" style="width: 48px; height: 48px; color: #6366f1;"></i>
                    LCP - 最大内容绘制
                </h1>
            </div>
            <div class="slide-content">
                <div class="content-grid">
                    <div class="text-section fade-in-left">
                        <h2 style="color: var(--primary-color); margin-bottom: 30px;">就像看电影的第一印象</h2>
                        <div style="font-size: 1.2em; line-height: 1.8; color: var(--text-dark);">
                            <p style="margin-bottom: 20px;">🎬 <strong>电影院体验：</strong></p>
                            <ul style="margin-left: 20px; margin-bottom: 20px;">
                                <li>进入影厅 → 打开网页</li>
                                <li>屏幕亮起 → 页面开始加载</li>
                                <li>主要画面出现 → LCP完成</li>
                            </ul>

                            <p style="margin-bottom: 20px;">📏 <strong>LCP测量什么：</strong></p>
                            <ul style="margin-left: 20px; margin-bottom: 20px;">
                                <li>页面最大的图片</li>
                                <li>最大的文本块</li>
                                <li>最大的视频元素</li>
                            </ul>

                            <p style="margin-bottom: 20px;">⏱️ <strong>标准时间：</strong></p>
                            <ul style="margin-left: 20px; margin-bottom: 20px;">
                                <li style="color: var(--success-color);">✅ 优秀：2.5秒内</li>
                                <li style="color: var(--warning-color);">⚠️ 需要改进：2.5-4秒</li>
                                <li style="color: var(--danger-color);">❌ 较差：超过4秒</li>
                            </ul>

                            <p style="margin-bottom: 20px;">🚫 <strong>影响LCP的罪魁祸首：</strong></p>
                            <ul style="margin-left: 20px;">
                                <li>📸 <strong>巨大图片</strong> - 5MB的照片要加载很久</li>
                                <li>🌐 <strong>慢服务器</strong> - 服务器在地球另一端</li>
                                <li>📱 <strong>阻塞资源</strong> - CSS/JS文件太大</li>
                                <li>🔤 <strong>网络字体</strong> - 下载字体文件耗时</li>
                            </ul>
                        </div>
                    </div>
                    <div class="visual-section fade-in-right">
                        <div class="animated-diagram">
                            <!-- 时间轴 -->
                            <div style="position: absolute; top: 20px; left: 20px; right: 20px; height: 40px; background: #f3f4f6; border-radius: 20px; display: flex; align-items: center; padding: 0 20px;">
                                <span style="font-weight: 600; color: var(--text-dark);">页面加载时间轴</span>
                            </div>

                            <!-- 加载阶段 -->
                            <div class="diagram-element" style="top: 80px; left: 30px; width: 100px; height: 60px; background: var(--warning-color);">
                                <span style="color: white; font-size: 12px;">0-1秒</span>
                                <span style="color: white; font-size: 10px;">开始加载</span>
                            </div>

                            <div class="diagram-element" style="top: 80px; left: 50%; transform: translateX(-50%); width: 100px; height: 60px; background: var(--accent-color);">
                                <span style="color: white; font-size: 12px;">1-2.5秒</span>
                                <span style="color: white; font-size: 10px;">内容出现</span>
                            </div>

                            <div class="diagram-element highlight" style="top: 80px; right: 30px; width: 100px; height: 60px;">
                                <span style="color: white; font-size: 12px;">2.5秒</span>
                                <span style="color: white; font-size: 10px;">LCP完成</span>
                            </div>

                            <!-- 页面内容示意 -->
                            <div style="position: absolute; bottom: 40px; left: 20px; right: 20px; height: 150px; background: white; border: 2px solid #e5e7eb; border-radius: 10px; padding: 15px;">
                                <div style="background: #f3f4f6; height: 30px; border-radius: 5px; margin-bottom: 10px; display: flex; align-items: center; padding: 0 10px; font-size: 12px; color: var(--text-dark);">
                                    网站标题
                                </div>
                                <div style="background: var(--primary-color); height: 80px; border-radius: 5px; display: flex; align-items: center; justify-content: center; color: white; font-weight: 600; position: relative;">
                                    最大内容元素 (LCP)
                                    <div style="position: absolute; top: -10px; right: -10px; background: var(--accent-color); color: white; padding: 2px 8px; border-radius: 10px; font-size: 10px;">
                                        这里！
                                    </div>
                                </div>
                            </div>

                            <!-- 连接线 -->
                            <div class="flow-line" style="top: 150px; right: 80px; width: 2px; height: 60px; transform: rotate(90deg);"></div>
                        </div>

                        <!-- LCP优化方案 -->
                        <div class="feature-box" style="margin-top: 20px;">
                            <h3 style="display: flex; align-items: center; gap: 10px; justify-content: center;">
                                <i data-lucide="zap" style="width: 24px; height: 24px;"></i>
                                LCP优化秘籍
                            </h3>
                            <div style="text-align: left; margin-top: 15px; font-size: 0.9em;">
                                <p style="color: var(--success-color); margin: 8px 0;">🖼️ 图片优化：压缩+WebP格式</p>
                                <p style="color: var(--success-color); margin: 8px 0;">🌐 使用CDN：就近访问资源</p>
                                <p style="color: var(--success-color); margin: 8px 0;">⚡ 预加载：提前加载关键资源</p>
                                <p style="color: var(--success-color); margin: 8px 0;">🔧 服务器优化：提升响应速度</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 幻灯片 5: CLS - 累积布局偏移 -->
        <div class="slide" id="slide-5">
            <div class="slide-header">
                <div class="slide-number">05</div>
                <h1 class="slide-title" style="display: flex; align-items: center; justify-content: center; gap: 15px;">
                    <i data-lucide="move" style="width: 48px; height: 48px; color: #6366f1;"></i>
                    CLS - 累积布局偏移
                </h1>
            </div>
            <div class="slide-content">
                <div class="content-grid">
                    <div class="text-section fade-in-left">
                        <h2 style="color: var(--primary-color); margin-bottom: 30px;">就像坐地铁时的突然刹车</h2>
                        <div style="font-size: 1.2em; line-height: 1.8; color: var(--text-dark);">
                            <p style="margin-bottom: 20px;">🚇 <strong>地铁体验类比：</strong></p>
                            <ul style="margin-left: 20px; margin-bottom: 20px;">
                                <li>平稳行驶 → 页面稳定显示</li>
                                <li>突然刹车 → 内容突然移动</li>
                                <li>乘客摔倒 → 用户点错按钮</li>
                            </ul>

                            <p style="margin-bottom: 20px;">📱 <strong>常见CLS问题：</strong></p>
                            <ul style="margin-left: 20px; margin-bottom: 20px;">
                                <li>图片加载后挤开文字</li>
                                <li>广告突然插入页面</li>
                                <li>字体加载导致文字跳动</li>
                                <li>按钮位置突然改变</li>
                            </ul>

                            <p style="margin-bottom: 20px;">📊 <strong>CLS评分标准：</strong></p>
                            <ul style="margin-left: 20px; margin-bottom: 20px;">
                                <li style="color: var(--success-color);">✅ 优秀：小于0.1</li>
                                <li style="color: var(--warning-color);">⚠️ 需要改进：0.1-0.25</li>
                                <li style="color: var(--danger-color);">❌ 较差：大于0.25</li>
                            </ul>

                            <p style="margin-bottom: 20px;">🚫 <strong>导致CLS的元凶：</strong></p>
                            <ul style="margin-left: 20px;">
                                <li>📸 <strong>无尺寸图片</strong> - 加载后挤开其他内容</li>
                                <li>📢 <strong>动态广告</strong> - 突然插入页面中间</li>
                                <li>🔤 <strong>字体闪烁</strong> - 网络字体加载导致跳动</li>
                                <li>📦 <strong>动态内容</strong> - AJAX加载的内容无预留空间</li>
                            </ul>
                        </div>
                    </div>
                    <div class="visual-section fade-in-right">
                        <div class="animated-diagram">
                            <!-- 好的布局 -->
                            <div style="position: absolute; top: 20px; left: 20px; width: 45%; height: 120px; background: white; border: 2px solid var(--success-color); border-radius: 10px; padding: 10px;">
                                <h4 style="color: var(--success-color); margin-bottom: 10px; text-align: center;">✅ 稳定布局</h4>
                                <div style="background: #f3f4f6; height: 20px; border-radius: 3px; margin-bottom: 5px;"></div>
                                <div style="background: var(--success-color); height: 40px; border-radius: 3px; margin-bottom: 5px;"></div>
                                <div style="background: #f3f4f6; height: 20px; border-radius: 3px;"></div>
                            </div>

                            <!-- 坏的布局 -->
                            <div style="position: absolute; top: 20px; right: 20px; width: 45%; height: 120px; background: white; border: 2px solid var(--danger-color); border-radius: 10px; padding: 10px;">
                                <h4 style="color: var(--danger-color); margin-bottom: 10px; text-align: center;">❌ 跳动布局</h4>
                                <div style="background: #f3f4f6; height: 20px; border-radius: 3px; margin-bottom: 5px;"></div>
                                <div style="background: var(--danger-color); height: 40px; border-radius: 3px; margin-bottom: 5px; animation: shake 1s infinite;">
                                    <div style="position: absolute; top: -5px; right: -5px; background: var(--warning-color); color: white; padding: 2px 6px; border-radius: 8px; font-size: 10px;">
                                        跳！
                                    </div>
                                </div>
                                <div style="background: #f3f4f6; height: 20px; border-radius: 3px; animation: shake 1s infinite 0.2s;"></div>
                            </div>

                            <!-- 用户体验对比 -->
                            <div style="position: absolute; bottom: 40px; left: 20px; width: 45%; text-align: center;">
                                <div style="font-size: 3em; margin-bottom: 10px;">😊</div>
                                <p style="color: var(--success-color); font-weight: 600;">用户开心</p>
                                <p style="font-size: 12px; color: var(--text-light);">准确点击按钮</p>
                            </div>

                            <div style="position: absolute; bottom: 40px; right: 20px; width: 45%; text-align: center;">
                                <div style="font-size: 3em; margin-bottom: 10px;">😤</div>
                                <p style="color: var(--danger-color); font-weight: 600;">用户生气</p>
                                <p style="font-size: 12px; color: var(--text-light);">误点错误按钮</p>
                            </div>

                            <!-- 中间箭头 -->
                            <div style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); font-size: 2em;">
                                VS
                            </div>
                        </div>

                        <!-- CLS解决方案 -->
                        <div class="feature-box" style="margin-top: 20px;">
                            <h3 style="display: flex; align-items: center; gap: 10px; justify-content: center;">
                                <i data-lucide="shield" style="width: 24px; height: 24px;"></i>
                                CLS防护指南
                            </h3>
                            <div style="text-align: left; margin-top: 15px; font-size: 0.9em;">
                                <p style="color: var(--success-color); margin: 8px 0;">📐 设置图片尺寸：width和height属性</p>
                                <p style="color: var(--success-color); margin: 8px 0;">📦 预留空间：为动态内容留位置</p>
                                <p style="color: var(--success-color); margin: 8px 0;">🔤 字体优化：font-display: swap</p>
                                <p style="color: var(--success-color); margin: 8px 0;">📢 固定广告位：避免动态插入</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 幻灯片 5: FID - 首次输入延迟 -->
        <div class="slide" id="slide-5">
            <div class="slide-header">
                <div class="slide-number">05</div>
                <h1 class="slide-title" style="display: flex; align-items: center; justify-content: center; gap: 15px;">
                    <i data-lucide="mouse-pointer-click" style="width: 48px; height: 48px; color: #6366f1;"></i>
                    FID - 首次输入延迟
                </h1>
            </div>
            <div class="slide-content">
                <div class="content-grid">
                    <div class="text-section fade-in-left">
                        <h2 style="color: var(--primary-color); margin-bottom: 30px;">就像餐厅服务员的反应速度</h2>
                        <div style="font-size: 1.2em; line-height: 1.8; color: var(--text-dark);">
                            <p style="margin-bottom: 20px;">🍽️ <strong>餐厅服务类比：</strong></p>
                            <ul style="margin-left: 20px; margin-bottom: 20px;">
                                <li>顾客举手 → 用户点击按钮</li>
                                <li>服务员看到 → 浏览器接收事件</li>
                                <li>走过来服务 → 页面响应操作</li>
                            </ul>

                            <p style="margin-bottom: 20px;">⚡ <strong>FID测量什么：</strong></p>
                            <ul style="margin-left: 20px; margin-bottom: 20px;">
                                <li>用户首次点击到响应的时间</li>
                                <li>包括：点击、轻触、按键</li>
                                <li>不包括：滚动、缩放</li>
                            </ul>

                            <p style="margin-bottom: 20px;">⏱️ <strong>FID标准时间：</strong></p>
                            <ul style="margin-left: 20px; margin-bottom: 20px;">
                                <li style="color: var(--success-color);">✅ 优秀：小于100毫秒</li>
                                <li style="color: var(--warning-color);">⚠️ 需要改进：100-300毫秒</li>
                                <li style="color: var(--danger-color);">❌ 较差：大于300毫秒</li>
                            </ul>

                            <p style="margin-bottom: 20px;">🚫 <strong>拖慢FID的罪魁祸首：</strong></p>
                            <ul style="margin-left: 20px;">
                                <li>📦 <strong>巨大JS文件</strong> - 主线程被长时间占用</li>
                                <li>🔄 <strong>复杂计算</strong> - CPU密集型任务阻塞</li>
                                <li>🌐 <strong>第三方脚本</strong> - 广告、统计代码影响</li>
                                <li>⚡ <strong>同步操作</strong> - 阻塞式代码执行</li>
                            </ul>
                        </div>
                        <button class="interactive-btn" onclick="showFIDDemo()" style="display: flex; align-items: center; gap: 8px; justify-content: center;">
                            <i data-lucide="zap" style="width: 20px; height: 20px;"></i>
                            体验FID延迟效果
                        </button>
                    </div>
                    <div class="visual-section fade-in-right">
                        <div class="animated-diagram">
                            <!-- 用户操作 -->
                            <div class="diagram-element" style="top: 30px; left: 30px; width: 100px; height: 70px; background: var(--orange-gradient);">
                                <i data-lucide="hand" style="width: 20px; height: 20px; color: white;"></i>
                                <span style="color: white; font-weight: 600; font-size: 14px;">用户点击</span>
                            </div>

                            <!-- 延迟时间 -->
                            <div class="diagram-element highlight" style="top: 30px; left: 50%; transform: translateX(-50%); width: 120px; height: 70px;">
                                <i data-lucide="clock" style="width: 24px; height: 24px; color: white;"></i>
                                <span style="color: white; font-weight: 700; font-size: 14px;">FID延迟</span>
                                <small style="color: rgba(255,255,255,0.9);">等待响应</small>
                            </div>

                            <!-- 页面响应 -->
                            <div class="diagram-element" style="top: 30px; right: 30px; width: 100px; height: 70px; background: var(--success-gradient);">
                                <i data-lucide="check" style="width: 20px; height: 20px; color: white;"></i>
                                <span style="color: white; font-weight: 600; font-size: 14px;">页面响应</span>
                            </div>

                            <!-- 时间轴 -->
                            <div style="position: absolute; top: 130px; left: 20px; right: 20px; height: 60px; background: #f3f4f6; border-radius: 10px; padding: 15px;">
                                <div style="display: flex; justify-content: space-between; align-items: center; height: 100%;">
                                    <span style="font-size: 12px; color: var(--text-dark);">0ms</span>
                                    <span style="font-size: 12px; color: var(--warning-color); font-weight: 600;">100ms</span>
                                    <span style="font-size: 12px; color: var(--danger-color); font-weight: 600;">300ms</span>
                                </div>
                                <div style="position: absolute; top: 50%; left: 33%; width: 2px; height: 20px; background: var(--warning-color); transform: translateY(-50%);"></div>
                                <div style="position: absolute; top: 50%; right: 20%; width: 2px; height: 20px; background: var(--danger-color); transform: translateY(-50%);"></div>
                            </div>

                            <!-- 用户感受 -->
                            <div style="position: absolute; bottom: 40px; left: 20px; right: 20px; display: flex; justify-content: space-around;">
                                <div style="text-align: center;">
                                    <div style="font-size: 2em; margin-bottom: 5px;">⚡</div>
                                    <p style="font-size: 12px; color: var(--success-color); font-weight: 600;">瞬间响应</p>
                                </div>
                                <div style="text-align: center;">
                                    <div style="font-size: 2em; margin-bottom: 5px;">🤔</div>
                                    <p style="font-size: 12px; color: var(--warning-color); font-weight: 600;">稍有延迟</p>
                                </div>
                                <div style="text-align: center;">
                                    <div style="font-size: 2em; margin-bottom: 5px;">😤</div>
                                    <p style="font-size: 12px; color: var(--danger-color); font-weight: 600;">明显卡顿</p>
                                </div>
                            </div>

                            <!-- 连接线 -->
                            <div class="flow-line" style="top: 65px; left: 140px; width: 80px; height: 2px;"></div>
                            <div class="flow-line" style="top: 65px; right: 140px; width: 80px; height: 2px;"></div>
                        </div>

                        <!-- FID优化方案 -->
                        <div class="feature-box" style="margin-top: 20px;">
                            <h3 style="display: flex; align-items: center; gap: 10px; justify-content: center;">
                                <i data-lucide="cpu" style="width: 24px; height: 24px;"></i>
                                FID加速秘籍
                            </h3>
                            <div style="text-align: left; margin-top: 15px; font-size: 0.9em;">
                                <p style="color: var(--success-color); margin: 8px 0;">✂️ 代码分割：按需加载JS文件</p>
                                <p style="color: var(--success-color); margin: 8px 0;">⚡ 异步处理：避免阻塞主线程</p>
                                <p style="color: var(--success-color); margin: 8px 0;">🗜️ 压缩代码：减少文件大小</p>
                                <p style="color: var(--success-color); margin: 8px 0;">🚫 移除无用代码：清理冗余脚本</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 幻灯片 6: WordPress性能优化插件 -->
        <div class="slide" id="slide-6">
            <div class="slide-header">
                <div class="slide-number">06</div>
                <h1 class="slide-title" style="display: flex; align-items: center; justify-content: center; gap: 15px;">
                    <i data-lucide="puzzle" style="width: 48px; height: 48px; color: #6366f1;"></i>
                    WordPress性能优化插件
                </h1>
            </div>
            <div class="slide-content">
                <div class="content-grid">
                    <div class="text-section fade-in-left">
                        <h2 style="color: var(--primary-color); margin-bottom: 30px;">我们推荐的两大性能插件</h2>
                        <div style="font-size: 1.1em; line-height: 1.8; color: var(--text-dark);">

                            <div style="margin-bottom: 25px; padding: 15px; background: rgba(99, 102, 241, 0.1); border-radius: 10px;">
                                <h4 style="color: var(--primary-color); margin-bottom: 10px;">🖼️ WebP Express</h4>
                                <p style="margin-bottom: 10px; font-size: 0.95em;"><strong>功能：</strong>自动将图片转换为WebP格式</p>
                                <p style="margin-bottom: 10px; font-size: 0.95em;"><strong>优势：</strong></p>
                                <ul style="margin-left: 15px; font-size: 0.9em;">
                                    <li>图片体积减少60-80%</li>
                                    <li>保持相同的视觉质量</li>
                                    <li>自动兼容不支持WebP的浏览器</li>
                                    <li>无需手动转换，全自动处理</li>
                                </ul>
                                <p style="margin-top: 10px; font-size: 0.9em; color: var(--success-color);"><strong>效果：</strong>显著提升LCP指标</p>
                            </div>

                            <div style="margin-bottom: 25px; padding: 15px; background: rgba(6, 214, 160, 0.1); border-radius: 10px;">
                                <h4 style="color: var(--accent-color); margin-bottom: 10px;">⚡ Lazy Load - Optimize Images</h4>
                                <p style="margin-bottom: 10px; font-size: 0.95em;"><strong>功能：</strong>图片懒加载技术</p>
                                <p style="margin-bottom: 10px; font-size: 0.95em;"><strong>工作原理：</strong></p>
                                <ul style="margin-left: 15px; font-size: 0.9em;">
                                    <li>只加载用户当前看到的图片</li>
                                    <li>未浏览到的图片不提前加载</li>
                                    <li>滚动到图片位置时才开始加载</li>
                                    <li>大幅减少初始页面加载时间</li>
                                </ul>
                                <p style="margin-top: 10px; font-size: 0.9em; color: var(--success-color);"><strong>效果：</strong>提升页面打开速度</p>
                            </div>

                            <div style="margin-bottom: 25px; padding: 15px; background: rgba(245, 158, 11, 0.1); border-radius: 10px;">
                                <h4 style="color: var(--warning-color); margin-bottom: 10px;">⚠️ 使用注意事项</h4>
                                <ul style="margin-left: 15px; font-size: 0.9em;">
                                    <li><strong>Lazy Load缺点：</strong>网络差的用户体验可能下降</li>
                                    <li><strong>建议：</strong>首屏重要图片不要懒加载</li>
                                    <li><strong>测试：</strong>安装后务必进行PageSpeed检测</li>
                                    <li><strong>监控：</strong>关注用户反馈和跳出率变化</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                    <div class="visual-section fade-in-right">
                        <div class="animated-diagram">
                            <!-- 插件效果对比 -->
                            <div style="position: absolute; top: 20px; left: 20px; right: 20px; height: 280px; background: white; border-radius: 10px; padding: 20px; border: 1px solid #e5e7eb;">
                                <h4 style="text-align: center; margin-bottom: 20px; color: var(--primary-color);">插件优化效果对比</h4>

                                <!-- WebP Express效果 -->
                                <div style="margin-bottom: 20px; padding: 12px; background: #f8fafc; border-radius: 8px;">
                                    <div style="display: flex; justify-content: space-between; margin-bottom: 8px;">
                                        <span style="font-size: 13px; font-weight: 600;">🖼️ WebP Express</span>
                                        <span style="font-size: 12px; color: var(--success-color);">文件大小 -70%</span>
                                    </div>
                                    <div style="display: flex; gap: 10px; font-size: 11px;">
                                        <div style="flex: 1; text-align: center; padding: 8px; background: var(--danger-color); color: white; border-radius: 4px;">
                                            JPEG 2MB
                                        </div>
                                        <div style="display: flex; align-items: center; color: var(--accent-color);">→</div>
                                        <div style="flex: 1; text-align: center; padding: 8px; background: var(--success-color); color: white; border-radius: 4px;">
                                            WebP 600KB
                                        </div>
                                    </div>
                                </div>

                                <!-- Lazy Load效果 -->
                                <div style="margin-bottom: 20px; padding: 12px; background: #f0fdf4; border-radius: 8px;">
                                    <div style="display: flex; justify-content: space-between; margin-bottom: 8px;">
                                        <span style="font-size: 13px; font-weight: 600;">⚡ Lazy Load</span>
                                        <span style="font-size: 12px; color: var(--success-color);">初始加载 -50%</span>
                                    </div>
                                    <div style="display: flex; gap: 10px; font-size: 11px;">
                                        <div style="flex: 1; text-align: center; padding: 8px; background: var(--warning-color); color: white; border-radius: 4px;">
                                            加载50张图片
                                        </div>
                                        <div style="display: flex; align-items: center; color: var(--accent-color);">→</div>
                                        <div style="flex: 1; text-align: center; padding: 8px; background: var(--success-color); color: white; border-radius: 4px;">
                                            只加载5张图片
                                        </div>
                                    </div>
                                </div>

                                <!-- 综合效果 -->
                                <div style="margin-bottom: 15px; padding: 12px; background: #fefce8; border-radius: 8px;">
                                    <div style="display: flex; justify-content: space-between; margin-bottom: 8px;">
                                        <span style="font-size: 13px; font-weight: 600;">🎯 综合优化</span>
                                        <span style="font-size: 12px; color: var(--success-color);">PageSpeed +30分</span>
                                    </div>
                                    <div style="display: flex; gap: 10px; font-size: 11px;">
                                        <div style="flex: 1; text-align: center; padding: 8px; background: var(--text-light); color: white; border-radius: 4px;">
                                            优化前 45分
                                        </div>
                                        <div style="display: flex; align-items: center; color: var(--accent-color);">→</div>
                                        <div style="flex: 1; text-align: center; padding: 8px; background: var(--success-color); color: white; border-radius: 4px;">
                                            优化后 75分
                                        </div>
                                    </div>
                                </div>

                                <!-- 安装提示 -->
                                <div style="text-align: center; padding: 10px; background: var(--bg-light); border-radius: 8px;">
                                    <div style="font-size: 12px; font-weight: 600; color: var(--primary-color); margin-bottom: 3px;">
                                        💡 安装顺序建议
                                    </div>
                                    <div style="font-size: 11px; color: var(--text-dark);">
                                        WebP Express → Lazy Load → PageSpeed检测
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 幻灯片 7: 具体优化技术详解 -->
        <div class="slide" id="slide-7">
            <div class="slide-header">
                <div class="slide-number">07</div>
                <h1 class="slide-title" style="display: flex; align-items: center; justify-content: center; gap: 15px;">
                    <i data-lucide="code" style="width: 48px; height: 48px; color: #6366f1;"></i>
                    具体优化技术详解
                </h1>
            </div>
            <div class="slide-content">
                <div class="content-grid">
                    <div class="text-section fade-in-left">
                        <h2 style="color: var(--primary-color); margin-bottom: 30px;">手把手教你优化技巧</h2>
                        <div style="font-size: 1.1em; line-height: 1.6; color: var(--text-dark);">

                            <div style="margin-bottom: 25px; padding: 15px; background: rgba(99, 102, 241, 0.1); border-radius: 10px;">
                                <h4 style="color: var(--primary-color); margin-bottom: 10px;">🖼️ 图片优化实战</h4>
                                <ul style="margin-left: 15px; font-size: 0.95em;">
                                    <li><strong>格式选择：</strong>照片用WebP，图标用SVG</li>
                                    <li><strong>尺寸设置：</strong>&lt;img width="300" height="200"&gt;</li>
                                    <li><strong>懒加载：</strong>loading="lazy" 属性</li>
                                    <li><strong>压缩工具：</strong>TinyPNG、ImageOptim</li>
                                </ul>
                            </div>

                            <div style="margin-bottom: 25px; padding: 15px; background: rgba(6, 214, 160, 0.1); border-radius: 10px;">
                                <h4 style="color: var(--accent-color); margin-bottom: 10px;">⚡ 代码优化技巧</h4>
                                <ul style="margin-left: 15px; font-size: 0.95em;">
                                    <li><strong>CSS优化：</strong>删除无用样式，合并文件</li>
                                    <li><strong>JS优化：</strong>代码分割，按需加载</li>
                                    <li><strong>字体优化：</strong>font-display: swap</li>
                                    <li><strong>预加载：</strong>&lt;link rel="preload"&gt;</li>
                                </ul>
                            </div>

                            <div style="margin-bottom: 25px; padding: 15px; background: rgba(245, 158, 11, 0.1); border-radius: 10px;">
                                <h4 style="color: var(--warning-color); margin-bottom: 10px;">🌐 服务器优化</h4>
                                <ul style="margin-left: 15px; font-size: 0.95em;">
                                    <li><strong>启用压缩：</strong>Gzip/Brotli压缩</li>
                                    <li><strong>缓存设置：</strong>合理的Cache-Control</li>
                                    <li><strong>CDN加速：</strong>全球节点分发</li>
                                    <li><strong>HTTP/2：</strong>多路复用技术</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                    <div class="visual-section fade-in-right">
                        <div class="animated-diagram">
                            <!-- 优化前后对比 -->
                            <div style="position: absolute; top: 20px; left: 20px; right: 20px; height: 280px; background: white; border-radius: 10px; padding: 20px; border: 1px solid #e5e7eb;">
                                <h4 style="text-align: center; margin-bottom: 20px; color: var(--primary-color);">优化技术效果对比</h4>

                                <!-- 图片优化 -->
                                <div style="margin-bottom: 20px; padding: 10px; background: #f8fafc; border-radius: 8px;">
                                    <div style="display: flex; justify-content: space-between; margin-bottom: 8px;">
                                        <span style="font-size: 12px; font-weight: 600;">🖼️ 图片优化</span>
                                        <span style="font-size: 12px; color: var(--success-color);">-80% 文件大小</span>
                                    </div>
                                    <div style="display: flex; gap: 10px; font-size: 11px;">
                                        <div style="flex: 1; text-align: center; padding: 8px; background: var(--danger-color); color: white; border-radius: 4px;">
                                            JPEG 5MB
                                        </div>
                                        <div style="display: flex; align-items: center; color: var(--accent-color);">→</div>
                                        <div style="flex: 1; text-align: center; padding: 8px; background: var(--success-color); color: white; border-radius: 4px;">
                                            WebP 1MB
                                        </div>
                                    </div>
                                </div>

                                <!-- 代码优化 -->
                                <div style="margin-bottom: 20px; padding: 10px; background: #f8fafc; border-radius: 8px;">
                                    <div style="display: flex; justify-content: space-between; margin-bottom: 8px;">
                                        <span style="font-size: 12px; font-weight: 600;">⚡ 代码优化</span>
                                        <span style="font-size: 12px; color: var(--success-color);">-60% 加载时间</span>
                                    </div>
                                    <div style="display: flex; gap: 10px; font-size: 11px;">
                                        <div style="flex: 1; text-align: center; padding: 8px; background: var(--warning-color); color: white; border-radius: 4px;">
                                            未压缩 500KB
                                        </div>
                                        <div style="display: flex; align-items: center; color: var(--accent-color);">→</div>
                                        <div style="flex: 1; text-align: center; padding: 8px; background: var(--success-color); color: white; border-radius: 4px;">
                                            压缩后 200KB
                                        </div>
                                    </div>
                                </div>

                                <!-- CDN优化 -->
                                <div style="margin-bottom: 20px; padding: 10px; background: #f8fafc; border-radius: 8px;">
                                    <div style="display: flex; justify-content: space-between; margin-bottom: 8px;">
                                        <span style="font-size: 12px; font-weight: 600;">🌐 CDN加速</span>
                                        <span style="font-size: 12px; color: var(--success-color);">-70% 响应时间</span>
                                    </div>
                                    <div style="display: flex; gap: 10px; font-size: 11px;">
                                        <div style="flex: 1; text-align: center; padding: 8px; background: var(--text-light); color: white; border-radius: 4px;">
                                            远程服务器 800ms
                                        </div>
                                        <div style="display: flex; align-items: center; color: var(--accent-color);">→</div>
                                        <div style="flex: 1; text-align: center; padding: 8px; background: var(--success-color); color: white; border-radius: 4px;">
                                            CDN节点 240ms
                                        </div>
                                    </div>
                                </div>

                                <!-- 综合效果 -->
                                <div style="text-align: center; padding: 15px; background: var(--bg-light); border-radius: 8px;">
                                    <div style="font-size: 14px; font-weight: 600; color: var(--primary-color); margin-bottom: 5px;">
                                        综合优化效果
                                    </div>
                                    <div style="font-size: 20px; font-weight: 700; color: var(--success-color);">
                                        页面加载速度提升 5倍！
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 幻灯片 8: 性能优化策略 -->
        <div class="slide" id="slide-8">
            <div class="slide-header">
                <div class="slide-number">08</div>
                <h1 class="slide-title" style="display: flex; align-items: center; justify-content: center; gap: 15px;">
                    <i data-lucide="settings" style="width: 48px; height: 48px; color: #6366f1;"></i>
                    性能优化策略
                </h1>
            </div>
            <div class="slide-content">
                <div class="content-grid">
                    <div class="text-section fade-in-left">
                        <h2 style="color: var(--primary-color); margin-bottom: 30px;">就像给汽车做全面保养</h2>
                        <div style="font-size: 1.2em; line-height: 1.8; color: var(--text-dark);">
                            <p style="margin-bottom: 20px;">🚗 <strong>汽车保养类比：</strong></p>

                            <p style="margin-bottom: 15px;">🔧 <strong>图片优化 = 减轻车重</strong></p>
                            <ul style="margin-left: 20px; margin-bottom: 20px; font-size: 1em;">
                                <li>压缩图片大小</li>
                                <li>使用现代格式（WebP）</li>
                                <li>懒加载技术</li>
                            </ul>

                            <p style="margin-bottom: 15px;">⚡ <strong>代码优化 = 调校引擎</strong></p>
                            <ul style="margin-left: 20px; margin-bottom: 20px; font-size: 1em;">
                                <li>压缩CSS/JS文件</li>
                                <li>删除无用代码</li>
                                <li>合并文件请求</li>
                            </ul>

                            <p style="margin-bottom: 15px;">🌐 <strong>CDN加速 = 就近加油站</strong></p>
                            <ul style="margin-left: 20px; font-size: 1em;">
                                <li>全球节点分布</li>
                                <li>就近访问资源</li>
                                <li>减少传输距离</li>
                            </ul>
                        </div>
                    </div>
                    <div class="visual-section fade-in-right">
                        <div class="animated-diagram">
                            <!-- 优化前后对比 -->
                            <div style="position: absolute; top: 20px; left: 20px; width: 45%; height: 140px; background: white; border: 2px solid var(--danger-color); border-radius: 10px; padding: 15px;">
                                <h4 style="color: var(--danger-color); margin-bottom: 15px; text-align: center;">优化前</h4>
                                <div style="display: flex; flex-direction: column; gap: 8px;">
                                    <div style="background: var(--danger-color); height: 20px; border-radius: 3px; display: flex; align-items: center; padding: 0 8px; color: white; font-size: 10px;">
                                        大图片 5MB
                                    </div>
                                    <div style="background: var(--warning-color); height: 20px; border-radius: 3px; display: flex; align-items: center; padding: 0 8px; color: white; font-size: 10px;">
                                        未压缩JS
                                    </div>
                                    <div style="background: var(--text-light); height: 20px; border-radius: 3px; display: flex; align-items: center; padding: 0 8px; color: white; font-size: 10px;">
                                        远程服务器
                                    </div>
                                </div>
                                <div style="text-align: center; margin-top: 10px; color: var(--danger-color); font-weight: 600;">
                                    加载时间: 8秒
                                </div>
                            </div>

                            <div style="position: absolute; top: 20px; right: 20px; width: 45%; height: 140px; background: white; border: 2px solid var(--success-color); border-radius: 10px; padding: 15px;">
                                <h4 style="color: var(--success-color); margin-bottom: 15px; text-align: center;">优化后</h4>
                                <div style="display: flex; flex-direction: column; gap: 8px;">
                                    <div style="background: var(--success-color); height: 20px; border-radius: 3px; display: flex; align-items: center; padding: 0 8px; color: white; font-size: 10px;">
                                        WebP 200KB
                                    </div>
                                    <div style="background: var(--accent-color); height: 20px; border-radius: 3px; display: flex; align-items: center; padding: 0 8px; color: white; font-size: 10px;">
                                        压缩JS
                                    </div>
                                    <div style="background: var(--primary-color); height: 20px; border-radius: 3px; display: flex; align-items: center; padding: 0 8px; color: white; font-size: 10px;">
                                        CDN加速
                                    </div>
                                </div>
                                <div style="text-align: center; margin-top: 10px; color: var(--success-color); font-weight: 600;">
                                    加载时间: 1.5秒
                                </div>
                            </div>

                            <!-- 性能提升箭头 -->
                            <div style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); text-align: center;">
                                <div style="font-size: 2em; color: var(--accent-color); margin-bottom: 5px;">⚡</div>
                                <div style="font-size: 14px; font-weight: 600; color: var(--accent-color);">5倍提升</div>
                            </div>

                            <!-- 优化工具 -->
                            <div style="position: absolute; bottom: 20px; left: 20px; right: 20px; background: var(--bg-light); border-radius: 10px; padding: 15px;">
                                <h4 style="color: var(--primary-color); margin-bottom: 10px; text-align: center;">常用优化工具</h4>
                                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 10px; font-size: 12px;">
                                    <div style="text-align: center;">🖼️ TinyPNG</div>
                                    <div style="text-align: center;">⚡ Webpack</div>
                                    <div style="text-align: center;">🌐 Cloudflare</div>
                                    <div style="text-align: center;">📊 Lighthouse</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 幻灯片 9: 实际优化案例 -->
        <div class="slide" id="slide-9">
            <div class="slide-header">
                <div class="slide-number">09</div>
                <h1 class="slide-title" style="display: flex; align-items: center; justify-content: center; gap: 15px;">
                    <i data-lucide="trending-up" style="width: 48px; height: 48px; color: #6366f1;"></i>
                    真实优化案例
                </h1>
            </div>
            <div class="slide-content">
                <div class="content-grid">
                    <div class="text-section fade-in-left">
                        <h2 style="color: var(--primary-color); margin-bottom: 30px;">电商网站的华丽转身</h2>
                        <div style="font-size: 1.2em; line-height: 1.8; color: var(--text-dark);">
                            <p style="margin-bottom: 20px;">📊 <strong>优化前的问题：</strong></p>
                            <ul style="margin-left: 20px; margin-bottom: 20px;">
                                <li style="color: var(--danger-color);">PageSpeed分数：32分</li>
                                <li style="color: var(--danger-color);">LCP：6.8秒</li>
                                <li style="color: var(--danger-color);">CLS：0.45</li>
                                <li style="color: var(--danger-color);">FID：350毫秒</li>
                            </ul>

                            <p style="margin-bottom: 20px;">🔧 <strong>采取的优化措施：</strong></p>
                            <ul style="margin-left: 20px; margin-bottom: 20px;">
                                <li>图片压缩 + WebP格式</li>
                                <li>启用CDN加速</li>
                                <li>代码分割和懒加载</li>
                                <li>预留图片空间</li>
                            </ul>

                            <p style="margin-bottom: 20px;">🎉 <strong>优化后的成果：</strong></p>
                            <ul style="margin-left: 20px;">
                                <li style="color: var(--success-color);">PageSpeed分数：89分</li>
                                <li style="color: var(--success-color);">LCP：1.8秒</li>
                                <li style="color: var(--success-color);">CLS：0.05</li>
                                <li style="color: var(--success-color);">FID：85毫秒</li>
                            </ul>
                        </div>
                    </div>
                    <div class="visual-section fade-in-right">
                        <div class="animated-diagram">
                            <!-- 优化前后对比图表 -->
                            <div style="position: absolute; top: 20px; left: 20px; right: 20px; height: 200px; background: white; border-radius: 10px; padding: 20px; border: 1px solid #e5e7eb;">
                                <h4 style="text-align: center; margin-bottom: 20px; color: var(--primary-color);">性能指标对比</h4>

                                <!-- PageSpeed分数 -->
                                <div style="margin-bottom: 15px;">
                                    <div style="display: flex; justify-content: space-between; margin-bottom: 5px;">
                                        <span style="font-size: 12px;">PageSpeed分数</span>
                                        <span style="font-size: 12px;">32 → 89</span>
                                    </div>
                                    <div style="background: #f3f4f6; height: 8px; border-radius: 4px; position: relative;">
                                        <div style="background: var(--danger-color); width: 32%; height: 100%; border-radius: 4px;"></div>
                                        <div style="background: var(--success-color); width: 89%; height: 100%; border-radius: 4px; position: absolute; top: 0; opacity: 0.7;"></div>
                                    </div>
                                </div>

                                <!-- LCP -->
                                <div style="margin-bottom: 15px;">
                                    <div style="display: flex; justify-content: space-between; margin-bottom: 5px;">
                                        <span style="font-size: 12px;">LCP (秒)</span>
                                        <span style="font-size: 12px;">6.8 → 1.8</span>
                                    </div>
                                    <div style="background: #f3f4f6; height: 8px; border-radius: 4px; position: relative;">
                                        <div style="background: var(--danger-color); width: 85%; height: 100%; border-radius: 4px;"></div>
                                        <div style="background: var(--success-color); width: 22%; height: 100%; border-radius: 4px; position: absolute; top: 0; opacity: 0.7;"></div>
                                    </div>
                                </div>

                                <!-- CLS -->
                                <div style="margin-bottom: 15px;">
                                    <div style="display: flex; justify-content: space-between; margin-bottom: 5px;">
                                        <span style="font-size: 12px;">CLS</span>
                                        <span style="font-size: 12px;">0.45 → 0.05</span>
                                    </div>
                                    <div style="background: #f3f4f6; height: 8px; border-radius: 4px; position: relative;">
                                        <div style="background: var(--danger-color); width: 90%; height: 100%; border-radius: 4px;"></div>
                                        <div style="background: var(--success-color); width: 10%; height: 100%; border-radius: 4px; position: absolute; top: 0; opacity: 0.7;"></div>
                                    </div>
                                </div>

                                <!-- FID -->
                                <div>
                                    <div style="display: flex; justify-content: space-between; margin-bottom: 5px;">
                                        <span style="font-size: 12px;">FID (毫秒)</span>
                                        <span style="font-size: 12px;">350 → 85</span>
                                    </div>
                                    <div style="background: #f3f4f6; height: 8px; border-radius: 4px; position: relative;">
                                        <div style="background: var(--danger-color); width: 70%; height: 100%; border-radius: 4px;"></div>
                                        <div style="background: var(--success-color); width: 17%; height: 100%; border-radius: 4px; position: absolute; top: 0; opacity: 0.7;"></div>
                                    </div>
                                </div>
                            </div>

                            <!-- 业务影响 -->
                            <div style="position: absolute; bottom: 20px; left: 20px; right: 20px; background: var(--bg-light); border-radius: 10px; padding: 15px;">
                                <h4 style="color: var(--primary-color); margin-bottom: 10px; text-align: center;">业务影响</h4>
                                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px; font-size: 12px; text-align: center;">
                                    <div>
                                        <div style="font-size: 1.5em; color: var(--success-color); margin-bottom: 5px;">📈</div>
                                        <div style="font-weight: 600; color: var(--success-color);">转化率 +23%</div>
                                    </div>
                                    <div>
                                        <div style="font-size: 1.5em; color: var(--success-color); margin-bottom: 5px;">⏱️</div>
                                        <div style="font-weight: 600; color: var(--success-color);">跳出率 -15%</div>
                                    </div>
                                    <div>
                                        <div style="font-size: 1.5em; color: var(--success-color); margin-bottom: 5px;">🔍</div>
                                        <div style="font-weight: 600; color: var(--success-color);">SEO排名 +12位</div>
                                    </div>
                                    <div>
                                        <div style="font-size: 1.5em; color: var(--success-color); margin-bottom: 5px;">💰</div>
                                        <div style="font-weight: 600; color: var(--success-color);">收入 +18%</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 幻灯片 10: 优化工具和检测方法 -->
        <div class="slide" id="slide-10">
            <div class="slide-header">
                <div class="slide-number">10</div>
                <h1 class="slide-title" style="display: flex; align-items: center; justify-content: center; gap: 15px;">
                    <i data-lucide="wrench" style="width: 48px; height: 48px; color: #6366f1;"></i>
                    优化工具箱
                </h1>
            </div>
            <div class="slide-content">
                <div class="content-grid">
                    <div class="text-section fade-in-left">
                        <h2 style="color: var(--primary-color); margin-bottom: 30px;">就像医生的诊断设备</h2>
                        <div style="font-size: 1.2em; line-height: 1.8; color: var(--text-dark);">
                            <p style="margin-bottom: 20px;">🏥 <strong>医院诊断类比：</strong></p>
                            <ul style="margin-left: 20px; margin-bottom: 20px;">
                                <li>体检设备 → 性能检测工具</li>
                                <li>化验报告 → 性能分析报告</li>
                                <li>治疗方案 → 优化建议</li>
                            </ul>

                            <p style="margin-bottom: 20px;">🔧 <strong>必备工具清单：</strong></p>
                            <ul style="margin-left: 20px; margin-bottom: 20px;">
                                <li><strong>Google PageSpeed Insights</strong> - 官方检测</li>
                                <li><strong>Lighthouse</strong> - 浏览器内置</li>
                                <li><strong>GTmetrix</strong> - 详细分析</li>
                                <li><strong>WebPageTest</strong> - 专业测试</li>
                            </ul>

                            <p style="margin-bottom: 20px;">📊 <strong>检测频率建议：</strong></p>
                            <ul style="margin-left: 20px;">
                                <li>开发阶段：每天检测</li>
                                <li>上线前：必须检测</li>
                                <li>运营期：每周检测</li>
                                <li>重大更新：立即检测</li>
                            </ul>
                        </div>
                    </div>
                    <div class="visual-section fade-in-right">
                        <div class="animated-diagram">
                            <!-- 工具展示 -->
                            <div style="position: absolute; top: 20px; left: 20px; width: 45%; height: 120px; background: white; border: 2px solid var(--primary-color); border-radius: 10px; padding: 15px;">
                                <div style="display: flex; align-items: center; gap: 10px; margin-bottom: 10px;">
                                    <div style="width: 30px; height: 30px; background: var(--primary-color); border-radius: 50%; display: flex; align-items: center; justify-content: center;">
                                        <i data-lucide="search" style="width: 16px; height: 16px; color: white;"></i>
                                    </div>
                                    <h4 style="color: var(--primary-color);">PageSpeed Insights</h4>
                                </div>
                                <p style="font-size: 12px; color: var(--text-light);">Google官方工具，权威性最高，直接影响SEO排名</p>
                            </div>

                            <div style="position: absolute; top: 20px; right: 20px; width: 45%; height: 120px; background: white; border: 2px solid var(--accent-color); border-radius: 10px; padding: 15px;">
                                <div style="display: flex; align-items: center; gap: 10px; margin-bottom: 10px;">
                                    <div style="width: 30px; height: 30px; background: var(--accent-color); border-radius: 50%; display: flex; align-items: center; justify-content: center;">
                                        <i data-lucide="zap" style="width: 16px; height: 16px; color: white;"></i>
                                    </div>
                                    <h4 style="color: var(--accent-color);">Lighthouse</h4>
                                </div>
                                <p style="font-size: 12px; color: var(--text-light);">Chrome内置工具，开发者首选，实时检测</p>
                            </div>

                            <div style="position: absolute; top: 160px; left: 20px; width: 45%; height: 120px; background: white; border: 2px solid var(--warning-color); border-radius: 10px; padding: 15px;">
                                <div style="display: flex; align-items: center; gap: 10px; margin-bottom: 10px;">
                                    <div style="width: 30px; height: 30px; background: var(--warning-color); border-radius: 50%; display: flex; align-items: center; justify-content: center;">
                                        <i data-lucide="bar-chart" style="width: 16px; height: 16px; color: white;"></i>
                                    </div>
                                    <h4 style="color: var(--warning-color);">GTmetrix</h4>
                                </div>
                                <p style="font-size: 12px; color: var(--text-light);">详细瀑布图分析，找出具体瓶颈</p>
                            </div>

                            <div style="position: absolute; top: 160px; right: 20px; width: 45%; height: 120px; background: white; border: 2px solid var(--secondary-color); border-radius: 10px; padding: 15px;">
                                <div style="display: flex; align-items: center; gap: 10px; margin-bottom: 10px;">
                                    <div style="width: 30px; height: 30px; background: var(--secondary-color); border-radius: 50%; display: flex; align-items: center; justify-content: center;">
                                        <i data-lucide="globe" style="width: 16px; height: 16px; color: white;"></i>
                                    </div>
                                    <h4 style="color: var(--secondary-color);">WebPageTest</h4>
                                </div>
                                <p style="font-size: 12px; color: var(--text-light);">全球多地测试，模拟真实用户环境</p>
                            </div>

                            <!-- 检测流程 -->
                            <div style="position: absolute; bottom: 20px; left: 20px; right: 20px; background: var(--bg-light); border-radius: 10px; padding: 15px;">
                                <h4 style="color: var(--primary-color); margin-bottom: 10px; text-align: center;">标准检测流程</h4>
                                <div style="display: flex; justify-content: space-between; align-items: center; font-size: 12px;">
                                    <div style="text-align: center;">
                                        <div style="width: 30px; height: 30px; background: var(--primary-color); border-radius: 50%; display: flex; align-items: center; justify-content: center; margin: 0 auto 5px;">
                                            <span style="color: white; font-weight: 600;">1</span>
                                        </div>
                                        <span>输入网址</span>
                                    </div>
                                    <div style="color: var(--text-light);">→</div>
                                    <div style="text-align: center;">
                                        <div style="width: 30px; height: 30px; background: var(--accent-color); border-radius: 50%; display: flex; align-items: center; justify-content: center; margin: 0 auto 5px;">
                                            <span style="color: white; font-weight: 600;">2</span>
                                        </div>
                                        <span>开始检测</span>
                                    </div>
                                    <div style="color: var(--text-light);">→</div>
                                    <div style="text-align: center;">
                                        <div style="width: 30px; height: 30px; background: var(--warning-color); border-radius: 50%; display: flex; align-items: center; justify-content: center; margin: 0 auto 5px;">
                                            <span style="color: white; font-weight: 600;">3</span>
                                        </div>
                                        <span>分析报告</span>
                                    </div>
                                    <div style="color: var(--text-light);">→</div>
                                    <div style="text-align: center;">
                                        <div style="width: 30px; height: 30px; background: var(--success-color); border-radius: 50%; display: flex; align-items: center; justify-content: center; margin: 0 auto 5px;">
                                            <span style="color: white; font-weight: 600;">4</span>
                                        </div>
                                        <span>优化实施</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 幻灯片 11: 常见问题解答 -->
        <div class="slide" id="slide-11">
            <div class="slide-header">
                <div class="slide-number">11</div>
                <h1 class="slide-title" style="display: flex; align-items: center; justify-content: center; gap: 15px;">
                    <i data-lucide="help-circle" style="width: 48px; height: 48px; color: #6366f1;"></i>
                    常见问题解答
                </h1>
            </div>
            <div class="slide-content">
                <div class="content-grid">
                    <div class="text-section fade-in-left">
                        <h2 style="color: var(--primary-color); margin-bottom: 30px;">新手最关心的问题</h2>
                        <div style="font-size: 1.1em; line-height: 1.6; color: var(--text-dark);">
                            <div style="margin-bottom: 25px; padding: 15px; background: rgba(99, 102, 241, 0.1); border-radius: 10px;">
                                <h4 style="color: var(--primary-color); margin-bottom: 10px;">❓ 多久检测一次性能？</h4>
                                <p style="color: var(--text-dark);">建议每周检测一次，重大更新后立即检测。就像定期体检一样重要。</p>
                            </div>

                            <div style="margin-bottom: 25px; padding: 15px; background: rgba(6, 214, 160, 0.1); border-radius: 10px;">
                                <h4 style="color: var(--accent-color); margin-bottom: 10px;">❓ 移动端和桌面端哪个更重要？</h4>
                                <p style="color: var(--text-dark);">移动端更重要！超过60%的用户使用手机访问，Google也优先考虑移动端性能。</p>
                            </div>

                            <div style="margin-bottom: 25px; padding: 15px; background: rgba(245, 158, 11, 0.1); border-radius: 10px;">
                                <h4 style="color: var(--warning-color); margin-bottom: 10px;">❓ 分数多少算合格？</h4>
                                <p style="color: var(--text-dark);">90分以上优秀，70-89分良好，50-69分需要改进，50分以下较差。</p>
                            </div>

                            <div style="margin-bottom: 25px; padding: 15px; background: rgba(239, 68, 68, 0.1); border-radius: 10px;">
                                <h4 style="color: var(--danger-color); margin-bottom: 10px;">❓ 优化会影响网站功能吗？</h4>
                                <p style="color: var(--text-dark);">正确的优化不会影响功能，反而会提升用户体验。建议在测试环境先验证。</p>
                            </div>
                        </div>
                    </div>
                    <div class="visual-section fade-in-right">
                        <div class="animated-diagram">
                            <!-- 优化误区 -->
                            <div style="position: absolute; top: 20px; left: 20px; right: 20px; height: 150px; background: white; border: 2px solid var(--danger-color); border-radius: 10px; padding: 20px;">
                                <h4 style="color: var(--danger-color); margin-bottom: 15px; text-align: center;">❌ 常见误区</h4>
                                <div style="display: flex; flex-direction: column; gap: 10px; font-size: 12px;">
                                    <div style="display: flex; align-items: center; gap: 10px;">
                                        <span style="color: var(--danger-color);">❌</span>
                                        <span>只关注桌面端性能</span>
                                    </div>
                                    <div style="display: flex; align-items: center; gap: 10px;">
                                        <span style="color: var(--danger-color);">❌</span>
                                        <span>过度优化影响功能</span>
                                    </div>
                                    <div style="display: flex; align-items: center; gap: 10px;">
                                        <span style="color: var(--danger-color);">❌</span>
                                        <span>只在上线前检测一次</span>
                                    </div>
                                    <div style="display: flex; align-items: center; gap: 10px;">
                                        <span style="color: var(--danger-color);">❌</span>
                                        <span>忽视用户真实体验</span>
                                    </div>
                                </div>
                            </div>

                            <!-- 最佳实践 -->
                            <div style="position: absolute; bottom: 20px; left: 20px; right: 20px; height: 150px; background: white; border: 2px solid var(--success-color); border-radius: 10px; padding: 20px;">
                                <h4 style="color: var(--success-color); margin-bottom: 15px; text-align: center;">✅ 最佳实践</h4>
                                <div style="display: flex; flex-direction: column; gap: 10px; font-size: 12px;">
                                    <div style="display: flex; align-items: center; gap: 10px;">
                                        <span style="color: var(--success-color);">✅</span>
                                        <span>移动端优先策略</span>
                                    </div>
                                    <div style="display: flex; align-items: center; gap: 10px;">
                                        <span style="color: var(--success-color);">✅</span>
                                        <span>渐进式优化方案</span>
                                    </div>
                                    <div style="display: flex; align-items: center; gap: 10px;">
                                        <span style="color: var(--success-color);">✅</span>
                                        <span>持续监控和改进</span>
                                    </div>
                                    <div style="display: flex; align-items: center; gap: 10px;">
                                        <span style="color: var(--success-color);">✅</span>
                                        <span>关注真实用户指标</span>
                                    </div>
                                </div>
                            </div>

                            <!-- 中间提示 -->
                            <div style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); text-align: center; background: var(--primary-color); color: white; padding: 15px; border-radius: 10px;">
                                <div style="font-size: 1.5em; margin-bottom: 5px;">💡</div>
                                <div style="font-size: 14px; font-weight: 600;">记住关键原则</div>
                                <div style="font-size: 12px;">用户体验第一</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 幻灯片 12: 总结 -->
        <div class="slide" id="slide-12">
            <div class="slide-header">
                <div class="slide-number">12</div>
                <h1 class="slide-title bounce-in" style="display: flex; align-items: center; justify-content: center; gap: 15px;">
                    <i data-lucide="trophy" style="width: 48px; height: 48px; color: #6366f1;"></i>
                    恭喜！您已掌握性能优化核心！
                </h1>
            </div>
            <div class="slide-content">
                <div class="content-grid">
                    <div class="text-section fade-in-left">
                        <h2 style="color: var(--primary-color); margin-bottom: 30px;">您的性能优化技能包</h2>

                        <!-- 知识点回顾卡片 -->
                        <div class="feature-box" style="margin-bottom: 25px; background: linear-gradient(135deg, #dbeafe, #bfdbfe); border: 1px solid #93c5fd;">
                            <h3 style="color: #1e40af; margin-bottom: 15px; font-weight: bold;">🎯 核心知识点回顾</h3>
                            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 8px; font-size: 0.95em; color: #1e3a8a; font-weight: 500;">
                                <div>✅ Google PageSpeed - 网站体检师</div>
                                <div>✅ LCP - 最大内容绘制时间</div>
                                <div>✅ CLS - 累积布局偏移</div>
                                <div>✅ FID - 首次输入延迟</div>
                                <div>✅ 优化策略 - 图片、代码、CDN</div>
                                <div>✅ 检测工具 - 持续监控改进</div>
                            </div>
                        </div>

                        <!-- 能力提升卡片 -->
                        <div class="feature-box" style="background: linear-gradient(135deg, #dcfce7, #bbf7d0); border: 1px solid #86efac;">
                            <h3 style="color: #15803d; margin-bottom: 15px; font-weight: bold;">🚀 现在您可以</h3>
                            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 8px; font-size: 0.95em; color: #14532d; font-weight: 500;">
                                <div>💡 理解性能指标含义</div>
                                <div>🎯 使用PageSpeed检测网站</div>
                                <div>🔧 制定优化改进方案</div>
                                <div>📊 监控性能变化趋势</div>
                            </div>
                        </div>
                    </div>
                    <div class="visual-section fade-in-right">
                        <div class="animated-diagram">
                            <!-- 成就徽章 -->
                            <div class="diagram-element highlight" style="top: 30px; left: 50%; transform: translateX(-50%); width: 150px; height: 100px;">
                                <i data-lucide="award" style="width: 40px; height: 40px; color: white;"></i>
                                <span style="color: white; font-weight: 700; font-size: 16px;">性能优化</span>
                                <span style="color: white; font-weight: 700; font-size: 16px;">专家认证</span>
                            </div>

                            <!-- 技能点 -->
                            <div style="position: absolute; top: 150px; left: 20px; right: 20px; display: grid; grid-template-columns: 1fr 1fr; gap: 15px;">
                                <div style="background: white; padding: 15px; border-radius: 10px; text-align: center; border: 2px solid var(--primary-color);">
                                    <div style="font-size: 2em; margin-bottom: 5px;">📊</div>
                                    <div style="font-weight: 600; color: var(--primary-color);">分析能力</div>
                                    <div style="font-size: 12px; color: var(--text-light);">读懂性能报告</div>
                                </div>
                                <div style="background: white; padding: 15px; border-radius: 10px; text-align: center; border: 2px solid var(--accent-color);">
                                    <div style="font-size: 2em; margin-bottom: 5px;">🔧</div>
                                    <div style="font-weight: 600; color: var(--accent-color);">优化技能</div>
                                    <div style="font-size: 12px; color: var(--text-light);">实施改进方案</div>
                                </div>
                                <div style="background: white; padding: 15px; border-radius: 10px; text-align: center; border: 2px solid var(--warning-color);">
                                    <div style="font-size: 2em; margin-bottom: 5px;">🎯</div>
                                    <div style="font-weight: 600; color: var(--warning-color);">监控意识</div>
                                    <div style="font-size: 12px; color: var(--text-light);">持续跟踪改进</div>
                                </div>
                                <div style="background: white; padding: 15px; border-radius: 10px; text-align: center; border: 2px solid var(--success-color);">
                                    <div style="font-size: 2em; margin-bottom: 5px;">💡</div>
                                    <div style="font-weight: 600; color: var(--success-color);">实战经验</div>
                                    <div style="font-size: 12px; color: var(--text-light);">解决实际问题</div>
                                </div>
                            </div>

                            <!-- 下一步建议 -->
                            <div style="position: absolute; bottom: 20px; left: 20px; right: 20px; background: var(--bg-light); border-radius: 10px; padding: 15px;">
                                <h4 style="color: var(--primary-color); margin-bottom: 10px; text-align: center;">🎯 下一步行动</h4>
                                <div style="font-size: 12px; text-align: center; color: var(--text-dark);">
                                    立即检测您的网站 → 制定优化计划 → 实施改进 → 持续监控
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 导航控制 -->
    <div class="navigation">
        <button class="nav-btn" onclick="previousSlide()" style="display: flex; align-items: center; gap: 8px;">
            <i data-lucide="chevron-left" style="width: 18px; height: 18px;"></i>
            上一页
        </button>
        <button class="nav-btn" onclick="nextSlide()" style="display: flex; align-items: center; gap: 8px;">
            下一页
            <i data-lucide="chevron-right" style="width: 18px; height: 18px;"></i>
        </button>
        <button class="nav-btn" onclick="toggleFullscreen()" style="display: flex; align-items: center; gap: 8px;">
            <i data-lucide="maximize" style="width: 18px; height: 18px;"></i>
            全屏
        </button>
    </div>

    <script>
        let currentSlide = 1;
        const totalSlides = 12;

        // 幻灯片切换功能
        function showSlide(slideNumber) {
            document.querySelectorAll('.slide').forEach(slide => {
                slide.classList.remove('active');
            });

            document.getElementById(`slide-${slideNumber}`).classList.add('active');
            document.getElementById('current-slide').textContent = slideNumber;

            const progress = (slideNumber / totalSlides) * 100;
            document.getElementById('progress-fill').style.width = progress + '%';

            currentSlide = slideNumber;
        }

        function nextSlide() {
            if (currentSlide < totalSlides) {
                showSlide(currentSlide + 1);
            }
        }

        function previousSlide() {
            if (currentSlide > 1) {
                showSlide(currentSlide - 1);
            }
        }

        function toggleFullscreen() {
            const fullscreenBtn = document.querySelector('.nav-btn[onclick="toggleFullscreen()"]');
            const icon = fullscreenBtn.querySelector('i');
            
            if (!document.fullscreenElement) {
                document.documentElement.requestFullscreen().then(() => {
                    icon.setAttribute('data-lucide', 'minimize');
                    if (typeof lucide !== 'undefined') {
                        lucide.createIcons();
                    }
                });
            } else {
                document.exitFullscreen().then(() => {
                    icon.setAttribute('data-lucide', 'maximize');
                    if (typeof lucide !== 'undefined') {
                        lucide.createIcons();
                    }
                });
            }
        }

        // 键盘事件
        document.addEventListener('keydown', function(e) {
            switch(e.key) {
                case 'ArrowRight':
                case ' ':
                    e.preventDefault();
                    nextSlide();
                    break;
                case 'ArrowLeft':
                    e.preventDefault();
                    previousSlide();
                    break;
                case 'F11':
                    e.preventDefault();
                    toggleFullscreen();
                    break;
                case 'Escape':
                    if (document.fullscreenElement) {
                        e.preventDefault();
                        document.exitFullscreen();
                    }
                    break;
            }
        });

        // 监听全屏状态变化
        document.addEventListener('fullscreenchange', function() {
            const fullscreenBtn = document.querySelector('.nav-btn[onclick="toggleFullscreen()"]');
            const icon = fullscreenBtn.querySelector('i');
            
            if (document.fullscreenElement) {
                icon.setAttribute('data-lucide', 'minimize');
            } else {
                icon.setAttribute('data-lucide', 'maximize');
            }
            
            if (typeof lucide !== 'undefined') {
                lucide.createIcons();
            }
        });

        // 页面初始化
        document.addEventListener('DOMContentLoaded', function() {
            document.getElementById('total-slides').textContent = totalSlides;
            showSlide(1);

            // 初始化Lucide图标
            if (typeof lucide !== 'undefined') {
                lucide.createIcons();
            }
        });
    </script>
</body>
</html>
