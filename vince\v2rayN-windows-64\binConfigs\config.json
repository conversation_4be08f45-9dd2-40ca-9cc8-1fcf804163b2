{"log": {"level": "warn", "timestamp": true}, "dns": {"servers": [{"tag": "remote", "address": "tcp://*******", "strategy": "prefer_ipv4", "detour": "proxy"}, {"tag": "local", "address": "*********", "strategy": "prefer_ipv4", "detour": "direct"}, {"tag": "block", "address": "rcode://success"}, {"tag": "local_local", "address": "*********", "detour": "direct"}], "rules": [{"server": "local_local", "domain": ["hy2-sg1.dhh.moe"]}, {"server": "remote", "clash_mode": "Global"}, {"server": "local_local", "clash_mode": "Direct"}, {"server": "local", "rule_set": ["geosite-cn"]}], "final": "remote"}, "inbounds": [{"type": "mixed", "tag": "socks", "listen": "127.0.0.1", "listen_port": 10808, "sniff": true, "sniff_override_destination": true}], "outbounds": [{"type": "hysteria2", "tag": "proxy", "server": "hy2-sg1.dhh.moe", "server_port": 14514, "up_mbps": 100, "down_mbps": 100, "password": "05ee387e-5361-4a01-9260-33fd2828dd7b", "tls": {"enabled": true, "server_name": "ssl.dhh.ac.cn", "insecure": true}}, {"type": "direct", "tag": "direct"}, {"type": "block", "tag": "block"}, {"type": "dns", "tag": "dns_out"}], "route": {"rules": [{"outbound": "proxy", "clash_mode": "Global"}, {"outbound": "direct", "clash_mode": "Direct"}, {"outbound": "dns_out", "protocol": ["dns"]}, {"outbound": "block", "network": ["udp"], "port": [443]}, {"outbound": "direct", "ip_is_private": true}, {"outbound": "direct", "rule_set": ["geosite-private"]}, {"outbound": "proxy", "port_range": ["0:65535"]}], "rule_set": [{"tag": "geosite-private", "type": "local", "format": "binary", "path": "C:\\Users\\<USER>\\Desktop\\v2rayN-windows-64\\bin\\srss\\geosite-private.srs"}, {"tag": "geosite-cn", "type": "local", "format": "binary", "path": "C:\\Users\\<USER>\\Desktop\\v2rayN-windows-64\\bin\\srss\\geosite-cn.srs"}]}, "experimental": {"cache_file": {"enabled": true, "path": "C:\\Users\\<USER>\\Desktop\\v2rayN-windows-64\\bin\\cache.db"}, "clash_api": {"external_controller": "127.0.0.1:10813"}}}