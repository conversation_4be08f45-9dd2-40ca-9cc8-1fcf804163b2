# 综合WordPress规则
# 合并：WordPress高级安全规则、PHP处理和其他WordPress配置

# 限流错误响应页面
error_page 429 /rate_limit_exceeded.html;
location = /rate_limit_exceeded.html {
    internal;
    return 429 '<!DOCTYPE html>
<html>
<head>
    <title>请求过于频繁</title>
    <meta charset="UTF-8">
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; line-height: 1.6; }
        h1 { color: #444; }
        .container { max-width: 600px; margin: 0 auto; }
    </style>
</head>
<body>
    <div class="container">
        <h1>请求过于频繁</h1>
        <p>您的请求速率超出了限制，请稍后再试。</p>
    </div>
</body>
</html>';
}

# PHP文件处理
location ~ \.php$ {
    try_files $uri =404;
    fastcgi_split_path_info ^(.+\.php)(/.+)$;
    fastcgi_pass php;
    fastcgi_index index.php;
    include fastcgi_params;
    fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
    fastcgi_intercept_errors on;
    fastcgi_buffer_size 16k;
    fastcgi_buffers 4 16k;
    fastcgi_read_timeout 300;
    
    # 隐藏PHP版本
    fastcgi_hide_header X-Powered-By;
}

# 增强安全性 - 隐藏PHP错误日志
fastcgi_param PHP_VALUE "display_errors=0\nlog_errors=1";

# 设置默认MIME类型
default_type application/octet-stream;

# 静态资源缓存和限流
location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot|otf)$ {
    limit_req zone=general burst=1000 nodelay;  # 静态资源使用非常大的burst值
    expires max;
    log_not_found off;
    access_log off;
    add_header Cache-Control "public, max-age=31536000";
}

# 常规文件处理
location = /favicon.ico {
    log_not_found off;
    access_log off;
}

location = /robots.txt {
    log_not_found off;
    access_log off;
    allow all;
}

# 基本的根路径处理
location / {
    # 限制HTTP请求方法
    limit_except GET POST HEAD {
        deny all;
    }
    
    # 添加IP访问控制 - 如需限制访问，使用统一内部网络白名单
    if ($internal_whitelist = 0) {
        # 如需启用严格访问控制，取消注释下面一行
        # return 403;
    }
    
    # WordPress固定链接规则 - 添加限流
    limit_req zone=perip burst=5 nodelay;
    try_files $uri $uri/ /index.php?$args;
    
    # 搜索参数检测与重定向
    if ($args ~ "s=(.+)") {
        set $word $1;
        rewrite (?:/page/\d+/)? /search/$word$1 last;
    }
}

# 搜索路径特别处理 - 限流规则
location /search/ {
    limit_req zone=search_limit burst=1 nodelay;  # 严格限流，防止搜索DOS攻击
    try_files $uri $uri/ /index.php?$args;
}

# WebP图片支持
location ~* \.(jpe?g|png)$ {
    add_header Vary Accept;
    try_files $uri$webp_suffix $uri =404;
    expires max;
    log_not_found off;
    access_log off;
}

# WebP Express支持 - 处理WebP图像转换
location ~* ^/?wp-content/.*\.(png|jpe?g)$ {
    add_header Vary Accept;
    expires 365d;
    access_log off;
    
    if ($http_accept !~* "webp") {
        break;
    }

    if ( !-f $document_root/wp-content/plugins/webp-express/wod/webp-on-demand.php ) {
        break;
    }

    try_files
        /wp-content/webp-express/webp-images/doc-root/$request_uri.webp
        $request_uri.webp
        /wp-content/plugins/webp-express/wod/webp-on-demand.php?xsource=x$request_filename&wp-content=wp-content
        ;
}
    
# 处理不存在的WebP图像转换请求
location ~* ^/?wp-content/.*\.(png|jpe?g)\.webp$ {
    expires 365d;
    access_log off;

    try_files
        $request_uri
        /wp-content/plugins/webp-express/wod/webp-realizer.php?xdestination=x$request_filename&wp-content=wp-content
        ;
}

# WebP文件统一处理
location ~* \.webp$ {
    expires 365d;
    access_log off;
}

# 禁止访问WebP转换脚本
location ~* ^/wp-content/webp-express/webp-images/.*\.php$ {
    deny all;
}

# WebP Express转换器速率限制
location ~* ^/wp-content/plugins/webp-express/wod2?/.*\.php$ {
    fastcgi_pass php;
    include fastcgi_params;
    fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
}

# 防止路径遍历攻击
# 禁止通过URL路径中的双斜杠访问文件
location ~ //  {
    return 404;
}

# 禁止通过URL路径中的多个点访问文件
location ~ /\. {
    return 404;
}

# 明确禁止诸如image.jpg/file.php这样的路径尝试执行PHP
location ~ "^(.*)\.([a-z]+)\.(php|ph(p|tml|p3|p4|p5|ar|t|s))$" {
    # 排除wp-admin路径，因为有些WordPress后台功能可能使用这种格式
    location ~ ^/wp-admin/ {
        try_files $uri =404;
        fastcgi_pass php;
        include fastcgi_params;
        fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
    }
    
    # 拒绝所有其他路径
    return 403;
}

# 限制WordPress XMLRPC访问
location = /xmlrpc.php {
    deny all;
    access_log off;
    log_not_found off;
}

# 限制WordPress登录页面防止暴力攻击
location = /wp-login.php {

    
    # 应用严格限流
    limit_req zone=wordpress burst=1 nodelay;  # 严格限流，防止暴力攻击
    
    # 禁用注册功能
    if ($arg_action = register) {
        return 403;
    }

    # 禁止使用HTTP/1.0进行POST请求（防止一些攻击工具）
    set $is_http_10 0;
    set $is_post_method 0;
    if ($server_protocol = "HTTP/1.0") {
        set $is_http_10 1;
    }
    if ($request_method = "POST") {
        set $is_post_method 1;
    }
    set $ban_http10_post "${is_http_10}${is_post_method}";
    if ($ban_http10_post = "11") {
        return 403;
    }
    

    
    # IP白名单访问控制（若主机未豁免认证）
    # 使用统一管理的管理员白名单
    if ($admin_whitelist = 1) {
        # 允许白名单IP访问
        set $auth_basic_enabled off;
    }
    
    # PHP处理
    include fastcgi_params;
    fastcgi_pass php;
    fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
}

# WordPress内容目录保护
location ~* ^/wp-content/$ {
    limit_req zone=perip burst=5 nodelay;  # 中等限流
    try_files $uri $uri/ =404;
}

# 大文件下载控制
location ~* \.(zip|rar|7z|tar|gz|sql|csv|txt|pdf|doc|docx|xls|xlsx|ppt|pptx)$ {
    if ($download_whitelist = 0) {
        return 403;  # 非白名单IP禁止访问大文件
    }
    limit_rate_after 5m;  # 前5MB不限速
    limit_rate 512k;      # 之后限制为512KB/s
    expires 1d;
    access_log /var/log/nginx/download.log main;  # 记录下载日志
}

# 对wp-admin目录入口额外的保护
location = /wp-admin {

    
    # IP白名单访问控制
    if ($admin_whitelist = 1) {
        # 允许白名单IP访问
        set $auth_basic_enabled off;
    }
    
    return 301 $scheme://$host$uri/;
}

# wp-admin目录下的PHP文件需要认证访问
location ~* ^/wp-admin/[^/]+\.php$ {

    
    # IP白名单访问控制
    if ($admin_whitelist = 1) {
        # 允许白名单IP访问
        set $auth_basic_enabled off;
    }
    
    fastcgi_pass php;
    include fastcgi_params;
    fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
}

# 允许访问wp-admin目录下的非PHP文件（CSS、JS等）
location ~* ^/wp-admin/ {
    try_files $uri $uri/ =404;
}

# 处理WordPress AJAX请求 - 前台AJAX不需要限制
location = /wp-admin/admin-ajax.php {
    # AJAX请求不限制
    fastcgi_pass php;
    include fastcgi_params;
    fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
}

# 安装程序特殊处理
location = /wp-admin/install.php {
    if ($is_setup_referer = 0) {
        return 403;
    }
    include fastcgi_params;
    fastcgi_pass php;
    fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
}

# 阻止wp-admin子目录下的php文件访问
location ~* ^/wp-admin/([^/]+/)+[^/]+\.php$ {
    deny all;
}

# 禁止WordPress REST API泄露用户信息，但允许特定域名和已登录用户访问
location ~* ^/wp-json {
    # 允许指定域名不受限制

    
    # 允许API白名单IP访问
    if ($api_whitelist = 1) {
        rewrite ^/wp-json/(.*)$ /index.php?$args last;
    }
    
    # 允许已登录用户访问
    if ($http_cookie ~* "wordpress_logged_in_") {
        rewrite ^/wp-json/(.*)$ /index.php?$args last;
    }
    
    # 允许特定插件API
    location ~* ^/wp-json/elementor/? {
        rewrite ^/wp-json/elementor/(.*)$ /index.php?$args last;
    }
    
    location ~* ^/wp-json/contact-form-7/? {
        rewrite ^/wp-json/contact-form-7/(.*)$ /index.php?$args last;
    }
    
    location ~* ^/wp-json/wc-analytics/? {
        rewrite ^/wp-json/wc-analytics/(.*)$ /index.php?$args last;
    }
    
    location ~* ^/wp-json/aioseo/? {
        rewrite ^/wp-json/aioseo/(.*)$ /index.php?$args last;
    }
    
    # 拒绝其他所有wp-json访问
    deny all;
}

# 禁止访问特定文件和文件类型
# 禁止.asp .env文件访问
location ~* \.(asp|env)$ {
    return 403;
}

# 禁止访问WordPress配置文件
location ~* wp-config.php {
    deny all;
}

# 禁止访问上传目录中的PHP文件，但允许访问wp-includes中的必要文件
location ~* /(?:uploads|files|wp-content)/.*\.php$ {
    deny all;
    access_log off;
    log_not_found off;
}

# 保护wp-includes目录，但允许必要的JS/CSS文件
location ~* /wp-includes/.*\.php$ {
    # 允许load-styles.php和load-scripts.php（后台所需）
    location ~* /wp-includes/js/tinymce/wp-tinymce\.php$ { 
        try_files $uri =404;
        fastcgi_split_path_info ^(.+\.php)(/.+)$;
        fastcgi_pass php;
        fastcgi_index index.php;
        include fastcgi_params;
        fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
        fastcgi_param PATH_INFO $fastcgi_path_info;
    }
    
    # 禁止其他PHP文件
    deny all;
    access_log off;
    log_not_found off;
}

# 限制对WordPress核心文件的直接访问
location ~* /wp-admin/includes { deny all; }
location ~* /wp-includes/theme-compat/ { deny all; }
location ~* /wp-includes/js/tinymce/langs/.*\.php { deny all; }

# 阻止恶意扫描
location ~* /(xmlrpc|wp-trackback).php { deny all; } 

# 禁止访问WordPress REST API泄露用户信息
# 阻止直接访问wp-json用户API
location ~* /wp-json/wp/v2/users {
    return 403 '{"error": "API access restricted"}';
}

# 阻止通过rest_route参数访问用户API
if ($args ~* "rest_route=/wp/v2/users") {
    return 403;
}

# WordPress多站点支持（如果需要）
if (!-e $request_filename) {
    rewrite /wp-admin$ $scheme://$host$uri/ permanent;
    rewrite ^(/[^/]+)?(/wp-.*) $2 last;
    rewrite ^(/[^/]+)?(/.*\.php) $2 last;
}

# 特定插件支持 - 从旧配置合并

# 优先允许LiteSpeed缓存插件访问
location ~ ^/wp-content/plugins/litespeed-cache/guest.vary.php$ {
    try_files $uri =404;
    fastcgi_pass php;
    include fastcgi_params;
    fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
}

# 放行Hero Menu插件后台功能
location ~ ^/wp-content/plugins/hmenu/views/[^\/]+/.*\.php$ {
    try_files $uri =404;
    fastcgi_pass php;
    include fastcgi_params;
    fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
}

# 放行Tool-Hub插件后台功能
location ~ ^/wp-content/plugins/tool-hub/.*\.php$ {
    try_files $uri =404;
    fastcgi_pass php;
    include fastcgi_params;
    fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
} 