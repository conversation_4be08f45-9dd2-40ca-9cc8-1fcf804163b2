# Nginx白名单配置
# 统一管理所有IP白名单，从ip_list.conf读取IP地址

# 本地和内部网络白名单 - 用于管理访问控制
geo $internal_whitelist {
    default         0;  # 默认拒绝
    
    # 引入通用IP列表
    include /etc/nginx/rules/ip_list.conf;
}

# 管理员白名单 - 用于WordPress后台访问
geo $admin_whitelist {
    default         0;  # 默认拒绝
    
    # 引入通用IP列表
    include /etc/nginx/rules/ip_list.conf;
}

# 下载白名单 - 允许大文件下载
geo $download_whitelist {
    default         0;  # 默认拒绝
    
    # 引入通用IP列表
    include /etc/nginx/rules/ip_list.conf;
}

# API访问白名单 - 允许访问REST API
geo $api_whitelist {
    default         0;  # 默认拒绝
    
    # 引入通用IP列表
    include /etc/nginx/rules/ip_list.conf;
}

# 无限流白名单 - 不受限流规则影响
geo $ratelimit_whitelist {
    default         0;  # 默认受限
    
    # 引入通用IP列表
    include /etc/nginx/rules/ip_list.conf;
}

# 设置限流地址变量，白名单IP使用0.0.0.0（不受限制）
map $ratelimit_whitelist $limited_addr {
    0 $binary_remote_addr;  # 非白名单使用客户端IP
    1 "";                   # 白名单使用空地址（不限流）
}

# 使用说明:
# 1. 所有白名单IP现在统一在 rules/ip_list.conf 文件中管理
# 2. 只需修改ip_list.conf一个文件，所有白名单都会自动更新
# 3. 在需要白名单控制的location块中使用相应变量:
#    if ($internal_whitelist = 1) { ... }
#    if ($admin_whitelist = 1) { ... }
#    if ($download_whitelist = 0) { return 403; } # 拒绝非白名单
# 4. 定期审查和更新白名单，确保安全性 