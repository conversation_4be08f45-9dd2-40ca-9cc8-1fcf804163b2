<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WordPress数据库扫描报告</title>
    <style>
        :root {
            --primary-color: #0073aa;
            --secondary-color: #005177;
            --accent-color: #d54e21;
            --light-gray: #f5f5f5;
            --dark-gray: #333;
            --success-color: #46b450;
            --warning-color: #ffb900;
            --danger-color: #dc3232;
        }
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen-Sans, Ubuntu, Cantarell, "Helvetica Neue", sans-serif;
            line-height: 1.6;
            color: #333;
            background-color: #f1f1f1;
        }
        
        .container {
            width: 90%;
            max-width: 1200px;
            margin: 2rem auto;
        }
        
        header {
            background-color: var(--primary-color);
            color: white;
            padding: 1.5rem;
            border-radius: 5px 5px 0 0;
        }
        
        .report-meta {
            display: flex;
            justify-content: space-between;
            background-color: var(--secondary-color);
            color: white;
            padding: 0.5rem 1.5rem;
            font-size: 0.9rem;
        }
        
        .content {
            background-color: white;
            padding: 1.5rem;
            border-radius: 0 0 5px 5px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }
        
        h1 {
            font-size: 1.8rem;
            margin-bottom: 0.5rem;
        }
        
        h2 {
            font-size: 1.4rem;
            margin: 1.5rem 0 1rem;
            padding-bottom: 0.5rem;
            border-bottom: 1px solid #eee;
            color: var(--primary-color);
        }
        
        .summary-cards {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
            gap: 1rem;
            margin: 1.5rem 0;
        }
        
        .card {
            background-color: white;
            border-radius: 5px;
            padding: 1.5rem;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            border-top: 3px solid var(--primary-color);
        }
        
        .card h3 {
            font-size: 1.1rem;
            margin-bottom: 0.5rem;
            color: var(--dark-gray);
        }
        
        .card p.value {
            font-size: 2rem;
            font-weight: bold;
            color: var(--primary-color);
            margin-bottom: 0.5rem;
        }
        
        .card.warning {
            border-top-color: var(--warning-color);
        }
        
        .card.warning p.value {
            color: var(--warning-color);
        }
        
        .card.danger {
            border-top-color: var(--danger-color);
        }
        
        .card.danger p.value {
            color: var(--danger-color);
        }
        
        .card.success {
            border-top-color: var(--success-color);
        }
        
        .card.success p.value {
            color: var(--success-color);
        }

        table {
            width: 100%;
            border-collapse: collapse;
            margin: 1.5rem 0;
        }
        
        thead {
            background-color: var(--light-gray);
        }
        
        th, td {
            padding: 0.75rem;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        
        th {
            font-weight: 600;
        }
        
        tr:hover {
            background-color: rgba(0, 115, 170, 0.05);
        }
        
        tr.child-site {
            background-color: rgba(0, 115, 170, 0.03);
        }
        
        tr.child-site td {
            padding-left: 2rem;
            font-size: 0.95em;
        }
        
        .suspicious-low a {
            color: var(--primary-color);
        }
        
        .suspicious-medium a {
            color: var(--warning-color);
        }
        
        .suspicious-high a {
            color: var(--danger-color);
        }
        
        .site-link {
            text-decoration: none;
            color: var(--primary-color);
        }
        
        .site-link:hover {
            text-decoration: underline;
        }
        
        footer {
            text-align: center;
            margin-top: 2rem;
            padding: 1rem;
            color: #777;
            font-size: 0.9rem;
        }
        
        @media (max-width: 768px) {
            .container {
                width: 95%;
            }
            
            .summary-cards {
                grid-template-columns: 1fr;
            }
        }
        
        /* 分页控制样式 */
        .pagination-controls {
            display: flex;
            justify-content: center;
            margin: 1.5rem 0;
            gap: 0.5rem;
        }
        
        .pagination-controls button {
            padding: 0.5rem 1rem;
            background-color: white;
            border: 1px solid #ddd;
            border-radius: 3px;
            cursor: pointer;
            transition: background-color 0.2s;
        }
        
        .pagination-controls button:hover {
            background-color: var(--light-gray);
        }
        
        .pagination-controls button.active {
            background-color: var(--primary-color);
            color: white;
            border-color: var(--primary-color);
        }
        
        .pagination-controls button:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }
        
        /* 搜索框样式 */
        .search-box {
            margin: 1rem 0;
            display: flex;
            gap: 0.5rem;
        }
        
        .search-box input {
            flex: 1;
            padding: 0.5rem;
            border: 1px solid #ddd;
            border-radius: 3px;
        }
        
        .search-box button {
            padding: 0.5rem 1rem;
            background-color: var(--primary-color);
            color: white;
            border: none;
            border-radius: 3px;
            cursor: pointer;
        }
    </style>
    <script>
        // 页面加载完成后执行
        document.addEventListener('DOMContentLoaded', function() {
            // 站点表格分页功能
            initPagination('site-url-table', 5);
            
            // 搜索功能
            const searchButton = document.getElementById('search-button');
            const searchInput = document.getElementById('search-input');
            
            if (searchButton && searchInput) {
                searchButton.addEventListener('click', function() {
                    searchSites(searchInput.value);
                });
                
                searchInput.addEventListener('keypress', function(e) {
                    if (e.key === 'Enter') {
                        searchSites(searchInput.value);
                    }
                });
            }
        });
        
        // 分页逻辑
        function initPagination(tableId, itemsPerPage) {
            const table = document.getElementById(tableId);
            if (!table) return;
            
            const tbody = table.querySelector('tbody');
            const rows = tbody.querySelectorAll('tr');
            const pageCount = Math.ceil(rows.length / itemsPerPage);
            
            // 创建分页控制区
            const paginationDiv = document.createElement('div');
            paginationDiv.className = 'pagination-controls';
            table.parentNode.insertBefore(paginationDiv, table.nextSibling);
            
            // 添加页码按钮
            const prevBtn = document.createElement('button');
            prevBtn.innerText = '上一页';
            prevBtn.addEventListener('click', () => goToPage(currentPage - 1));
            paginationDiv.appendChild(prevBtn);
            
            // 页码按钮
            for (let i = 1; i <= pageCount; i++) {
                const pageBtn = document.createElement('button');
                pageBtn.innerText = i;
                pageBtn.addEventListener('click', () => goToPage(i));
                paginationDiv.appendChild(pageBtn);
            }
            
            const nextBtn = document.createElement('button');
            nextBtn.innerText = '下一页';
            nextBtn.addEventListener('click', () => goToPage(currentPage + 1));
            paginationDiv.appendChild(nextBtn);
            
            // 显示第一页
            let currentPage = 1;
            goToPage(currentPage);
            
            function goToPage(page) {
                if (page < 1 || page > pageCount) return;
                
                currentPage = page;
                const start = (page - 1) * itemsPerPage;
                const end = start + itemsPerPage;
                
                // 隐藏所有行
                rows.forEach(row => row.style.display = 'none');
                
                // 显示当前页的行
                for (let i = start; i < end && i < rows.length; i++) {
                    rows[i].style.display = '';
                }
                
                // 更新按钮状态
                updatePaginationButtons();
            }
            
            function updatePaginationButtons() {
                const buttons = paginationDiv.querySelectorAll('button');
                buttons.forEach((button, i) => {
                    if (i === 0) { // 前一页按钮
                        button.disabled = currentPage === 1;
                    } else if (i === buttons.length - 1) { // 下一页按钮
                        button.disabled = currentPage === pageCount;
                    } else { // 页码按钮
                        button.classList.toggle('active', i === currentPage);
                    }
                });
            }
        }
        
        // 搜索功能
        function searchSites(query) {
            if (!query) return;
            
            query = query.toLowerCase();
            const table = document.getElementById('site-url-table');
            const rows = table.querySelectorAll('tbody tr');
            
            let hasResults = false;
            
            rows.forEach(row => {
                const text = row.textContent.toLowerCase();
                if (text.includes(query)) {
                    row.style.display = '';
                    hasResults = true;
                } else {
                    row.style.display = 'none';
                }
            });
            
            // 隐藏分页控制
            const pagination = table.nextElementSibling;
            if (pagination && pagination.classList.contains('pagination-controls')) {
                pagination.style.display = query ? 'none' : 'flex';
            }
            
            // 显示搜索结果信息
            const resultInfo = document.getElementById('search-result-info');
            if (resultInfo) {
                resultInfo.textContent = hasResults 
                    ? "找到包含\"" + query + "\"的结果" 
                    : "未找到包含\"" + query + "\"的结果";
                resultInfo.style.display = 'block';
            }
        }
    </script>
</head>
<body>
    <div class="container">
        <header>
            <h1>WordPress数据库扫描报告</h1>
            <p>服务器：ip-172-31-5-36</p>
        </header>
        <div class="report-meta">
            <span>生成时间：2025年05月13日 03:24:08</span>
            <span>扫描站点数：42</span>
        </div>
        <div class="content">
            <section>
                <h2>概览</h2>
                <div class="summary-cards">
                    <div class="card">
                        <h3>WordPress数据库总数</h3>
                        <p class="value">42</p>
                    </div>
                    <div class="card">
                        <h3>总数据库大小</h3>
                        <p class="value">7766.76 MB</p>
                    </div>
                    <div class="card">
                        <h3>总文章数</h3>
                        <p class="value">2014</p>
                    </div>
                    <div class="card">
                        <h3>总用户数</h3>
                        <p class="value">286</p>
                    </div>
                </div>
                
                <div class="summary-cards">
                    <div class="card">
                        <h3>总评论数</h3>
                        <p class="value">190</p>
                    </div>
                    <div class="card">
                        <h3>总表数量</h3>
                        <p class="value">4264</p>
                    </div>
                    <div class="card" style="border-top-color: var(--danger-color);">
                        <h3>可疑内容总数</h3>
                        <p class="value" style="color: var(--danger-color);">5786</p>
                    </div>
                    <div class="card">
                        <h3>扫描站点总数</h3>
                        <p class="value">42</p>
                    </div>
                </div>
            </section>
            
            <section>
                <h2>站点信息</h2>
                <div class="search-box">
                    <input type="text" id="search-input" placeholder="搜索站点...">
                    <button id="search-button">搜索</button>
                </div>
                <p id="search-result-info" style="display: none; margin: 1rem 0; font-style: italic;"></p>
                <table id="site-url-table">
                    <thead>
                        <tr>
                            <th>站点路径</th>
                            <th>站点URL</th>
                            <th>数据库名</th>
                            <th>大小(MB)</th>
                            <th>表数量</th>
                            <th>文章数</th>
                            <th>用户数</th>
                            <th>评论数</th>
                            <th>WP版本</th>
                            <th>可疑项</th>
                        </tr>
                    </thead>
                    <tbody>
            <tr>
                <td>/var/www/dexingmeihua</td>
                <td><a href="https://demo66.yhct.site" target="_blank" class="site-link">https://demo66.yhct.site</a></td>
                <td>dexingmeihua</td>
                <td>93.67</td>
                <td>72</td>
                <td>4</td>
                <td>1</td>
                <td>0</td>
                <td>未知</td>
                <td class="suspicious-high"><a href="./dexingmeihua/report.html" class="site-link">232</a></td>
            </tr>
            <tr>
                <td>/var/www/jinsanjiang_en</td>
                <td><a href="https://us.jsj-silica.com" target="_blank" class="site-link">https://us.jsj-silica.com</a></td>
                <td>jinsanjiang_en</td>
                <td>199.30</td>
                <td>74</td>
                <td>25</td>
                <td>3</td>
                <td>1</td>
                <td>未知</td>
                <td class="suspicious-high"><a href="./jinsanjiang_en/report.html" class="site-link">98</a></td>
            </tr>
            <tr>
                <td>/var/www/xiamenjiuran</td>
                <td><a href="https://www.jooeverfoods.com" target="_blank" class="site-link">https://www.jooeverfoods.com</a></td>
                <td>jiuran</td>
                <td>158.82</td>
                <td>161</td>
                <td>36</td>
                <td>14</td>
                <td>4</td>
                <td>未知</td>
                <td class="suspicious-high"><a href="./xiamenjiuran/report.html" class="site-link">29</a></td>
            </tr>
            <tr>
                <td>/var/www/daqu</td>
                <td><a href="https://www.trenditen.com" target="_blank" class="site-link">https://www.trenditen.com</a></td>
                <td>daqu</td>
                <td>154.78</td>
                <td>154</td>
                <td>42</td>
                <td>3</td>
                <td>0</td>
                <td>未知</td>
                <td class="suspicious-high"><a href="./daqu/report.html" class="site-link">19</a></td>
            </tr>
            <tr>
                <td>/var/www/hongjie</td>
                <td><a href="https://hongjiesealing.com" target="_blank" class="site-link">https://hongjiesealing.com</a></td>
                <td>hongjie</td>
                <td>43.41</td>
                <td>92</td>
                <td>1</td>
                <td>2</td>
                <td>0</td>
                <td>未知</td>
                <td class="suspicious-medium"><a href="./hongjie/report.html" class="site-link">6</a></td>
            </tr>
            <tr>
                <td>/var/www/zhongzushangye</td>
                <td><a href="https://www.onepearlbay.com" target="_blank" class="site-link">https://www.onepearlbay.com</a></td>
                <td>zhongzushangye</td>
                <td>172.06</td>
                <td>118</td>
                <td>6</td>
                <td>2</td>
                <td>7</td>
                <td>未知</td>
                <td class="suspicious-medium"><a href="./zhongzushangye/report.html" class="site-link">1</a></td>
            </tr>
            <tr>
                <td>/var/www/senmeixieer</td>
                <td><a href="https://ru.topprober.com" target="_blank" class="site-link">https://ru.topprober.com</a></td>
                <td>senmeixieer</td>
                <td>75.57</td>
                <td>124</td>
                <td>0</td>
                <td>2</td>
                <td>4</td>
                <td>未知</td>
                <td class="suspicious-high"><a href="./senmeixieer/report.html" class="site-link">11</a></td>
            </tr>
            <tr>
                <td>/var/www/aiweisen</td>
                <td><a href="https://www.aivisonauto.com" target="_blank" class="site-link">https://www.aivisonauto.com</a></td>
                <td>aiweisen</td>
                <td>103.65</td>
                <td>166</td>
                <td>71</td>
                <td>2</td>
                <td>4</td>
                <td>未知</td>
                <td class="suspicious-high"><a href="./aiweisen/report.html" class="site-link">60</a></td>
            </tr>
            <tr>
                <td>/var/www/zhongji</td>
                <td><a href="https://www.szj-automation.com" target="_blank" class="site-link">https://www.szj-automation.com</a></td>
                <td>zhongji</td>
                <td>147.50</td>
                <td>67</td>
                <td>293</td>
                <td>2</td>
                <td>0</td>
                <td>未知</td>
                <td class="suspicious-high"><a href="./zhongji/report.html" class="site-link">99</a></td>
            </tr>
            <tr>
                <td>/var/www/jitonglongsujiao</td>
                <td><a href="https://www.toukoojet.com" target="_blank" class="site-link">https://www.toukoojet.com</a></td>
                <td>jitonglongsujiao</td>
                <td>550.69</td>
                <td>113</td>
                <td>128</td>
                <td>2</td>
                <td>4</td>
                <td>未知</td>
                <td class="suspicious-high"><a href="./jitonglongsujiao/report.html" class="site-link">30</a></td>
            </tr>
            <tr>
                <td>/var/www/jinsanjiang_la</td>
                <td><a href="https://la.jsj-silica.com" target="_blank" class="site-link">https://la.jsj-silica.com</a></td>
                <td>jinsanjiang_la</td>
                <td>199.31</td>
                <td>79</td>
                <td>26</td>
                <td>3</td>
                <td>1</td>
                <td>未知</td>
                <td class="suspicious-high"><a href="./jinsanjiang_la/report.html" class="site-link">104</a></td>
            </tr>
            <tr>
                <td>/var/www/yinhuamaoyi</td>
                <td><a href="https://www.infinechem.com" target="_blank" class="site-link">https://www.infinechem.com</a></td>
                <td>yinhuamaoyi</td>
                <td>176.13</td>
                <td>126</td>
                <td>12</td>
                <td>2</td>
                <td>0</td>
                <td>未知</td>
                <td class="suspicious-high"><a href="./yinhuamaoyi/report.html" class="site-link">101</a></td>
            </tr>
            <tr>
                <td>/var/www/tuopusi</td>
                <td><a href="https://www.tope-sol.com" target="_blank" class="site-link">https://www.tope-sol.com</a></td>
                <td>tuopusi</td>
                <td>331.05</td>
                <td>91</td>
                <td>8</td>
                <td>3</td>
                <td>0</td>
                <td>未知</td>
                <td class="suspicious-high"><a href="./tuopusi/report.html" class="site-link">14</a></td>
            </tr>
            <tr>
                <td>/var/www/fanqiujiaju</td>
                <td><a href="https://fanqiujiaju.yhct.top" target="_blank" class="site-link">https://fanqiujiaju.yhct.top</a></td>
                <td>fanqiujiaju</td>
                <td>103.14</td>
                <td>72</td>
                <td>7</td>
                <td>2</td>
                <td>1</td>
                <td>未知</td>
                <td class="suspicious-high"><a href="./fanqiujiaju/report.html" class="site-link">378</a></td>
            </tr>
            <tr>
                <td>/var/www/pingxianghualian</td>
                <td><a href="https://www.hlcatalyst.com" target="_blank" class="site-link">https://www.hlcatalyst.com</a></td>
                <td>pingxianghualian</td>
                <td>91.67</td>
                <td>79</td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td>未知</td>
                <td class="suspicious-high"><a href="./pingxianghualian/report.html" class="site-link">50</a></td>
            </tr>
            <tr>
                <td>/var/www/meiying</td>
                <td><a href="https://www.gainwell.com" target="_blank" class="site-link">https://www.gainwell.com</a></td>
                <td>meiying</td>
                <td>206.73</td>
                <td>114</td>
                <td>39</td>
                <td>3</td>
                <td>0</td>
                <td>未知</td>
                <td class="suspicious-high"><a href="./meiying/report.html" class="site-link">695</a></td>
            </tr>
            <tr>
                <td>/var/www/bairun</td>
                <td><a href="https://www.brprinting.net" target="_blank" class="site-link">https://www.brprinting.net</a></td>
                <td>bairun</td>
                <td>335.92</td>
                <td>115</td>
                <td>55</td>
                <td>84</td>
                <td>69</td>
                <td>未知</td>
                <td class="suspicious-high"><a href="./bairun/report.html" class="site-link">531</a></td>
            </tr>
            <tr>
                <td>/var/www/jilian</td>
                <td><a href="https://www.jliangroup.com" target="_blank" class="site-link">https://www.jliangroup.com</a></td>
                <td>jilian</td>
                <td>530.63</td>
                <td>76</td>
                <td>11</td>
                <td>2</td>
                <td>0</td>
                <td>未知</td>
                <td class="suspicious-medium"><a href="./jilian/report.html" class="site-link">2</a></td>
            </tr>
            <tr>
                <td>/var/www/lanchang</td>
                <td><a href="https://blovedream.net" target="_blank" class="site-link">https://blovedream.net</a></td>
                <td>lanchang</td>
                <td>104.81</td>
                <td>129</td>
                <td>226</td>
                <td>4</td>
                <td>0</td>
                <td>未知</td>
                <td class="suspicious-high"><a href="./lanchang/report.html" class="site-link">310</a></td>
            </tr>
            <tr>
                <td>/var/www/ouniluomanru</td>
                <td><a href="http://ouniluomanru.yhct.top/" target="_blank" class="site-link">http://ouniluomanru.yhct.top/</a></td>
                <td>ouniluomanru</td>
                <td>56.84</td>
                <td>40</td>
                <td>7</td>
                <td>2</td>
                <td>0</td>
                <td>未知</td>
                <td class="suspicious-high"><a href="./ouniluomanru/report.html" class="site-link">73</a></td>
            </tr>
            <tr>
                <td>/var/www/ouniluoman</td>
                <td><a href="https://www.uni-laman.cn/" target="_blank" class="site-link">https://www.uni-laman.cn/</a></td>
                <td>ouniluoman</td>
                <td>47.70</td>
                <td>70</td>
                <td>7</td>
                <td>2</td>
                <td>0</td>
                <td>未知</td>
                <td class="suspicious-high"><a href="./ouniluoman/report.html" class="site-link">50</a></td>
            </tr>
            <tr>
                <td>/var/www/dexingmeihua</td>
                <td><a href="https://demo66.yhct.site" target="_blank" class="site-link">https://demo66.yhct.site</a></td>
                <td>dexingmeihua</td>
                <td>93.67</td>
                <td>72</td>
                <td>4</td>
                <td>1</td>
                <td>0</td>
                <td>未知</td>
                <td class="suspicious-high"><a href="./dexingmeihua/report.html" class="site-link">232</a></td>
            </tr>
            <tr>
                <td>/var/www/jinsanjiang_en</td>
                <td><a href="https://us.jsj-silica.com" target="_blank" class="site-link">https://us.jsj-silica.com</a></td>
                <td>jinsanjiang_en</td>
                <td>199.30</td>
                <td>74</td>
                <td>25</td>
                <td>3</td>
                <td>1</td>
                <td>未知</td>
                <td class="suspicious-high"><a href="./jinsanjiang_en/report.html" class="site-link">98</a></td>
            </tr>
            <tr>
                <td>/var/www/xiamenjiuran</td>
                <td><a href="https://www.jooeverfoods.com" target="_blank" class="site-link">https://www.jooeverfoods.com</a></td>
                <td>jiuran</td>
                <td>158.82</td>
                <td>161</td>
                <td>36</td>
                <td>14</td>
                <td>4</td>
                <td>未知</td>
                <td class="suspicious-high"><a href="./xiamenjiuran/report.html" class="site-link">29</a></td>
            </tr>
            <tr>
                <td>/var/www/daqu</td>
                <td><a href="https://www.trenditen.com" target="_blank" class="site-link">https://www.trenditen.com</a></td>
                <td>daqu</td>
                <td>154.78</td>
                <td>154</td>
                <td>42</td>
                <td>3</td>
                <td>0</td>
                <td>未知</td>
                <td class="suspicious-high"><a href="./daqu/report.html" class="site-link">19</a></td>
            </tr>
            <tr>
                <td>/var/www/hongjie</td>
                <td><a href="https://hongjiesealing.com" target="_blank" class="site-link">https://hongjiesealing.com</a></td>
                <td>hongjie</td>
                <td>43.41</td>
                <td>92</td>
                <td>1</td>
                <td>2</td>
                <td>0</td>
                <td>未知</td>
                <td class="suspicious-medium"><a href="./hongjie/report.html" class="site-link">6</a></td>
            </tr>
            <tr>
                <td>/var/www/zhongzushangye</td>
                <td><a href="https://www.onepearlbay.com" target="_blank" class="site-link">https://www.onepearlbay.com</a></td>
                <td>zhongzushangye</td>
                <td>172.06</td>
                <td>118</td>
                <td>6</td>
                <td>2</td>
                <td>7</td>
                <td>未知</td>
                <td class="suspicious-medium"><a href="./zhongzushangye/report.html" class="site-link">1</a></td>
            </tr>
            <tr>
                <td>/var/www/senmeixieer</td>
                <td><a href="https://ru.topprober.com" target="_blank" class="site-link">https://ru.topprober.com</a></td>
                <td>senmeixieer</td>
                <td>75.57</td>
                <td>124</td>
                <td>0</td>
                <td>2</td>
                <td>4</td>
                <td>未知</td>
                <td class="suspicious-high"><a href="./senmeixieer/report.html" class="site-link">11</a></td>
            </tr>
            <tr>
                <td>/var/www/aiweisen</td>
                <td><a href="https://www.aivisonauto.com" target="_blank" class="site-link">https://www.aivisonauto.com</a></td>
                <td>aiweisen</td>
                <td>103.65</td>
                <td>166</td>
                <td>71</td>
                <td>2</td>
                <td>4</td>
                <td>未知</td>
                <td class="suspicious-high"><a href="./aiweisen/report.html" class="site-link">60</a></td>
            </tr>
            <tr>
                <td>/var/www/zhongji</td>
                <td><a href="https://www.szj-automation.com" target="_blank" class="site-link">https://www.szj-automation.com</a></td>
                <td>zhongji</td>
                <td>147.50</td>
                <td>67</td>
                <td>293</td>
                <td>2</td>
                <td>0</td>
                <td>未知</td>
                <td class="suspicious-high"><a href="./zhongji/report.html" class="site-link">99</a></td>
            </tr>
            <tr>
                <td>/var/www/jitonglongsujiao</td>
                <td><a href="https://www.toukoojet.com" target="_blank" class="site-link">https://www.toukoojet.com</a></td>
                <td>jitonglongsujiao</td>
                <td>550.69</td>
                <td>113</td>
                <td>128</td>
                <td>2</td>
                <td>4</td>
                <td>未知</td>
                <td class="suspicious-high"><a href="./jitonglongsujiao/report.html" class="site-link">30</a></td>
            </tr>
            <tr>
                <td>/var/www/jinsanjiang_la</td>
                <td><a href="https://la.jsj-silica.com" target="_blank" class="site-link">https://la.jsj-silica.com</a></td>
                <td>jinsanjiang_la</td>
                <td>199.31</td>
                <td>79</td>
                <td>26</td>
                <td>3</td>
                <td>1</td>
                <td>未知</td>
                <td class="suspicious-high"><a href="./jinsanjiang_la/report.html" class="site-link">104</a></td>
            </tr>
            <tr>
                <td>/var/www/yinhuamaoyi</td>
                <td><a href="https://www.infinechem.com" target="_blank" class="site-link">https://www.infinechem.com</a></td>
                <td>yinhuamaoyi</td>
                <td>176.13</td>
                <td>126</td>
                <td>12</td>
                <td>2</td>
                <td>0</td>
                <td>未知</td>
                <td class="suspicious-high"><a href="./yinhuamaoyi/report.html" class="site-link">101</a></td>
            </tr>
            <tr>
                <td>/var/www/tuopusi</td>
                <td><a href="https://www.tope-sol.com" target="_blank" class="site-link">https://www.tope-sol.com</a></td>
                <td>tuopusi</td>
                <td>331.05</td>
                <td>91</td>
                <td>8</td>
                <td>3</td>
                <td>0</td>
                <td>未知</td>
                <td class="suspicious-high"><a href="./tuopusi/report.html" class="site-link">14</a></td>
            </tr>
            <tr>
                <td>/var/www/fanqiujiaju</td>
                <td><a href="https://fanqiujiaju.yhct.top" target="_blank" class="site-link">https://fanqiujiaju.yhct.top</a></td>
                <td>fanqiujiaju</td>
                <td>103.14</td>
                <td>72</td>
                <td>7</td>
                <td>2</td>
                <td>1</td>
                <td>未知</td>
                <td class="suspicious-high"><a href="./fanqiujiaju/report.html" class="site-link">378</a></td>
            </tr>
            <tr>
                <td>/var/www/pingxianghualian</td>
                <td><a href="https://www.hlcatalyst.com" target="_blank" class="site-link">https://www.hlcatalyst.com</a></td>
                <td>pingxianghualian</td>
                <td>91.67</td>
                <td>79</td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td>未知</td>
                <td class="suspicious-high"><a href="./pingxianghualian/report.html" class="site-link">50</a></td>
            </tr>
            <tr>
                <td>/var/www/meiying</td>
                <td><a href="https://www.gainwell.com" target="_blank" class="site-link">https://www.gainwell.com</a></td>
                <td>meiying</td>
                <td>206.73</td>
                <td>114</td>
                <td>39</td>
                <td>3</td>
                <td>0</td>
                <td>未知</td>
                <td class="suspicious-high"><a href="./meiying/report.html" class="site-link">695</a></td>
            </tr>
            <tr>
                <td>/var/www/bairun</td>
                <td><a href="https://www.brprinting.net" target="_blank" class="site-link">https://www.brprinting.net</a></td>
                <td>bairun</td>
                <td>335.92</td>
                <td>115</td>
                <td>55</td>
                <td>84</td>
                <td>69</td>
                <td>未知</td>
                <td class="suspicious-high"><a href="./bairun/report.html" class="site-link">531</a></td>
            </tr>
            <tr>
                <td>/var/www/jilian</td>
                <td><a href="https://www.jliangroup.com" target="_blank" class="site-link">https://www.jliangroup.com</a></td>
                <td>jilian</td>
                <td>530.63</td>
                <td>76</td>
                <td>11</td>
                <td>2</td>
                <td>0</td>
                <td>未知</td>
                <td class="suspicious-medium"><a href="./jilian/report.html" class="site-link">2</a></td>
            </tr>
            <tr>
                <td>/var/www/lanchang</td>
                <td><a href="https://blovedream.net" target="_blank" class="site-link">https://blovedream.net</a></td>
                <td>lanchang</td>
                <td>104.81</td>
                <td>129</td>
                <td>226</td>
                <td>4</td>
                <td>0</td>
                <td>未知</td>
                <td class="suspicious-high"><a href="./lanchang/report.html" class="site-link">310</a></td>
            </tr>
            <tr>
                <td>/var/www/ouniluomanru</td>
                <td><a href="http://ouniluomanru.yhct.top/" target="_blank" class="site-link">http://ouniluomanru.yhct.top/</a></td>
                <td>ouniluomanru</td>
                <td>56.84</td>
                <td>40</td>
                <td>7</td>
                <td>2</td>
                <td>0</td>
                <td>未知</td>
                <td class="suspicious-high"><a href="./ouniluomanru/report.html" class="site-link">73</a></td>
            </tr>
            <tr>
                <td>/var/www/ouniluoman</td>
                <td><a href="https://www.uni-laman.cn/" target="_blank" class="site-link">https://www.uni-laman.cn/</a></td>
                <td>ouniluoman</td>
                <td>47.70</td>
                <td>70</td>
                <td>7</td>
                <td>2</td>
                <td>0</td>
                <td>未知</td>
                <td class="suspicious-high"><a href="./ouniluoman/report.html" class="site-link">50</a></td>
            </tr>
        </tbody>
    </table>
    </section>
    
    <section>
        <h2>可疑内容统计</h2>
        <div class="summary-cards">
            <div class="card" style="border-top-color: var(--danger-color);">
                <h3>可疑文章</h3>
                <p class="value" style="color: var(--danger-color);">456</p>
            </div>
            <div class="card" style="border-top-color: var(--warning-color);">
                <h3>可疑用户</h3>
                <p class="value" style="color: var(--warning-color);">6</p>
            </div>
            <div class="card" style="border-top-color: var(--danger-color);">
                <h3>可疑选项</h3>
                <p class="value" style="color: var(--danger-color);">58</p>
            </div>
            <div class="card" style="border-top-color: var(--warning-color);">
                <h3>可疑评论</h3>
                <p class="value" style="color: var(--warning-color);">10</p>
            </div>
            <div class="card" style="border-top-color: var(--danger-color);">
                <h3>可疑元数据</h3>
                <p class="value" style="color: var(--danger-color);">5256</p>
            </div>
            <div class="card" style="border-top-color: var(--warning-color);">
                <h3>可疑日期</h3>
                <p class="value" style="color: var(--warning-color);">0</p>
            </div>
        </div>
    </section>
    
    <footer>
        <p>WordPress数据库扫描工具 - 生成于 2025年05月13日 03:24:08</p>
    </footer>
</div>
</body>
</html>
