server {
    listen 80;
    server_name example.com www.example.com;  # 请替换为您的域名
    root /var/www/example.com;                # 请替换为您的WordPress安装目录
    index index.php index.html;
    
    # 日志配置
    access_log /var/log/nginx/example.com.access.log;
    error_log /var/log/nginx/example.com.error.log;
    
    # 隐藏NGINX版本号
    server_tokens off;
    
    # WordPress固定链接规则
    location / {
        try_files $uri $uri/ /index.php?$args;
    }
    
    # 限制PHP文件执行
    location ~ \.php$ {
        # 防止PHP路径解析漏洞
        if ($fastcgi_script_name ~ \..*\/.*php) {
            return 403;
        }
        
        try_files $uri =404;
        fastcgi_split_path_info ^(.+\.php)(/.+)$;
        fastcgi_pass unix:/var/run/php/php7.4-fpm.sock;  # 根据您的PHP版本调整
        fastcgi_index index.php;
        include fastcgi_params;
        fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
        fastcgi_param PATH_INFO $fastcgi_path_info;
        
        # 隐藏PHP版本号
        fastcgi_hide_header X-Powered-By;
        proxy_hide_header X-Powered-By;
        
        # 安全响应头
        add_header X-Frame-Options "SAMEORIGIN" always;
        add_header X-Content-Type-Options "nosniff" always;
        add_header X-XSS-Protection "1; mode=block" always;
        add_header Referrer-Policy "strict-origin-when-cross-origin" always;
        
        # 增加FastCGI超时时间
        fastcgi_read_timeout 300;
    }
    
    # 限制WordPress XMLRPC访问
    location = /xmlrpc.php {
        # 如需允许特定IP访问，请按如下格式添加
        # allow ***************;
        deny all;
        access_log off;
        log_not_found off;
    }
    
    # 限制WordPress登录页面防止暴力攻击
    location = /wp-login.php {
        # 建议配置limit_req_zone，例如：
        # limit_req zone=wordpress burst=3 nodelay;
        fastcgi_pass unix:/var/run/php/php7.4-fpm.sock;
    }
    
    # 禁止访问敏感文件
    location ~ /\.(git|svn|ht) {
        deny all;
        access_log off;
        log_not_found off;
    }
    
    # 禁止访问WordPress配置文件
    location ~* wp-config.php {
        deny all;
    }
    
    # 禁止访问上传目录中的PHP文件
    location ~* /(?:uploads|files|wp-content|wp-includes)/.*\.php$ {
        deny all;
        access_log off;
        log_not_found off;
    }
    
    # 常规文件处理
    location = /favicon.ico {
        log_not_found off;
        access_log off;
    }
    
    location = /robots.txt {
        log_not_found off;
        access_log off;
        allow all;
    }
    
    # 静态资源缓存
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot|otf)$ {
        expires max;
        log_not_found off;
        access_log off;
        add_header Cache-Control "public, max-age=31536000";
    }
    
    # 限制请求方法
    if ($request_method !~ ^(GET|POST|HEAD)$) {
        return 444;
    }
    
    # 禁用目录列表
    autoindex off;
    
    # 上传文件大小限制
    client_max_body_size 20M;
} 