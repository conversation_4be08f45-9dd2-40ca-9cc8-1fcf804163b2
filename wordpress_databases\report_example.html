<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WordPress数据库扫描报告</title>
    <style>
        :root {
            --primary-color: #0073aa;
            --secondary-color: #005177;
            --accent-color: #d54e21;
            --light-gray: #f5f5f5;
            --dark-gray: #333;
            --success-color: #46b450;
            --warning-color: #ffb900;
            --danger-color: #dc3232;
        }
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen-Sans, Ubuntu, Cantarell, "Helvetica Neue", sans-serif;
            line-height: 1.6;
            color: #333;
            background-color: #f1f1f1;
        }
        
        .container {
            width: 90%;
            max-width: 1200px;
            margin: 2rem auto;
        }
        
        header {
            background-color: var(--primary-color);
            color: white;
            padding: 1.5rem;
            border-radius: 5px 5px 0 0;
        }
        
        .report-meta {
            display: flex;
            justify-content: space-between;
            background-color: var(--secondary-color);
            color: white;
            padding: 0.5rem 1.5rem;
            font-size: 0.9rem;
        }
        
        .content {
            background-color: white;
            padding: 1.5rem;
            border-radius: 0 0 5px 5px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }
        
        h1 {
            font-size: 1.8rem;
            margin-bottom: 0.5rem;
        }
        
        h2 {
            font-size: 1.4rem;
            margin: 1.5rem 0 1rem;
            padding-bottom: 0.5rem;
            border-bottom: 1px solid #eee;
            color: var(--primary-color);
        }
        
        .summary-cards {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
            gap: 1rem;
            margin: 1.5rem 0;
        }
        
        .card {
            background-color: white;
            border-radius: 5px;
            padding: 1.5rem;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            border-top: 3px solid var(--primary-color);
        }
        
        .card h3 {
            font-size: 1.1rem;
            margin-bottom: 0.5rem;
            color: var(--dark-gray);
        }
        
        .card p.value {
            font-size: 2rem;
            font-weight: bold;
            color: var(--primary-color);
            margin-bottom: 0.5rem;
        }
        
        .card.warning {
            border-top-color: var(--warning-color);
        }
        
        .card.warning p.value {
            color: var(--warning-color);
        }
        
        .card.danger {
            border-top-color: var(--danger-color);
        }
        
        .card.danger p.value {
            color: var(--danger-color);
        }
        
        .card.success {
            border-top-color: var(--success-color);
        }
        
        .card.success p.value {
            color: var(--success-color);
        }

        table {
            width: 100%;
            border-collapse: collapse;
            margin: 1.5rem 0;
        }
        
        thead {
            background-color: var(--light-gray);
        }
        
        th, td {
            padding: 0.75rem;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        
        th {
            font-weight: 600;
        }
        
        tr:hover {
            background-color: rgba(0, 115, 170, 0.05);
        }
        
        tr.child-site {
            background-color: rgba(0, 115, 170, 0.03);
        }
        
        tr.child-site td {
            padding-left: 2rem;
            font-size: 0.95em;
        }
        
        .chart-container {
            height: 400px;
            margin: 2rem 0;
            border: 1px solid #ddd;
            border-radius: 5px;
            padding: 1rem;
            background-color: white;
        }
        
        .placeholder-chart {
            width: 100%;
            height: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
            background-color: var(--light-gray);
            color: #777;
            font-style: italic;
        }
        
        footer {
            text-align: center;
            margin-top: 2rem;
            padding: 1rem;
            color: #777;
            font-size: 0.9rem;
        }
        
        @media (max-width: 768px) {
            .container {
                width: 95%;
            }
            
            .summary-cards {
                grid-template-columns: 1fr;
            }
        }
        
        /* 分页控制样式 */
        .pagination-controls {
            display: flex;
            justify-content: center;
            margin: 1.5rem 0;
            gap: 0.5rem;
        }
        
        .pagination-controls button {
            padding: 0.5rem 1rem;
            background-color: white;
            border: 1px solid #ddd;
            border-radius: 3px;
            cursor: pointer;
            transition: background-color 0.2s;
        }
        
        .pagination-controls button:hover {
            background-color: var(--light-gray);
        }
        
        .pagination-controls button.active {
            background-color: var(--primary-color);
            color: white;
            border-color: var(--primary-color);
        }
        
        .pagination-controls button:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }
        
        /* 搜索框样式 */
        .search-box {
            margin: 1rem 0;
            display: flex;
            gap: 0.5rem;
        }
        
        .search-box input {
            flex: 1;
            padding: 0.5rem;
            border: 1px solid #ddd;
            border-radius: 3px;
        }
        
        .search-box button {
            padding: 0.5rem 1rem;
            background-color: var(--primary-color);
            color: white;
            border: none;
            border-radius: 3px;
            cursor: pointer;
        }
        
        /* 可疑内容高亮 */
        .suspicious {
            background-color: rgba(220, 50, 50, 0.1);
        }
        
        .suspicious td {
            border-left: 3px solid var(--danger-color);
        }
        
        /* 折叠区域 */
        .collapsible {
            margin-top: 1rem;
        }
        
        .collapsible-header {
            background-color: var(--light-gray);
            padding: 0.75rem;
            border-radius: 3px;
            cursor: pointer;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .collapsible-header::after {
            content: "+";
            font-size: 1.2rem;
            font-weight: bold;
        }
        
        .collapsible-header.active::after {
            content: "-";
        }
        
        .collapsible-content {
            display: none;
            padding: 1rem;
            border: 1px solid #ddd;
            border-top: none;
            border-radius: 0 0 3px 3px;
        }
    </style>
    <script>
        // 页面加载完成后执行
        document.addEventListener('DOMContentLoaded', function() {
            // 折叠面板功能
            const collapsibles = document.querySelectorAll('.collapsible-header');
            collapsibles.forEach(header => {
                header.addEventListener('click', function() {
                    this.classList.toggle('active');
                    const content = this.nextElementSibling;
                    if (content.style.display === 'block') {
                        content.style.display = 'none';
                    } else {
                        content.style.display = 'block';
                    }
                });
            });
            
            // 站点表格分页功能
            initPagination('site-url-table', 5);
            
            // 搜索功能
            const searchButton = document.getElementById('search-button');
            const searchInput = document.getElementById('search-input');
            
            if (searchButton && searchInput) {
                searchButton.addEventListener('click', function() {
                    searchSites(searchInput.value);
                });
                
                searchInput.addEventListener('keypress', function(e) {
                    if (e.key === 'Enter') {
                        searchSites(searchInput.value);
                    }
                });
            }
        });
        
        // 分页逻辑
        function initPagination(tableId, itemsPerPage) {
            const table = document.getElementById(tableId);
            if (!table) return;
            
            const tbody = table.querySelector('tbody');
            const rows = tbody.querySelectorAll('tr');
            const pageCount = Math.ceil(rows.length / itemsPerPage);
            
            // 创建分页控制区
            const paginationDiv = document.createElement('div');
            paginationDiv.className = 'pagination-controls';
            table.parentNode.insertBefore(paginationDiv, table.nextSibling);
            
            // 添加页码按钮
            const prevBtn = document.createElement('button');
            prevBtn.innerText = '上一页';
            prevBtn.addEventListener('click', () => goToPage(currentPage - 1));
            paginationDiv.appendChild(prevBtn);
            
            // 页码按钮
            for (let i = 1; i <= pageCount; i++) {
                const pageBtn = document.createElement('button');
                pageBtn.innerText = i;
                pageBtn.addEventListener('click', () => goToPage(i));
                paginationDiv.appendChild(pageBtn);
            }
            
            const nextBtn = document.createElement('button');
            nextBtn.innerText = '下一页';
            nextBtn.addEventListener('click', () => goToPage(currentPage + 1));
            paginationDiv.appendChild(nextBtn);
            
            // 显示第一页
            let currentPage = 1;
            goToPage(currentPage);
            
            function goToPage(page) {
                if (page < 1 || page > pageCount) return;
                
                currentPage = page;
                const start = (page - 1) * itemsPerPage;
                const end = start + itemsPerPage;
                
                // 隐藏所有行
                rows.forEach(row => row.style.display = 'none');
                
                // 显示当前页的行
                for (let i = start; i < end && i < rows.length; i++) {
                    rows[i].style.display = '';
                }
                
                // 更新按钮状态
                updatePaginationButtons();
            }
            
            function updatePaginationButtons() {
                const buttons = paginationDiv.querySelectorAll('button');
                buttons.forEach((button, i) => {
                    if (i === 0) { // 前一页按钮
                        button.disabled = currentPage === 1;
                    } else if (i === buttons.length - 1) { // 下一页按钮
                        button.disabled = currentPage === pageCount;
                    } else { // 页码按钮
                        button.classList.toggle('active', i === currentPage);
                    }
                });
            }
        }
        
        // 搜索功能
        function searchSites(query) {
            if (!query) return;
            
            query = query.toLowerCase();
            const table = document.getElementById('site-url-table');
            const rows = table.querySelectorAll('tbody tr');
            
            let hasResults = false;
            
            rows.forEach(row => {
                const text = row.textContent.toLowerCase();
                if (text.includes(query)) {
                    row.style.display = '';
                    hasResults = true;
                } else {
                    row.style.display = 'none';
                }
            });
            
            // 隐藏分页控制
            const pagination = table.nextElementSibling;
            if (pagination && pagination.classList.contains('pagination-controls')) {
                pagination.style.display = query ? 'none' : 'flex';
            }
            
            // 显示搜索结果信息
            const resultInfo = document.getElementById('search-result-info');
            if (resultInfo) {
                resultInfo.textContent = hasResults 
                    ? `找到包含"${query}"的结果` 
                    : `未找到包含"${query}"的结果`;
                resultInfo.style.display = 'block';
            }
        }
    </script>
</head>
<body>
    <div class="container">
        <header>
            <h1>WordPress数据库扫描报告</h1>
            <p>服务器：example-server.com</p>
        </header>
        <div class="report-meta">
            <span>生成时间：2023年11月3日 14:30:45</span>
            <span>扫描耗时：2分钟35秒</span>
        </div>
        <div class="content">
            <section>
                <h2>概览</h2>
                <div class="summary-cards">
                    <div class="card">
                        <h3>WordPress数据库总数</h3>
                        <p class="value">8</p>
                    </div>
                    <div class="card">
                        <h3>总数据库大小</h3>
                        <p class="value">2.43 GB</p>
                    </div>
                    <div class="card">
                        <h3>总网站数</h3>
                        <p class="value">12</p>
                    </div>
                    <div class="card">
                        <h3>扫描站点总数</h3>
                        <p class="value">10</p>
                    </div>
                </div>
            </section>
            
            <section>
                <h2>数据库详情</h2>
                <table>
                    <thead>
                        <tr>
                            <th>数据库名称</th>
                            <th>大小</th>
                            <th>表数量</th>
                            <th>文章数</th>
                            <th>用户数</th>
                            <th>评论数</th>
                            <th>WP版本</th>
                            <th>最后更新</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>wp_main</td>
                            <td>850 MB</td>
                            <td>42</td>
                            <td>1,250</td>
                            <td>145</td>
                            <td>3,721</td>
                            <td>6.3.1</td>
                            <td>2023-10-25</td>
                        </tr>
                        <tr>
                            <td>wp_blog</td>
                            <td>420 MB</td>
                            <td>28</td>
                            <td>852</td>
                            <td>24</td>
                            <td>1,568</td>
                            <td>6.3.1</td>
                            <td>2023-10-28</td>
                        </tr>
                        <tr>
                            <td>wp_shop</td>
                            <td>680 MB</td>
                            <td>56</td>
                            <td>320</td>
                            <td>1,240</td>
                            <td>845</td>
                            <td>6.2.2</td>
                            <td>2023-09-15</td>
                        </tr>
                        <tr>
                            <td>wp_forum</td>
                            <td>215 MB</td>
                            <td>32</td>
                            <td>125</td>
                            <td>1,876</td>
                            <td>8,541</td>
                            <td>6.3.0</td>
                            <td>2023-10-10</td>
                        </tr>
                        <tr>
                            <td>wp_test</td>
                            <td>85 MB</td>
                            <td>24</td>
                            <td>52</td>
                            <td>8</td>
                            <td>12</td>
                            <td>6.3.1</td>
                            <td>2023-10-30</td>
                        </tr>
                        <tr>
                            <td>wp_dev</td>
                            <td>92 MB</td>
                            <td>24</td>
                            <td>78</td>
                            <td>12</td>
                            <td>35</td>
                            <td>6.4.0</td>
                            <td>2023-11-01</td>
                        </tr>
                        <tr>
                            <td>wp_archive</td>
                            <td>320 MB</td>
                            <td>28</td>
                            <td>1,540</td>
                            <td>56</td>
                            <td>4,215</td>
                            <td>5.9.5</td>
                            <td>2022-12-15</td>
                        </tr>
                        <tr>
                            <td>wp_community</td>
                            <td>175 MB</td>
                            <td>36</td>
                            <td>325</td>
                            <td>452</td>
                            <td>2,845</td>
                            <td>6.3.1</td>
                            <td>2023-10-22</td>
                        </tr>
                    </tbody>
                </table>
            </section>
            
            <section>
                <h2>站点URL信息</h2>
                <div class="search-box">
                    <input type="text" id="search-input" placeholder="搜索站点...">
                    <button id="search-button">搜索</button>
                </div>
                <p id="search-result-info" style="display: none; margin: 1rem 0; font-style: italic;"></p>
                <table id="site-url-table">
                    <thead>
                        <tr>
                            <th>数据库名称</th>
                            <th>站点URL</th>
                            <th>首页URL</th>
                            <th>多站点</th>
                            <th>状态</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>wp_main</td>
                            <td>https://example.com/wordpress</td>
                            <td>https://example.com</td>
                            <td>否</td>
                            <td>正常</td>
                        </tr>
                        <tr>
                            <td>wp_blog</td>
                            <td>https://blog.example.com</td>
                            <td>https://blog.example.com</td>
                            <td>否</td>
                            <td>正常</td>
                        </tr>
                        <tr>
                            <td>wp_shop</td>
                            <td>https://shop.example.com</td>
                            <td>https://shop.example.com</td>
                            <td>否</td>
                            <td>正常</td>
                        </tr>
                        <tr>
                            <td>wp_forum</td>
                            <td>https://forum.example.com</td>
                            <td>https://forum.example.com</td>
                            <td>否</td>
                            <td>正常</td>
                        </tr>
                        <tr>
                            <td>wp_community</td>
                            <td>https://community.example.com</td>
                            <td>https://community.example.com</td>
                            <td>是</td>
                            <td>正常</td>
                        </tr>
                        <tr class="child-site">
                            <td>└ 子站点 1</td>
                            <td>https://community.example.com/site1</td>
                            <td>https://community.example.com/site1</td>
                            <td>-</td>
                            <td>正常</td>
                        </tr>
                        <tr class="child-site">
                            <td>└ 子站点 2</td>
                            <td>https://community.example.com/site2</td>
                            <td>https://community.example.com/site2</td>
                            <td>-</td>
                            <td>正常</td>
                        </tr>
                        <tr>
                            <td>wp_test</td>
                            <td>https://test.example.com</td>
                            <td>https://test.example.com</td>
                            <td>否</td>
                            <td>测试中</td>
                        </tr>
                        <tr>
                            <td>wp_dev</td>
                            <td>https://dev.example.com</td>
                            <td>https://dev.example.com</td>
                            <td>否</td>
                            <td>开发中</td>
                        </tr>
                        <tr>
                            <td>wp_archive</td>
                            <td>https://archive.example.com</td>
                            <td>https://archive.example.com</td>
                            <td>否</td>
                            <td>归档</td>
                        </tr>
                    </tbody>
                </table>
            </section>
            
            <section>
                <h2>垃圾信息检测结果</h2>
                <div class="summary-cards">
                    <div class="card">
                        <h3>扫描内容总数</h3>
                        <p class="value">14,857</p>
                    </div>
                    <div class="card" style="border-top-color: var(--danger-color);">
                        <h3>可疑内容数</h3>
                        <p class="value" style="color: var(--danger-color);">23</p>
                    </div>
                    <div class="card">
                        <h3>可疑站点数</h3>
                        <p class="value">3</p>
                    </div>
                    <div class="card">
                        <h3>检测关键词数</h3>
                        <p class="value">42</p>
                    </div>
                </div>
                
                <div class="collapsible">
                    <div class="collapsible-header">可疑文章内容</div>
                    <div class="collapsible-content">
                        <table>
                            <thead>
                                <tr>
                                    <th>数据库</th>
                                    <th>文章ID</th>
                                    <th>标题</th>
                                    <th>作者</th>
                                    <th>发布日期</th>
                                    <th>匹配关键词</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr class="suspicious">
                                    <td>wp_blog</td>
                                    <td>432</td>
                                    <td>网站流量提升技巧</td>
                                    <td>author1</td>
                                    <td>2023-10-15</td>
                                    <td>eval(), base64_decode</td>
                                    <td><a href="#">查看</a></td>
                                </tr>
                                <tr class="suspicious">
                                    <td>wp_shop</td>
                                    <td>789</td>
                                    <td>产品促销页面</td>
                                    <td>admin</td>
                                    <td>2023-10-05</td>
                                    <td>gzinflate()</td>
                                    <td><a href="#">查看</a></td>
                                </tr>
                                <tr class="suspicious">
                                    <td>wp_main</td>
                                    <td>125</td>
                                    <td>关于我们</td>
                                    <td>editor</td>
                                    <td>2023-09-12</td>
                                    <td>adult, porn</td>
                                    <td><a href="#">查看</a></td>
                                </tr>
                                <tr class="suspicious">
                                    <td>wp_forum</td>
                                    <td>236</td>
                                    <td>用户交流专区</td>
                                    <td>moderator</td>
                                    <td>2023-09-08</td>
                                    <td>成人, 色情</td>
                                    <td><a href="#">查看</a></td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
                
                <div class="collapsible">
                    <div class="collapsible-header">可疑选项值</div>
                    <div class="collapsible-content">
                        <table>
                            <thead>
                                <tr>
                                    <th>数据库</th>
                                    <th>选项ID</th>
                                    <th>选项名称</th>
                                    <th>自动加载</th>
                                    <th>匹配关键词</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr class="suspicious">
                                    <td>wp_main</td>
                                    <td>256</td>
                                    <td>widget_custom_html</td>
                                    <td>是</td>
                                    <td>&lt;script&gt;</td>
                                    <td><a href="#">查看</a></td>
                                </tr>
                                <tr class="suspicious">
                                    <td>wp_blog</td>
                                    <td>357</td>
                                    <td>theme_mods_twentytwenty</td>
                                    <td>是</td>
                                    <td>eval(), base64_decode</td>
                                    <td><a href="#">查看</a></td>
                                </tr>
                                <tr class="suspicious">
                                    <td>wp_forum</td>
                                    <td>452</td>
                                    <td>active_plugins</td>
                                    <td>是</td>
                                    <td>eval()</td>
                                    <td><a href="#">查看</a></td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
                
                <div class="collapsible">
                    <div class="collapsible-header">可疑评论内容</div>
                    <div class="collapsible-content">
                        <table>
                            <thead>
                                <tr>
                                    <th>数据库</th>
                                    <th>文章ID</th>
                                    <th>评论日期</th>
                                    <th>匹配关键词</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr class="suspicious">
                                    <td>wp_blog</td>
                                    <td>432</td>
                                    <td>2023-10-22</td>
                                    <td>&lt;script&gt;</td>
                                    <td><a href="#">查看</a></td>
                                </tr>
                                <tr class="suspicious">
                                    <td>wp_forum</td>
                                    <td>785</td>
                                    <td>2023-10-18</td>
                                    <td>adult, porn, xxx</td>
                                    <td><a href="#">查看</a></td>
                                </tr>
                                <tr class="suspicious">
                                    <td>wp_shop</td>
                                    <td>215</td>
                                    <td>2023-10-10</td>
                                    <td>iframe</td>
                                    <td><a href="#">查看</a></td>
                                </tr>
                                <tr class="suspicious">
                                    <td>wp_main</td>
                                    <td>125</td>
                                    <td>2023-10-05</td>
                                    <td>telegram</td>
                                    <td><a href="#">查看</a></td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
                
                <div class="collapsible">
                    <div class="collapsible-header">可疑元数据</div>
                    <div class="collapsible-content">
                        <table>
                            <thead>
                                <tr>
                                    <th>数据库</th>
                                    <th>元数据ID</th>
                                    <th>文章ID</th>
                                    <th>元数据键</th>
                                    <th>匹配关键词</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr class="suspicious">
                                    <td>wp_blog</td>
                                    <td>1245</td>
                                    <td>432</td>
                                    <td>_custom_html</td>
                                    <td>&lt;script&gt;, iframe</td>
                                    <td><a href="#">查看</a></td>
                                </tr>
                                <tr class="suspicious">
                                    <td>wp_shop</td>
                                    <td>2584</td>
                                    <td>789</td>
                                    <td>_product_attributes</td>
                                    <td>eval(), base64_decode</td>
                                    <td><a href="#">查看</a></td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
                
                <div class="collapsible">
                    <div class="collapsible-header">可疑用户账号</div>
                    <div class="collapsible-content">
                        <table>
                            <thead>
                                <tr>
                                    <th>数据库</th>
                                    <th>用户名</th>
                                    <th>创建时间</th>
                                    <th>匹配条件</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr class="suspicious">
                                    <td>wp_main</td>
                                    <td>admin123</td>
                                    <td>2023-10-20</td>
                                    <td>admin</td>
                                    <td><a href="#">查看</a></td>
                                </tr>
                                <tr class="suspicious">
                                    <td>wp_blog</td>
                                    <td>test_user</td>
                                    <td>2023-10-21</td>
                                    <td>test</td>
                                    <td><a href="#">查看</a></td>
                                </tr>
                                <tr class="suspicious">
                                    <td>wp_shop</td>
                                    <td>temp_admin</td>
                                    <td>2023-10-22</td>
                                    <td>temp, admin</td>
                                    <td><a href="#">查看</a></td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
                
                <div class="collapsible">
                    <div class="collapsible-header">可疑日期内容</div>
                    <div class="collapsible-content">
                        <table>
                            <thead>
                                <tr>
                                    <th>数据库</th>
                                    <th>文章ID</th>
                                    <th>标题</th>
                                    <th>作者</th>
                                    <th>发布/修改日期</th>
                                    <th>异常</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr class="suspicious">
                                    <td>wp_main</td>
                                    <td>567</td>
                                    <td>网站公告</td>
                                    <td>admin123</td>
                                    <td>2024-01-15</td>
                                    <td>未来日期</td>
                                    <td><a href="#">查看</a></td>
                                </tr>
                                <tr class="suspicious">
                                    <td>wp_blog</td>
                                    <td>678</td>
                                    <td>博客动态</td>
                                    <td>editor</td>
                                    <td>2024-02-10</td>
                                    <td>未来日期</td>
                                    <td><a href="#">查看</a></td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </section>
            
            <section>
                <h2>插件使用情况</h2>
                <div class="chart-container">
                    <div class="placeholder-chart">此处将显示插件使用情况图表</div>
                </div>
                <table>
                    <thead>
                        <tr>
                            <th>插件名称</th>
                            <th>使用站点数</th>
                            <th>版本分布</th>
                            <th>最新版本</th>
                            <th>状态</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>Yoast SEO</td>
                            <td>7</td>
                            <td>20.5 (5), 20.4 (2)</td>
                            <td>20.5</td>
                            <td>最新</td>
                        </tr>
                        <tr>
                            <td>WooCommerce</td>
                            <td>3</td>
                            <td>8.0.3 (2), 7.9.0 (1)</td>
                            <td>8.0.3</td>
                            <td>需更新 (1)</td>
                        </tr>
                        <tr>
                            <td>Elementor</td>
                            <td>5</td>
                            <td>3.16.3 (3), 3.15.1 (2)</td>
                            <td>3.16.4</td>
                            <td>需更新 (5)</td>
                        </tr>
                        <tr>
                            <td>Contact Form 7</td>
                            <td>8</td>
                            <td>5.8.1 (8)</td>
                            <td>5.8.1</td>
                            <td>最新</td>
                        </tr>
                    </tbody>
                </table>
            </section>
            
            <section>
                <h2>性能分析</h2>
                <div class="chart-container">
                    <div class="placeholder-chart">此处将显示数据库性能分析图表</div>
                </div>
            </section>
            
            <section>
                <h2>安全评估</h2>
                <table>
                    <thead>
                        <tr>
                            <th>数据库</th>
                            <th>风险级别</th>
                            <th>风险描述</th>
                            <th>建议操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>wp_archive</td>
                            <td style="color: var(--danger-color);">高</td>
                            <td>WordPress版本过旧 (5.9.5)</td>
                            <td>尽快更新至最新版本</td>
                        </tr>
                        <tr>
                            <td>wp_shop</td>
                            <td style="color: var(--warning-color);">中</td>
                            <td>未使用表前缀，使用默认前缀wp_</td>
                            <td>考虑更改表前缀</td>
                        </tr>
                        <tr>
                            <td>wp_test</td>
                            <td style="color: var(--warning-color);">中</td>
                            <td>测试环境可公开访问</td>
                            <td>限制测试环境访问权限</td>
                        </tr>
                    </tbody>
                </table>
            </section>
            
            <section>
                <h2>优化建议</h2>
                <ul style="list-style-type: none; padding: 1rem;">
                    <li style="margin-bottom: 1rem; padding: 1rem; background-color: var(--light-gray); border-left: 4px solid var(--primary-color); border-radius: 0 5px 5px 0;">
                        <strong>清理wp_post_meta表</strong>: wp_main和wp_blog数据库中的wp_post_meta表大小异常。建议清理无用的元数据以减小数据库大小。
                    </li>
                    <li style="margin-bottom: 1rem; padding: 1rem; background-color: var(--light-gray); border-left: 4px solid var(--primary-color); border-radius: 0 5px 5px 0;">
                        <strong>优化数据库表</strong>: 建议为所有数据库表执行OPTIMIZE TABLE操作，可减少总存储空间约15%。
                    </li>
                    <li style="margin-bottom: 1rem; padding: 1rem; background-color: var(--light-gray); border-left: 4px solid var(--primary-color); border-radius: 0 5px 5px 0;">
                        <strong>移除废弃的插件数据</strong>: 在wp_forum数据库中发现已停用但未删除的插件数据，建议清理。
                    </li>
                </ul>
            </section>
        </div>
        <footer>
            <p>WordPress数据库扫描报告 | 生成于2023年11月3日 | v1.0</p>
        </footer>
    </div>
</body>
</html> 