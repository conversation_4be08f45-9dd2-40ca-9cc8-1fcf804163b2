<?php
/* ============================================================================================================
	 This software is provided "as is" and any express or implied warranties, including, but not limited to, the
	 implied warranties of merchantibility and fitness for a particular purpose are disclaimed. In no event shall
	 the copyright owner or contributors be liable for any direct, indirect, incidental, special, exemplary, or
	 consequential damages (including, but not limited to, procurement of substitute goods or services; loss of
	 use, data, or profits; or business interruption) however caused and on any theory of liability, whether in
	 contract, strict liability, or tort (including negligence or otherwise) arising in any way out of the use of
	 this software, even if advised of the possibility of such damage.

	 This software is provided free-to-use, but is not free software.  The copyright and ownership remains
	 entirely with the author.  Please distribute and use as necessary, in a personal or commercial environment,
	 but it cannot be sold or re-used without express consent from the author.
   ============================================================================================================ */

/**
 * CSV导出功能
 * 
 * 安全设计:
 * 1. 使用nonce验证请求的合法性
 * 2. 检查当前用户是否有权限查看审核日志
 * 3. 直接输出CSV内容，不在服务器上保存文件
 */

// 防止直接访问
if (!defined('ABSPATH')) {
	exit;
}

// 设置超时时间，防止长时间运行
set_time_limit(300);

/**
 * 防爬虫检查
 */
function is_bot() {
	$user_agent = isset($_SERVER['HTTP_USER_AGENT']) ? $_SERVER['HTTP_USER_AGENT'] : '';
	$bot_patterns = array(
		'bot', 'crawl', 'spider', 'slurp', 'mediapartners-google',
		'yandex', 'baidu', 'bingbot', 'facebookexternalhit', 'duckduckbot',
		'applebot', 'ia_archiver', 'msnbot', 'semrushbot', 'ahrefsbot'
	);
	
	foreach ($bot_patterns as $pattern) {
		if (stripos($user_agent, $pattern) !== false) {
			return true;
		}
	}
	
	// 检查浏览器特征标识，大多数爬虫不设置Accept或Referer头
	if (empty($_SERVER['HTTP_ACCEPT']) || empty($_SERVER['HTTP_REFERER'])) {
		return true;
	}
	
	return false;
}

// 防爬虫保护
if (is_bot()) {
	wp_die(__('访问被拒绝', 'audit-trail'), __('安全错误', 'audit-trail'), array('response' => 403));
}

// 安全头
if (!headers_sent()) {
	header('X-Content-Type-Options: nosniff');
	header('X-XSS-Protection: 1; mode=block');
	header('Content-Security-Policy: default-src \'none\'; frame-ancestors \'none\'');
	header('X-Frame-Options: DENY');
	header('Referrer-Policy: strict-origin-when-cross-origin');
}

// 验证nonce
if (!isset($_GET['_wpnonce']) || !wp_verify_nonce($_GET['_wpnonce'], 'audit_csv_export')) {
	wp_die(__('安全验证失败', 'audit-trail'), __('安全错误', 'audit-trail'), array('response' => 403));
}

// 检查当前用户是否被禁止使用插件
$current_user_id = get_current_user_id();
$forbidden_users = explode(',', get_option('audit_forbidden_users', ''));
if (in_array($current_user_id, $forbidden_users)) {
	wp_die(__('您没有权限导出数据', 'audit-trail'), __('权限错误', 'audit-trail'), array('response' => 403));
}

// 检查用户权限
if (!current_user_can('publish_posts') && !current_user_can('audit_trail') && !current_user_can('edit_plugins')) {
	wp_die(__('您没有权限导出数据', 'audit-trail'), __('权限错误', 'audit-trail'), array('response' => 403));
}

global $wpdb;

// 构建SQL查询
$sql = "SELECT {$wpdb->prefix}audit_trail.happened_at as date, 
			   {$wpdb->users}.user_login as user, 
			   {$wpdb->prefix}audit_trail.operation, 
			   {$wpdb->prefix}audit_trail.data, 
			   {$wpdb->prefix}audit_trail.ip 
		FROM {$wpdb->prefix}audit_trail
		LEFT JOIN {$wpdb->users} ON {$wpdb->users}.ID = {$wpdb->prefix}audit_trail.user_id 
		WHERE 1=1";

// 处理搜索条件
$conditions = array();
$values = array();

if (isset($_GET['search_user']) && !empty($_GET['search_user'])) {
	$search_user = sanitize_text_field($_GET['search_user']);
	$sql .= " AND ({$wpdb->users}.user_login LIKE %s OR {$wpdb->users}.user_nicename LIKE %s)";
	$values[] = '%' . $wpdb->esc_like($search_user) . '%';
	$values[] = '%' . $wpdb->esc_like($search_user) . '%';
}

if (isset($_GET['search_operation']) && !empty($_GET['search_operation'])) {
	$search_operation = sanitize_text_field($_GET['search_operation']);
	$sql .= " AND {$wpdb->prefix}audit_trail.operation LIKE %s";
	$values[] = '%' . $wpdb->esc_like($search_operation) . '%';
}

if (isset($_GET['search_ip']) && !empty($_GET['search_ip'])) {
	$search_ip = sanitize_text_field($_GET['search_ip']);
	// 将IP地址转换为数字处理
	if (filter_var($search_ip, FILTER_VALIDATE_IP, FILTER_FLAG_IPV4)) {
		$ip_num = sprintf('%u', ip2long($search_ip));
		$sql .= " AND {$wpdb->prefix}audit_trail.ip = %d";
		$values[] = $ip_num;
	} else {
		// 模糊匹配IP地址
		$sql .= " AND %s LIKE CONCAT('%', INET_NTOA({$wpdb->prefix}audit_trail.ip), '%')";
		$values[] = $search_ip;
	}
}

if (isset($_GET['search_date_from']) && !empty($_GET['search_date_from'])) {
	$date_from = sanitize_text_field($_GET['search_date_from']);
	$sql .= " AND {$wpdb->prefix}audit_trail.happened_at >= %s";
	$values[] = $date_from . ' 00:00:00';
}

if (isset($_GET['search_date_to']) && !empty($_GET['search_date_to'])) {
	$date_to = sanitize_text_field($_GET['search_date_to']);
	$sql .= " AND {$wpdb->prefix}audit_trail.happened_at <= %s";
	$values[] = $date_to . ' 23:59:59';
}

// 排除"访问页面"日志
if (isset($_GET['exclude_page_views']) && $_GET['exclude_page_views'] == '1') {
	$sql .= " AND {$wpdb->prefix}audit_trail.operation != %s";
	$values[] = 'template_redirect';
}

$sql .= " ORDER BY {$wpdb->prefix}audit_trail.happened_at DESC";

// 限制查询结果以避免内存问题
if (!isset($_GET['no_limit']) || $_GET['no_limit'] != '1') {
	$sql .= " LIMIT 10000";
}

// 准备和执行查询
if (!empty($values)) {
	$sql = $wpdb->prepare($sql, $values);
}

$results = $wpdb->get_results($sql, ARRAY_A);

/**
 * 转义CSV数据中的逗号和引号
 */
function audit_csv_escape($data) {
	if (is_serialized($data)) {
		$data = maybe_unserialize($data);
		if (is_array($data)) {
			$lines = array();
			foreach ($data as $key => $value) {
				if (is_array($value)) {
					$value = json_encode($value);
				}
				$lines[] = "$key: $value";
			}
			$data = implode(" | ", $lines);
		}
	}
	
	// 处理JSON数据
	if (is_string($data) && !empty($data) && $data[0] == '{') {
		$json = json_decode($data);
		if (json_last_error() === JSON_ERROR_NONE && is_object($json)) {
			$lines = array();
			foreach ($json as $key => $value) {
				if (is_object($value) || is_array($value)) {
					$value = json_encode($value);
				}
				$lines[] = "$key: $value";
			}
			$data = implode(" | ", $lines);
		}
	}
	
	// CSV数据转义
	if (is_string($data)) {
		// 如果数据包含引号、逗号或换行符，需要进行处理
		if (strpos($data, '"') !== false || strpos($data, ',') !== false || 
			strpos($data, "\n") !== false || strpos($data, "\r") !== false) {
			// 替换所有双引号为两个双引号
			$data = str_replace('"', '""', $data);
			// 将整个字段用双引号括起来
			$data = '"' . $data . '"';
		}
	} elseif (is_null($data)) {
		$data = '';
	} else {
		$data = (string)$data;
	}
	
	return $data;
}

/**
 * 获取操作类型的友好名称
 */
function get_operation_friendly_name($operation) {
	// 将操作类型转换为易于理解的名称
	$operations = array(
		'save_post' => '保存文章',
		'delete_post' => '删除文章',
		'wp_login' => '用户登录',
		'wp_logout' => '用户登出',
		'user_register' => '用户注册',
		'profile_update' => '更新用户资料',
		'delete_user' => '删除用户',
		'switch_theme' => '切换主题',
		'edit_category' => '编辑分类',
		'add_category' => '添加分类',
		'delete_category' => '删除分类',
		'edit_comment' => '编辑评论',
		'delete_comment' => '删除评论',
		'add_attachment' => '添加附件',
		'edit_attachment' => '编辑附件',
		'delete_attachment' => '删除附件',
		'template_redirect' => '页面访问',
		'activate_plugin' => '激活插件',
		'deactivate_plugin' => '停用插件',
	);
	
	return isset($operations[$operation]) ? $operations[$operation] : $operation;
}

/**
 * 格式化IP地址
 */
function format_ip($ip) {
	// 如果是数字，尝试转换为点分四段式
	if (is_numeric($ip)) {
		return long2ip($ip);
	}
	
	return $ip;
}

/**
 * 格式化日期时间
 */
function format_date($date) {
	return get_date_from_gmt($date, get_option('date_format') . ' ' . get_option('time_format'));
}

// 设置HTTP头
$filename = 'audit-trail-' . date('Y-m-d') . '.csv';
header('Content-Type: text/csv; charset=utf-8');
header('Content-Disposition: attachment; filename="' . $filename . '"');
header('Pragma: no-cache');
header('Expires: 0');

// 打开输出流
$output = fopen('php://output', 'w');

// 写入标题行
fputcsv($output, array(
	__('日期', 'audit-trail'),
	__('用户', 'audit-trail'),
	__('操作类型', 'audit-trail'),
	__('操作详情', 'audit-trail'),
	__('IP地址', 'audit-trail')
));

// 记录导出操作
include_once(dirname(__FILE__) . '/models/audit.php');
AT_Audit::create('export_csv', '', 
	array(
		'search_params' => $_GET,
		'row_count' => count($results)
	), 
	sprintf('导出%d条审计记录到CSV', count($results))
);

// 写入数据行
foreach ($results as $row) {
	fputcsv($output, array(
		format_date($row['date']),
		audit_csv_escape($row['user'] ?: __('未知用户', 'audit-trail')),
		audit_csv_escape(get_operation_friendly_name($row['operation'])),
		audit_csv_escape($row['data']),
		format_ip($row['ip'])
	));
}

// 关闭输出流
fclose($output);
exit();

