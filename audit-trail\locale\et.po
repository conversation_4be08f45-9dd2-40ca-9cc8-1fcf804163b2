msgid ""
msgstr ""
"Project-Id-Version: Audit Trail ET\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2010-01-23 23:46+0200\n"
"PO-Revision-Date: \n"
"Last-Translator: <PERSON><PERSON><PERSON>\n"
"Language-Team: <PERSON> <<EMAIL>>\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=n != 1\n"
"X-Poedit-Language: French\n"
"X-Poedit-Country: FRANCE\n"
"X-Poedit-SourceCharset: utf-8\n"
"X-Poedit-KeywordsList: __;_e\n"
"X-Poedit-Basepath: .\n"
"X-Poedit-SearchPath-0: ..\n"

#: ../audit-trail.php:93
msgid "Audit Trail Help"
msgstr "Aide Audit Trail"

#: ../audit-trail.php:94
msgid "Audit Trail Documentation"
msgstr "Documentation Audit Trail"

#: ../audit-trail.php:95
msgid "Audit Trail Support Forum"
msgstr "Forum Audit Trail"

#: ../audit-trail.php:96
msgid "Audit Trail Bug Tracker"
msgstr "Bug Tracker Audit Trail"

#: ../audit-trail.php:97
msgid "Please read the documentation and check the bug tracker before asking a question."
msgstr "S'il vous plaît lisez la documentation et vérifiez le bug tracker avant de poser une question."

# ?
#: ../audit-trail.php:105
msgid "Trail"
msgstr "Trail"

#: ../audit-trail.php:180
msgid "Audit Trail"
msgstr "Audit Trail"

#: ../audit-trail.php:278
msgid "Options have been updated"
msgstr "Des options ont été mises à jour"

#: ../models/auditor.php:52
msgid "Post & page management"
msgstr "Le message et la page de gestion"

#: ../models/auditor.php:53
msgid "File attachments"
msgstr "Les pièces jointes"

#: ../models/auditor.php:54
msgid "User profiles & logins"
msgstr "Les profils utilisateur et les connexions"

#: ../models/auditor.php:55
msgid "Theme switching"
msgstr "Thème de commutation"

#: ../models/auditor.php:56
msgid "Link management"
msgstr "Gestion des liens"

#: ../models/auditor.php:57
msgid "Category management"
msgstr "Catégorie de gestion"

#: ../models/auditor.php:58
msgid "Comment management"
msgstr "Commentaire de gestion"

#: ../models/auditor.php:59
msgid "User page visits"
msgstr "Page utilisateur visites"

#: ../models/auditor.php:60
msgid "Audit Trail actions"
msgstr "Actions Audit Trail"

#: ../models/auditor.php:282
msgid "Theme switch"
msgstr "Commutateur de thème"

# ?
#: ../models/auditor.php:288
msgid "Profile updated for deleted user"
msgstr "Profil mis à jour pour utilisateur supprimé"

#: ../models/auditor.php:290
msgid "Profile updated"
msgstr "Profil mis à jour"

#: ../models/auditor.php:296
msgid "Logged In"
msgstr "Connectés"

#: ../models/auditor.php:300
msgid "Logged Out"
msgstr "Déconnecté"

#: ../models/auditor.php:304
msgid "Login failed"
msgstr "Échec de la connexion"

#: ../models/auditor.php:308
msgid "New user registration"
msgstr "Nouvel enregistrement d'utilisateur"

#: ../models/auditor.php:312
msgid "Retrieve password"
msgstr "Récupérer le mot de passe"

#: ../models/auditor.php:316
msgid "Delete user"
msgstr "Supprimer l'utilisateur"

#: ../models/auditor.php:320
msgid "Add link"
msgstr "Ajouter un lien"

#: ../models/auditor.php:325
msgid "Edit link"
msgstr "Modifier un lien"

#: ../models/auditor.php:329
msgid "Delete link"
msgstr "Supprimer un lien"

#: ../models/auditor.php:333
msgid "Edit category "
msgstr "Modifier une catégorie"

#: ../models/auditor.php:337
msgid "Add category"
msgstr "Ajouter une catégorie"

#: ../models/auditor.php:341
msgid "Delete category"
msgstr "Supprimer la catégorie"

#: ../models/auditor.php:345
msgid "Edit comment"
msgstr "Editer ce commentaire"

#: ../models/auditor.php:349
msgid "Delete comment"
msgstr "Supprimer le commentaire"

#: ../models/auditor.php:353
msgid "Delete post"
msgstr "Supprimer le message"

#: ../models/auditor.php:359
msgid "Save post"
msgstr "Enregistrer message"

#: ../models/auditor.php:361
msgid "Save page"
msgstr "Enregistrer la page"

#: ../models/auditor.php:370
msgid "Add attachment"
msgstr "Ajouter une pièce jointe"

#: ../models/auditor.php:378
msgid "Edit attachment"
msgstr "Modifier la pièce jointe"

#: ../models/auditor.php:382
msgid "View page"
msgstr "Voir la page"

#: ../models/auditor.php:386
msgid "Restored"
msgstr "Restauré"

#: ../models/auditor.php:434
msgid "<strong>ERROR</strong>: Incorrect password."
msgstr "<strong>ERREUR</strong>: Mot de passe incorrect."

#: ../models/pager.php:404
msgid "Previous"
msgstr "Précédente"

#: ../models/pager.php:405
msgid "Next"
msgstr "Suivant"

#: ../models/pager.php:463
#, php-format
msgid "%d per-page"
msgstr "%d par page"

# ?
#: ../models/pager.php:472
#, php-format
msgid "Displaying %s&#8211;%s of %s"
msgstr "Affichant %s&#8211;%s of %s"

#: ../view/admin/options.php:4
msgid "Audit Trail Options"
msgstr "Options de Audit Trail "

#: ../view/admin/options.php:11
msgid "Actions to monitor"
msgstr "Actions à suivre"

#: ../view/admin/options.php:23
msgid "There are no actions to monitor"
msgstr "Il n'y a pas d'actions a surveiller"

#: ../view/admin/options.php:26
msgid "Other Options"
msgstr "Autres Options"

#: ../view/admin/options.php:30
msgid "Plugin Support"
msgstr "Soutien des plugins"

#: ../view/admin/options.php:33
#, php-format
msgid "Click this if you have <a href=\"%s\">supported</a> the author"
msgstr "Cliquez sur ce si vous avez <a href=\"%s\">soutenu</a> l'auteur"

#: ../view/admin/options.php:37
msgid "Auto-expire"
msgstr "Auto-expiration"

#: ../view/admin/options.php:38
msgid "days (0 for no expiry)"
msgstr "jours (0 pour aucune d'expiration)"

#: ../view/admin/options.php:41
msgid "Ignore users"
msgstr "Ignorer des utilisateurs"

#: ../view/admin/options.php:42
msgid "separate user IDs with a comma"
msgstr "séparez ID utilisateur par une virgule"

#: ../view/admin/options.php:46
msgid "Save Options"
msgstr "Enregistrer les options"

#: ../view/admin/pager.php:6
msgid "Search"
msgstr "Recherche"

#: ../view/admin/pager.php:9
msgid "Results per page"
msgstr "Résultats par page"

#: ../view/admin/pager.php:16
msgid "Go"
msgstr "Allez"

#: ../view/admin/submenu.php:4
msgid "Options"
msgstr "Les options"

#: ../view/admin/submenu.php:5
msgid "Support"
msgstr "Soutien"

#: ../view/admin/support.php:5
msgid "Audit Trail | Support"
msgstr "Audit Trail | Soutien"

#: ../view/admin/support.php:9
msgid "Audit Trail is free to use - life is wonderful and lovely!  However, it has required a great deal of time and effort to develop and if it has been useful you can help support this development by <strong>making a small donation</strong>."
msgstr "Audit Trail est libre d'utiliser - la vie est merveilleuse et belle! Cependant, il a fallu beaucoup de temps et d'efforts pour développer et si elle a été utile, vous pouvez aider à soutenir ce développement en <strong>faisant un petit don</strong>."

#: ../view/admin/support.php:10
msgid "This will act as an incentive for me to carry on developing, providing countless hours of support, and including new features and suggestions. You get some useful software and I get to carry on making it.  Everybody wins."
msgstr "Ceci agira comme un encouragement pour moi de poursuivre le développement, offrant d'innombrables heures de soutien, et notamment de nouvelles fonctionnalités et des suggestions. Vous obtenez un logiciel utile et j'arrive à continuer à le faire. Tout le monde gagne."

#: ../view/admin/support.php:13
msgid "If you are using this plugin in a commercial setup, or feel that it's been particularly useful, then you may want to consider a <strong>commercial donation</strong>.  If you really really want to show your appreciation then there is the <strong>Super Smashing Great</strong> donation which, along with making my day, will earn you a badge of honour (125x125 image of your choosing + nofollow link) to be displayed on the Audit Trail page for a period of two months."
msgstr "Si vous utilisez ce plugin dans une configuration commerciale, ou le sentiment que ça a été particulièrement utile, alors vous pouvez envisager une <strong>donation commerciales</strong>. Si vous voulez vraiment montrer votre appréciation puis il ya le don de <strong>Smashing super Grand</strong> qui, avec prise de ma journée, vous fera gagner un badge d'honneur (125x125 image de votre choix + lien nofollow) pour être affiché sur la page Audit Trail pour une période de deux mois."

#: ../view/admin/support.php:36
msgid "Individual<br/>Donation"
msgstr "Individuels<br/>Don"

#: ../view/admin/support.php:57
msgid "Commercial<br/>Donation"
msgstr "Commercial<br/>Don"

#: ../view/admin/support.php:78
msgid "Super Smashing<br/>Great Donation"
msgstr "Super Smashing<br/>Grande donation"

#: ../view/admin/support.php:82
msgid "Translations"
msgstr "Traductions"

#: ../view/admin/support.php:84
msgid "If you're multi-lingual then you may want to consider donating a translation:"
msgstr "Si vous êtes multi-langues, alors vous pouvez envisager de faire don d'une traduction:"

#: ../view/admin/support.php:92
msgid "All translators will have a link to their website placed on the plugin homepage at <a href=\"http://urbangiraffe.com/plugins/audit-trail/\">UrbanGiraffe</a> and <a href=\"http://wordpress.org/extend/plugins/audit-trail/\">WordPress.org</a>, in addition to being an individual supporter."
msgstr "Tous les traducteurs auront un lien vers leur site placé sur la page d'accueil plugin à <a href=\"http://urbangiraffe.com/plugins/audit-trail/\">UrbanGiraffe</a> et <a href=\"http://wordpress.org/extend/plugins/audit-trail/\">WordPress.org</a> , en plus d'être un partisan individuels."

#: ../view/admin/support.php:93
msgid "Full details of producing a translation can be found in this <a href=\"http://urbangiraffe.com/articles/translating-wordpress-themes-and-plugins/\">guide to translating WordPress plugins</a>."
msgstr "Les détails complets de production d'une traduction peut être trouvée dans ce <a href=\"http://urbangiraffe.com/articles/translating-wordpress-themes-and-plugins/\">guide pour la traduction des plugins Wordpress</a> ."

#: ../view/admin/trail.php:16
msgid "Bulk Actions"
msgstr "Actions en vrac"

#: ../view/admin/trail.php:17
msgid "Delete"
msgstr "Supprimer"

#: ../view/admin/trail.php:20
msgid "Apply"
msgstr "Appliquer"

#: ../view/admin/trail.php:24
msgid "Filter"
msgstr "Filtre"

#: ../view/admin/trail.php:39
#: ../view/admin/trail.php:50
msgid "User"
msgstr "Utilisateur"

#: ../view/admin/trail.php:40
#: ../view/admin/trail.php:51
msgid "Action"
msgstr "Action"

#: ../view/admin/trail.php:41
#: ../view/admin/trail.php:52
msgid "Target"
msgstr "Target"

#: ../view/admin/trail.php:42
#: ../view/admin/trail.php:53
msgid "Date"
msgstr "Date"

#: ../view/admin/trail.php:43
#: ../view/admin/trail.php:54
msgid "IP"
msgstr "IP"

#: ../view/admin/trail.php:76
msgid "There is nothing to display!"
msgstr "Il n'ya rien à afficher!"

#: ../view/admin/version.php:2
msgid "Audit Trail News"
msgstr "Nouvelles Audit Trail"

# ?
#: ../view/admin/version.php:5
#, php-format
msgid "%s ago"
msgstr "Il ya %s"

#: ../view/admin/details/edit_category.php:1
#: ../view/admin/details/edit_link.php:1
msgid "Name"
msgstr "Nom"

#: ../view/admin/details/edit_category.php:5
#: ../view/admin/details/edit_link.php:5
#: ../view/admin/details/profile_update.php:9
#: ../view/admin/details/save_post.php:5
msgid "URL"
msgstr "URL"

#: ../view/admin/details/edit_category.php:9
#: ../view/admin/details/edit_link.php:9
msgid "Description"
msgstr "Description"

#: ../view/admin/details/edit_comment.php:1
msgid "Author"
msgstr "Auteur"

#: ../view/admin/details/edit_comment.php:5
#: ../view/admin/details/save_post.php:9
msgid "Content"
msgstr "Contenu"

# ?
#: ../view/admin/details/profile_update.php:1
msgid "Login"
msgstr "Connectez-vous"

#: ../view/admin/details/profile_update.php:5
msgid "Email"
msgstr "Email"

#: ../view/admin/details/save_post.php:1
msgid "Title"
msgstr "Title"

#: ../view/admin/details/save_post.php:15
msgid "Difference between this and current version"
msgstr "Différence entre ceci et la version actuelle"

