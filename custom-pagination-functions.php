<?php
/**
 * 自定义分页功能 - WordPress集成
 * 将此代码添加到主题的 functions.php 文件中
 */

// 加载自定义分页样式和脚本
add_action('wp_enqueue_scripts', 'enqueue_custom_pagination_assets');
function enqueue_custom_pagination_assets() {
    // 只在需要的页面加载
    if (is_page() || is_home() || is_category()) {
        wp_enqueue_style(
            'custom-pagination-style',
            get_template_directory_uri() . '/css/custom-pagination-styles.css',
            array(),
            '1.0.0'
        );
        
        wp_enqueue_script(
            'custom-pagination-script',
            get_template_directory_uri() . '/js/custom-pagination.js',
            array('jquery'),
            '1.0.0',
            true
        );
        
        // 传递分页数据到JavaScript
        wp_localize_script('custom-pagination-script', 'paginationData', array(
            'totalPosts' => get_pagination_total_posts(),
            'postsPerPage' => get_posts_per_page(),
            'ajaxUrl' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('pagination_nonce')
        ));
    }
}

// 获取总文章数
function get_pagination_total_posts() {
    global $wp_query;
    
    if (is_category()) {
        $category = get_queried_object();
        return $category->count;
    } elseif (is_tag()) {
        $tag = get_queried_object();
        return $tag->count;
    } else {
        // 对于特定分类（如news）
        $args = array(
            'post_type' => 'post',
            'post_status' => 'publish',
            'category_name' => 'news', // 根据你的分类调整
            'posts_per_page' => -1
        );
        $query = new WP_Query($args);
        return $query->found_posts;
    }
}

// 获取每页文章数
function get_posts_per_page() {
    return get_option('posts_per_page', 6);
}

// 自定义分页短代码
add_shortcode('custom_news_pagination', 'custom_news_pagination_shortcode');
function custom_news_pagination_shortcode($atts) {
    $atts = shortcode_atts(array(
        'posts_per_page' => 6,
        'category' => 'news',
        'columns' => 3,
        'show_excerpt' => 'yes',
        'show_meta' => 'yes'
    ), $atts);
    
    $paged = (get_query_var('paged')) ? get_query_var('paged') : 1;
    
    $args = array(
        'post_type' => 'post',
        'post_status' => 'publish',
        'posts_per_page' => intval($atts['posts_per_page']),
        'paged' => $paged,
        'category_name' => $atts['category']
    );
    
    $query = new WP_Query($args);
    
    ob_start();
    
    if ($query->have_posts()) {
        echo '<div class="custom-news-grid aobailei-news-blog" data-columns="' . esc_attr($atts['columns']) . '">';
        echo '<div class="news-grid-container">';
        
        while ($query->have_posts()) {
            $query->the_post();
            ?>
            <article class="news-item">
                <?php if (has_post_thumbnail()): ?>
                    <div class="news-thumbnail">
                        <a href="<?php the_permalink(); ?>">
                            <?php the_post_thumbnail('medium'); ?>
                        </a>
                    </div>
                <?php endif; ?>
                
                <div class="news-content">
                    <h3 class="news-title">
                        <a href="<?php the_permalink(); ?>"><?php the_title(); ?></a>
                    </h3>
                    
                    <?php if ($atts['show_meta'] === 'yes'): ?>
                        <div class="news-meta">
                            <span class="news-date"><?php echo get_the_date(); ?></span>
                        </div>
                    <?php endif; ?>
                    
                    <?php if ($atts['show_excerpt'] === 'yes'): ?>
                        <div class="news-excerpt">
                            <?php the_excerpt(); ?>
                        </div>
                    <?php endif; ?>
                    
                    <div class="news-read-more">
                        <a href="<?php the_permalink(); ?>" class="read-more-btn">阅读更多</a>
                    </div>
                </div>
            </article>
            <?php
        }
        
        echo '</div>'; // .news-grid-container
        echo '</div>'; // .custom-news-grid
        
        // 添加分页数据
        ?>
        <script>
        window.paginationData = {
            totalPosts: <?php echo $query->found_posts; ?>,
            postsPerPage: <?php echo intval($atts['posts_per_page']); ?>,
            currentPage: <?php echo $paged; ?>,
            totalPages: <?php echo $query->max_num_pages; ?>
        };
        </script>
        <?php
    }
    
    wp_reset_postdata();
    
    return ob_get_clean();
}

// AJAX处理分页请求
add_action('wp_ajax_load_pagination_content', 'handle_pagination_ajax');
add_action('wp_ajax_nopriv_load_pagination_content', 'handle_pagination_ajax');
function handle_pagination_ajax() {
    check_ajax_referer('pagination_nonce', 'nonce');
    
    $page = intval($_POST['page']);
    $posts_per_page = intval($_POST['posts_per_page']);
    $category = sanitize_text_field($_POST['category']);
    
    $args = array(
        'post_type' => 'post',
        'post_status' => 'publish',
        'posts_per_page' => $posts_per_page,
        'paged' => $page,
        'category_name' => $category
    );
    
    $query = new WP_Query($args);
    
    ob_start();
    
    if ($query->have_posts()) {
        while ($query->have_posts()) {
            $query->the_post();
            // 输出文章HTML结构
            // 这里可以复用上面的文章模板
        }
    }
    
    wp_reset_postdata();
    
    wp_send_json_success(array(
        'content' => ob_get_clean(),
        'total_pages' => $query->max_num_pages,
        'current_page' => $page
    ));
}

// 添加自定义CSS到头部
add_action('wp_head', 'add_custom_pagination_inline_styles');
function add_custom_pagination_inline_styles() {
    ?>
    <style>
    /* 新闻网格布局 */
    .custom-news-grid .news-grid-container {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 35px;
        margin-bottom: 40px;
    }
    
    .custom-news-grid[data-columns="3"] .news-grid-container {
        grid-template-columns: repeat(3, 1fr);
    }
    
    .custom-news-grid[data-columns="2"] .news-grid-container {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .news-item {
        background: white;
        border-radius: 8px;
        overflow: hidden;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        transition: transform 0.3s ease, box-shadow 0.3s ease;
    }
    
    .news-item:hover {
        transform: translateY(-5px);
        box-shadow: 0 5px 20px rgba(0,0,0,0.15);
    }
    
    .news-thumbnail img {
        width: 100%;
        height: 200px;
        object-fit: cover;
    }
    
    .news-content {
        padding: 20px;
    }
    
    .news-title {
        margin: 0 0 10px 0;
        font-size: 18px;
        line-height: 1.4;
    }
    
    .news-title a {
        color: #333;
        text-decoration: none;
    }
    
    .news-title a:hover {
        color: #007cba;
    }
    
    .news-meta {
        color: #666;
        font-size: 14px;
        margin-bottom: 15px;
    }
    
    .news-excerpt {
        color: #555;
        line-height: 1.6;
        margin-bottom: 15px;
    }
    
    .read-more-btn {
        display: inline-block;
        padding: 8px 16px;
        background: #007cba;
        color: white;
        text-decoration: none;
        border-radius: 4px;
        font-size: 14px;
        transition: background 0.3s ease;
    }
    
    .read-more-btn:hover {
        background: #005a87;
        text-decoration: none;
    }
    
    /* 响应式设计 */
    @media (max-width: 768px) {
        .custom-news-grid .news-grid-container {
            grid-template-columns: 1fr;
            gap: 20px;
        }
        
        .news-content {
            padding: 15px;
        }
    }
    </style>
    <?php
}

// 修改Fusion Builder的分页样式
add_action('wp_footer', 'modify_fusion_pagination');
function modify_fusion_pagination() {
    ?>
    <script>
    jQuery(document).ready(function($) {
        // 隐藏原有的Fusion分页
        $('.aobailei-news-blog .pagination').hide();
        
        // 如果存在自定义分页数据，初始化自定义分页
        if (typeof window.paginationData !== 'undefined') {
            new CustomPagination({
                container: '.aobailei-news-blog',
                ...window.paginationData
            });
        }
    });
    </script>
    <?php
}
?>
