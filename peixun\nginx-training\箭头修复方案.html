<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>箭头修复方案</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            font-family: 'Microsoft YaHei', sans-serif;
            min-height: 100vh;
        }

        .demo-container {
            max-width: 800px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 20px;
            padding: 30px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .title {
            color: white;
            font-size: 28px;
            font-weight: bold;
            text-align: center;
            margin-bottom: 30px;
        }

        /* 方案1：使用SVG箭头 */
        .svg-demo {
            position: relative;
            width: 600px;
            height: 400px;
            margin: 20px auto;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 15px;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .element {
            position: absolute;
            width: 100px;
            height: 60px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
            font-size: 14px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
        }

        .center {
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 120px;
            height: 80px;
            background: linear-gradient(135deg, #6366f1, #8b5cf6);
            font-size: 16px;
        }

        .top-left {
            top: 30px;
            left: 30px;
            background: linear-gradient(135deg, #06d6a0, #00c9ff);
        }

        .top-right {
            top: 30px;
            right: 30px;
            background: linear-gradient(135deg, #f093fb, #f5576c);
        }

        .bottom-left {
            bottom: 30px;
            left: 30px;
            background: linear-gradient(135deg, #4facfe, #00f2fe);
        }

        .bottom-right {
            bottom: 30px;
            right: 30px;
            background: linear-gradient(135deg, #43e97b, #38f9d7);
        }

        /* SVG箭头样式 */
        .arrow-svg {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
        }

        .arrow-line {
            stroke: #4facfe;
            stroke-width: 4;
            fill: none;
            stroke-linecap: round;
            animation: arrowFlow 2s ease-in-out infinite;
        }

        .arrow-head {
            fill: #4facfe;
            animation: arrowFlow 2s ease-in-out infinite;
        }

        @keyframes arrowFlow {
            0%, 100% { opacity: 0.7; }
            50% { opacity: 1; }
        }

        .demo-title {
            color: white;
            font-size: 18px;
            font-weight: bold;
            text-align: center;
            margin: 20px 0 10px 0;
        }

        .demo-desc {
            color: rgba(255, 255, 255, 0.8);
            font-size: 14px;
            text-align: center;
            margin-bottom: 20px;
        }

        .code-block {
            background: rgba(0, 0, 0, 0.3);
            border-radius: 8px;
            padding: 15px;
            margin: 20px 0;
            color: #00ff88;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            overflow-x: auto;
        }
    </style>
</head>
<body>
    <div class="demo-container">
        <div class="title">🎯 箭头修复方案演示</div>
        
        <div class="demo-title">方案：使用SVG绘制精确箭头</div>
        <div class="demo-desc">所有箭头都精确指向中心的Nginx</div>
        
        <div class="svg-demo">
            <!-- 四个角的元素 -->
            <div class="element top-left">互联网</div>
            <div class="element top-right">用户</div>
            <div class="element bottom-left">网站</div>
            <div class="element bottom-right">应用</div>
            
            <!-- 中心元素 -->
            <div class="element center">Nginx<br>超级服务器</div>
            
            <!-- SVG箭头 -->
            <svg class="arrow-svg">
                <!-- 左上到中心 -->
                <line class="arrow-line" x1="130" y1="90" x2="250" y2="170"></line>
                <polygon class="arrow-head" points="250,170 240,165 240,175"></polygon>
                
                <!-- 右上到中心 -->
                <line class="arrow-line" x1="470" y1="90" x2="350" y2="170"></line>
                <polygon class="arrow-head" points="350,170 360,165 360,175"></polygon>
                
                <!-- 左下到中心 -->
                <line class="arrow-line" x1="130" y1="310" x2="250" y2="230"></line>
                <polygon class="arrow-head" points="250,230 240,225 240,235"></polygon>
                
                <!-- 右下到中心 -->
                <line class="arrow-line" x1="470" y1="310" x2="350" y2="230"></line>
                <polygon class="arrow-head" points="350,230 360,225 360,235"></polygon>
            </svg>
        </div>
        
        <div class="code-block">
&lt;!-- SVG箭头实现代码 --&gt;
&lt;svg class="arrow-svg"&gt;
    &lt;!-- 左上到中心 --&gt;
    &lt;line class="arrow-line" x1="130" y1="90" x2="250" y2="170"&gt;&lt;/line&gt;
    &lt;polygon class="arrow-head" points="250,170 240,165 240,175"&gt;&lt;/polygon&gt;
    
    &lt;!-- 右上到中心 --&gt;
    &lt;line class="arrow-line" x1="470" y1="90" x2="350" y2="170"&gt;&lt;/line&gt;
    &lt;polygon class="arrow-head" points="350,170 360,165 360,175"&gt;&lt;/polygon&gt;
    
    &lt;!-- 左下到中心 --&gt;
    &lt;line class="arrow-line" x1="130" y1="310" x2="250" y2="230"&gt;&lt;/line&gt;
    &lt;polygon class="arrow-head" points="250,230 240,225 240,235"&gt;&lt;/polygon&gt;
    
    &lt;!-- 右下到中心 --&gt;
    &lt;line class="arrow-line" x1="470" y1="310" x2="350" y2="230"&gt;&lt;/line&gt;
    &lt;polygon class="arrow-head" points="350,230 360,225 360,235"&gt;&lt;/polygon&gt;
&lt;/svg&gt;
        </div>
        
        <div style="color: white; text-align: center; margin-top: 30px;">
            <h3>✅ 优势</h3>
            <p>• 精确控制箭头起点和终点<br>
            • 完美指向中心<br>
            • 流畅的动画效果<br>
            • 响应式设计</p>
        </div>
    </div>
</body>
</html>
