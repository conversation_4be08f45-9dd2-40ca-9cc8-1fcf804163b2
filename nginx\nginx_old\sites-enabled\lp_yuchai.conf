server {
    listen 80;
    server_name lp.yuchai.com;  # 替换成您的域名
    root /var/www/lp_yuchai;      # WordPress安装目录
    index index.php index.html;

    # 日志配置
    access_log /var/log/nginx/lp_yuchai.access.log;
    error_log /var/log/nginx/lp_yuchai.error.log;
    
    # 引入通用规则
    include /etc/nginx/rules/wordpress_common.conf;
    include /etc/nginx/rules/php_security.conf;
    include /etc/nginx/rules/wordpress_security.conf;
    include /etc/nginx/rules/static_files.conf;
    include /etc/nginx/rules/file_access.conf;
    include /etc/nginx/rules/wp-admin.conf;
    include /etc/nginx/rules/path_traversal.conf;
    
    # 为该站点增加的特定配置
    
    # WordPress多站点支持（如果需要）
    if (!-e $request_filename) {
        rewrite /wp-admin$ $scheme://$host$uri/ permanent;
        rewrite ^(/[^/]+)?(/wp-.*) $2 last;
        rewrite ^(/[^/]+)?(/.*\.php) $2 last;
    }
}
