<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Nginx技术培训 - PPT演示</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');
        
        :root {
            --primary-blue: #1E3A8A;
            --secondary-blue: #3B82F6;
            --accent-orange: #F59E0B;
            --success-green: #10B981;
            --warning-red: #EF4444;
            --text-dark: #1F2937;
            --text-light: #6B7280;
            --bg-light: #F8FAFC;
            --white: #FFFFFF;
            --shadow: 0 10px 25px rgba(0,0,0,0.1);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            overflow: hidden;
        }

        .presentation-container {
            width: 100vw;
            height: 100vh;
            position: relative;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .slide {
            width: 90vw;
            max-width: 1200px;
            height: 85vh;
            background: var(--white);
            border-radius: 20px;
            box-shadow: var(--shadow);
            padding: 60px;
            display: none;
            flex-direction: column;
            position: relative;
            overflow: hidden;
        }

        .slide.active {
            display: flex;
            animation: slideIn 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94);
        }

        @keyframes slideIn {
            from {
                opacity: 0;
                transform: translateX(50px);
            }
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }

        .slide-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 40px;
            padding-bottom: 20px;
            border-bottom: 3px solid var(--primary-blue);
        }

        .slide-number {
            background: var(--primary-blue);
            color: var(--white);
            padding: 8px 16px;
            border-radius: 20px;
            font-weight: 600;
            font-size: 14px;
        }

        .company-logo {
            background: var(--accent-orange);
            color: var(--white);
            padding: 12px 20px;
            border-radius: 10px;
            font-weight: 700;
            font-size: 18px;
        }

        .slide-title {
            font-size: 3.5em;
            font-weight: 700;
            color: var(--primary-blue);
            margin-bottom: 20px;
            line-height: 1.2;
        }

        .slide-subtitle {
            font-size: 1.5em;
            color: var(--text-light);
            margin-bottom: 40px;
            font-weight: 400;
        }

        .slide-content {
            flex: 1;
            display: flex;
            flex-direction: column;
            justify-content: center;
        }

        .bullet-points {
            list-style: none;
            padding: 0;
        }

        .bullet-points li {
            font-size: 1.4em;
            margin: 20px 0;
            padding: 20px;
            background: var(--bg-light);
            border-radius: 12px;
            border-left: 5px solid var(--secondary-blue);
            display: flex;
            align-items: center;
            transition: all 0.3s ease;
        }

        .bullet-points li:hover {
            transform: translateX(10px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }

        .bullet-icon {
            font-size: 1.5em;
            margin-right: 20px;
            color: var(--secondary-blue);
        }

        .two-column {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 40px;
            height: 100%;
        }

        .column {
            display: flex;
            flex-direction: column;
            justify-content: center;
        }

        .diagram-box {
            background: var(--bg-light);
            border: 2px solid var(--secondary-blue);
            border-radius: 15px;
            padding: 30px;
            text-align: center;
            margin: 20px 0;
            transition: all 0.3s ease;
        }

        .diagram-box:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(59, 130, 246, 0.15);
        }

        .diagram-icon {
            font-size: 4em;
            margin-bottom: 20px;
            color: var(--secondary-blue);
        }

        .comparison-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
            background: var(--white);
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }

        .comparison-table th {
            background: var(--primary-blue);
            color: var(--white);
            padding: 20px;
            font-weight: 600;
            font-size: 1.2em;
        }

        .comparison-table td {
            padding: 20px;
            border-bottom: 1px solid #E5E7EB;
            font-size: 1.1em;
        }

        .comparison-table tr:nth-child(even) {
            background: var(--bg-light);
        }

        .highlight-box {
            background: linear-gradient(135deg, var(--success-green), #059669);
            color: var(--white);
            padding: 30px;
            border-radius: 15px;
            margin: 20px 0;
            text-align: center;
        }

        .warning-box {
            background: linear-gradient(135deg, var(--warning-red), #DC2626);
            color: var(--white);
            padding: 30px;
            border-radius: 15px;
            margin: 20px 0;
            text-align: center;
        }

        .flow-diagram {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin: 30px 0;
            flex-wrap: wrap;
            gap: 20px;
        }

        .flow-step {
            background: var(--secondary-blue);
            color: var(--white);
            padding: 20px;
            border-radius: 12px;
            flex: 1;
            min-width: 150px;
            text-align: center;
            font-weight: 600;
            position: relative;
            transition: all 0.3s ease;
        }

        .flow-step:hover {
            transform: scale(1.05);
            background: var(--primary-blue);
        }

        .flow-step::after {
            content: "→";
            position: absolute;
            right: -25px;
            top: 50%;
            transform: translateY(-50%);
            font-size: 2em;
            color: var(--primary-blue);
            font-weight: bold;
        }

        .flow-step:last-child::after {
            display: none;
        }

        .navigation {
            position: fixed;
            bottom: 30px;
            left: 50%;
            transform: translateX(-50%);
            display: flex;
            gap: 15px;
            z-index: 1000;
        }

        .nav-btn {
            background: var(--white);
            border: 2px solid var(--primary-blue);
            color: var(--primary-blue);
            padding: 12px 24px;
            border-radius: 25px;
            cursor: pointer;
            font-weight: 600;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }

        .nav-btn:hover {
            background: var(--primary-blue);
            color: var(--white);
            transform: translateY(-2px);
        }

        .nav-btn.active {
            background: var(--primary-blue);
            color: var(--white);
        }

        .slide-counter {
            position: fixed;
            top: 30px;
            right: 30px;
            background: rgba(255,255,255,0.9);
            padding: 10px 20px;
            border-radius: 20px;
            font-weight: 600;
            color: var(--primary-blue);
            z-index: 1000;
        }

        .progress-bar {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 4px;
            background: rgba(255,255,255,0.3);
            z-index: 1000;
        }

        .progress-fill {
            height: 100%;
            background: var(--accent-orange);
            transition: width 0.3s ease;
        }

        .chart-container {
            background: var(--white);
            border-radius: 15px;
            padding: 30px;
            margin: 20px 0;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }

        .bar-chart {
            display: flex;
            align-items: end;
            justify-content: space-around;
            height: 200px;
            margin: 20px 0;
        }

        .bar {
            background: var(--secondary-blue);
            border-radius: 8px 8px 0 0;
            min-width: 60px;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: end;
            color: var(--white);
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .bar:hover {
            background: var(--primary-blue);
            transform: scale(1.05);
        }

        .bar-label {
            color: var(--text-dark);
            margin-top: 10px;
            font-size: 0.9em;
            text-align: center;
        }

        .interactive-demo {
            background: var(--bg-light);
            border: 2px dashed var(--secondary-blue);
            border-radius: 15px;
            padding: 30px;
            text-align: center;
            margin: 20px 0;
        }

        .demo-btn {
            background: var(--accent-orange);
            color: var(--white);
            border: none;
            padding: 15px 30px;
            border-radius: 25px;
            font-weight: 600;
            cursor: pointer;
            margin: 10px;
            transition: all 0.3s ease;
        }

        .demo-btn:hover {
            background: #D97706;
            transform: translateY(-2px);
        }

        @media (max-width: 768px) {
            .slide {
                width: 95vw;
                height: 90vh;
                padding: 30px;
            }
            
            .slide-title {
                font-size: 2.5em;
            }
            
            .two-column {
                grid-template-columns: 1fr;
                gap: 20px;
            }
            
            .flow-diagram {
                flex-direction: column;
            }
            
            .flow-step::after {
                content: "↓";
                right: 50%;
                top: 100%;
                transform: translateX(50%);
            }
        }
    </style>
</head>
<body>
    <!-- 进度条 -->
    <div class="progress-bar">
        <div class="progress-fill" id="progress-fill"></div>
    </div>

    <!-- 幻灯片计数器 -->
    <div class="slide-counter">
        <span id="current-slide">1</span> / <span id="total-slides">12</span>
    </div>

    <div class="presentation-container">

        <!-- 幻灯片 1: 封面 -->
        <div class="slide active" id="slide-1">
            <div class="slide-header">
                <div class="slide-number">01</div>
                <div class="company-logo" id="company-logo">NGINX</div>
            </div>
            <div class="slide-content">
                <h1 class="slide-title">Nginx技术培训</h1>
                <p class="slide-subtitle">高性能Web服务器与反向代理技术详解</p>
                <div style="text-align: center; margin-top: 60px;">
                    <div style="font-size: 8em; color: var(--secondary-blue); margin-bottom: 30px;">⚡</div>
                    <p style="font-size: 1.3em; color: var(--text-light);">让我们一起探索Nginx的强大功能</p>
                </div>
            </div>
        </div>

        <!-- 幻灯片 2: 课程大纲 -->
        <div class="slide" id="slide-2">
            <div class="slide-header">
                <div class="slide-number">02</div>
                <div class="company-logo" id="company-logo-2">NGINX</div>
            </div>
            <div class="slide-content">
                <h1 class="slide-title">课程大纲</h1>
                <ul class="bullet-points">
                    <li><span class="bullet-icon">🎯</span>Nginx基础概念与特性</li>
                    <li><span class="bullet-icon">🏛️</span>Web架构与前后端关系</li>
                    <li><span class="bullet-icon">🌐</span>HTTP协议与服务器原理</li>
                    <li><span class="bullet-icon">🔀</span>反向代理与负载均衡</li>
                    <li><span class="bullet-icon">🔒</span>安全防护与访问控制</li>
                    <li><span class="bullet-icon">📈</span>性能优化与监控</li>
                </ul>
            </div>
        </div>

        <!-- 幻灯片 3: 什么是Nginx -->
        <div class="slide" id="slide-3">
            <div class="slide-header">
                <div class="slide-number">03</div>
                <div class="company-logo" id="company-logo-3">NGINX</div>
            </div>
            <div class="slide-content">
                <h1 class="slide-title">什么是Nginx？</h1>
                <div class="two-column">
                    <div class="column">
                        <div class="diagram-box">
                            <div class="diagram-icon">🏢</div>
                            <h3>24小时便利店</h3>
                            <p>就像一个永不打烊的便利店，随时为顾客提供服务</p>
                        </div>
                        <div class="diagram-box">
                            <div class="diagram-icon">🤖</div>
                            <h3>超级服务员</h3>
                            <p>一个服务员可以同时服务成千上万的顾客</p>
                        </div>
                    </div>
                    <div class="column">
                        <div class="highlight-box">
                            <h3>Nginx核心特性</h3>
                            <ul style="text-align: left; margin-top: 20px;">
                                <li>✅ 高并发处理能力</li>
                                <li>✅ 低内存消耗</li>
                                <li>✅ 高稳定性</li>
                                <li>✅ 模块化设计</li>
                                <li>✅ 热部署支持</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 幻灯片 4: Nginx vs 传统服务器 -->
        <div class="slide" id="slide-4">
            <div class="slide-header">
                <div class="slide-number">04</div>
                <div class="company-logo" id="company-logo-4">NGINX</div>
            </div>
            <div class="slide-content">
                <h1 class="slide-title">性能对比</h1>
                <div class="chart-container">
                    <h3 style="text-align: center; margin-bottom: 30px;">并发处理能力对比</h3>
                    <div class="bar-chart">
                        <div class="bar" style="height: 30%;">
                            <span>1K</span>
                            <div class="bar-label">Apache</div>
                        </div>
                        <div class="bar" style="height: 100%; background: var(--success-green);">
                            <span>10K+</span>
                            <div class="bar-label">Nginx</div>
                        </div>
                        <div class="bar" style="height: 25%;">
                            <span>800</span>
                            <div class="bar-label">IIS</div>
                        </div>
                    </div>
                </div>
                <table class="comparison-table">
                    <tr>
                        <th>特性</th>
                        <th>传统服务器</th>
                        <th>Nginx</th>
                    </tr>
                    <tr>
                        <td>并发连接</td>
                        <td>1,000</td>
                        <td style="color: var(--success-green); font-weight: bold;">10,000+</td>
                    </tr>
                    <tr>
                        <td>内存使用</td>
                        <td>高</td>
                        <td style="color: var(--success-green); font-weight: bold;">低</td>
                    </tr>
                    <tr>
                        <td>CPU使用</td>
                        <td>高</td>
                        <td style="color: var(--success-green); font-weight: bold;">低</td>
                    </tr>
                </table>
            </div>
        </div>

        <!-- 幻灯片 5: Web架构概述 -->
        <div class="slide" id="slide-5">
            <div class="slide-header">
                <div class="slide-number">05</div>
                <div class="company-logo" id="company-logo-5">NGINX</div>
            </div>
            <div class="slide-content">
                <h1 class="slide-title">Web架构概述</h1>
                <div style="text-align: center; margin: 40px 0;">
                    <div style="display: grid; grid-template-columns: repeat(3, 1fr); gap: 30px; margin: 40px 0;">
                        <div class="diagram-box">
                            <div class="diagram-icon">💻</div>
                            <h3>前端 Frontend</h3>
                            <p>用户界面<br>用户体验<br>页面展示</p>
                        </div>
                        <div class="diagram-box">
                            <div class="diagram-icon">🔧</div>
                            <h3>后端 Backend</h3>
                            <p>业务逻辑<br>数据处理<br>API服务</p>
                        </div>
                        <div class="diagram-box">
                            <div class="diagram-icon">💾</div>
                            <h3>数据库 Database</h3>
                            <p>数据存储<br>数据管理<br>数据查询</p>
                        </div>
                    </div>
                </div>
                <div class="flow-diagram">
                    <div class="flow-step">用户请求</div>
                    <div class="flow-step">前端处理</div>
                    <div class="flow-step">后端逻辑</div>
                    <div class="flow-step">数据库查询</div>
                    <div class="flow-step">返回结果</div>
                </div>
            </div>
        </div>

        <!-- 幻灯片 6: HTTP协议基础 -->
        <div class="slide" id="slide-6">
            <div class="slide-header">
                <div class="slide-number">06</div>
                <div class="company-logo" id="company-logo-6">NGINX</div>
            </div>
            <div class="slide-content">
                <h1 class="slide-title">HTTP协议基础</h1>
                <div class="two-column">
                    <div class="column">
                        <h3 style="margin-bottom: 30px;">HTTP请求方法</h3>
                        <div class="diagram-box">
                            <div class="diagram-icon">📥</div>
                            <h4>GET</h4>
                            <p>获取资源<br>如：浏览网页</p>
                        </div>
                        <div class="diagram-box">
                            <div class="diagram-icon">📤</div>
                            <h4>POST</h4>
                            <p>提交数据<br>如：表单提交</p>
                        </div>
                        <div class="diagram-box">
                            <div class="diagram-icon">🔁</div>
                            <h4>PUT/DELETE</h4>
                            <p>更新/删除资源<br>如：API操作</p>
                        </div>
                    </div>
                    <div class="column">
                        <h3 style="margin-bottom: 30px;">HTTP状态码</h3>
                        <table class="comparison-table">
                            <tr>
                                <th>状态码</th>
                                <th>含义</th>
                            </tr>
                            <tr>
                                <td style="color: var(--success-green);">200</td>
                                <td>请求成功</td>
                            </tr>
                            <tr>
                                <td style="color: var(--accent-orange);">301/302</td>
                                <td>重定向</td>
                            </tr>
                            <tr>
                                <td style="color: var(--warning-red);">404</td>
                                <td>资源未找到</td>
                            </tr>
                            <tr>
                                <td style="color: var(--warning-red);">500</td>
                                <td>服务器错误</td>
                            </tr>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- 幻灯片 7: 反向代理概念 -->
        <div class="slide" id="slide-7">
            <div class="slide-header">
                <div class="slide-number">07</div>
                <div class="company-logo" id="company-logo-7">NGINX</div>
            </div>
            <div class="slide-content">
                <h1 class="slide-title">反向代理</h1>
                <div style="text-align: center; margin: 40px 0;">
                    <div class="diagram-box" style="background: linear-gradient(135deg, #E8F4FD 0%, #B3D9FF 100%);">
                        <div class="diagram-icon">🎛️</div>
                        <h3>智能交通指挥员</h3>
                        <p>Nginx就像一个智能交通指挥员，合理分配用户请求到不同的服务器</p>
                    </div>
                </div>
                <div class="flow-diagram">
                    <div class="flow-step">用户请求</div>
                    <div class="flow-step" style="background: var(--accent-orange);">Nginx代理</div>
                    <div class="flow-step">后端服务器1</div>
                </div>
                <div class="flow-diagram" style="margin-top: 10px;">
                    <div class="flow-step" style="opacity: 0;"></div>
                    <div class="flow-step" style="opacity: 0;"></div>
                    <div class="flow-step">后端服务器2</div>
                </div>
                <div class="flow-diagram" style="margin-top: 10px;">
                    <div class="flow-step" style="opacity: 0;"></div>
                    <div class="flow-step" style="opacity: 0;"></div>
                    <div class="flow-step">后端服务器3</div>
                </div>
            </div>
        </div>

        <!-- 幻灯片 8: 负载均衡策略 -->
        <div class="slide" id="slide-8">
            <div class="slide-header">
                <div class="slide-number">08</div>
                <div class="company-logo" id="company-logo-8">NGINX</div>
            </div>
            <div class="slide-content">
                <h1 class="slide-title">负载均衡策略</h1>
                <div style="display: grid; grid-template-columns: repeat(2, 1fr); gap: 30px;">
                    <div class="diagram-box">
                        <div class="diagram-icon">🔃</div>
                        <h3>轮询 (Round Robin)</h3>
                        <p>依次分配请求到每个服务器</p>
                    </div>
                    <div class="diagram-box">
                        <div class="diagram-icon">⚖️</div>
                        <h3>加权轮询</h3>
                        <p>根据服务器性能分配不同权重</p>
                    </div>
                    <div class="diagram-box">
                        <div class="diagram-icon">📉</div>
                        <h3>最少连接</h3>
                        <p>优先分配给连接数最少的服务器</p>
                    </div>
                    <div class="diagram-box">
                        <div class="diagram-icon">🔗</div>
                        <h3>IP哈希</h3>
                        <p>根据客户端IP分配到固定服务器</p>
                    </div>
                </div>
                <div class="interactive-demo">
                    <h3>负载均衡演示</h3>
                    <button class="demo-btn" onclick="showLoadBalanceDemo()">查看负载分配效果</button>
                    <div id="load-balance-result" style="margin-top: 20px;"></div>
                </div>
            </div>
        </div>

        <!-- 幻灯片 9: 安全防护功能 -->
        <div class="slide" id="slide-9">
            <div class="slide-header">
                <div class="slide-number">09</div>
                <div class="company-logo" id="company-logo-9">NGINX</div>
            </div>
            <div class="slide-content">
                <h1 class="slide-title">安全防护功能</h1>
                <div class="two-column">
                    <div class="column">
                        <h3 style="margin-bottom: 30px;">访问控制</h3>
                        <ul class="bullet-points">
                            <li><span class="bullet-icon">🚫</span>IP地址白名单/黑名单</li>
                            <li><span class="bullet-icon">🗺️</span>地理位置限制</li>
                            <li><span class="bullet-icon">🔑</span>HTTP基础认证</li>
                            <li><span class="bullet-icon">🔐</span>SSL/TLS加密</li>
                        </ul>
                    </div>
                    <div class="column">
                        <h3 style="margin-bottom: 30px;">速率限制</h3>
                        <div class="warning-box">
                            <h4>DDoS攻击防护</h4>
                            <p>限制每个IP的请求频率，防止恶意攻击</p>
                        </div>
                        <div class="highlight-box">
                            <h4>带宽控制</h4>
                            <p>控制下载速度，合理分配带宽资源</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 幻灯片 10: 性能优化 -->
        <div class="slide" id="slide-10">
            <div class="slide-header">
                <div class="slide-number">10</div>
                <div class="company-logo" id="company-logo-10">NGINX</div>
            </div>
            <div class="slide-content">
                <h1 class="slide-title">性能优化技术</h1>
                <div style="display: grid; grid-template-columns: repeat(3, 1fr); gap: 30px; margin: 40px 0;">
                    <div class="diagram-box">
                        <div class="diagram-icon">💨</div>
                        <h3>静态文件缓存</h3>
                        <p>缓存图片、CSS、JS等静态资源</p>
                    </div>
                    <div class="diagram-box">
                        <div class="diagram-icon">📦</div>
                        <h3>Gzip压缩</h3>
                        <p>压缩传输内容，减少带宽使用</p>
                    </div>
                    <div class="diagram-box">
                        <div class="diagram-icon">🔄</div>
                        <h3>Keep-Alive</h3>
                        <p>复用TCP连接，减少握手开销</p>
                    </div>
                </div>
                <div class="chart-container">
                    <h3 style="text-align: center; margin-bottom: 30px;">性能提升效果</h3>
                    <div class="bar-chart">
                        <div class="bar" style="height: 40%;">
                            <span>40%</span>
                            <div class="bar-label">响应时间减少</div>
                        </div>
                        <div class="bar" style="height: 60%;">
                            <span>60%</span>
                            <div class="bar-label">带宽节省</div>
                        </div>
                        <div class="bar" style="height: 80%;">
                            <span>80%</span>
                            <div class="bar-label">并发能力提升</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 幻灯片 11: 实际应用场景 -->
        <div class="slide" id="slide-11">
            <div class="slide-header">
                <div class="slide-number">11</div>
                <div class="company-logo" id="company-logo-11">NGINX</div>
            </div>
            <div class="slide-content">
                <h1 class="slide-title">实际应用场景</h1>
                <div style="display: grid; grid-template-columns: repeat(2, 1fr); gap: 30px;">
                    <div class="diagram-box" style="background: linear-gradient(135deg, #FFE5E5 0%, #FFB3B3 100%);">
                        <div class="diagram-icon">🛍️</div>
                        <h3>电商网站</h3>
                        <p>处理双11等高并发场景<br>负载均衡多个服务器<br>静态资源CDN加速</p>
                    </div>
                    <div class="diagram-box" style="background: linear-gradient(135deg, #E5F5E5 0%, #B3FFB3 100%);">
                        <div class="diagram-icon">🎥</div>
                        <h3>视频网站</h3>
                        <p>大文件传输优化<br>带宽控制管理<br>地理位置分发</p>
                    </div>
                    <div class="diagram-box" style="background: linear-gradient(135deg, #E5E5FF 0%, #B3B3FF 100%);">
                        <div class="diagram-icon">🏛️</div>
                        <h3>企业应用</h3>
                        <p>内部系统代理<br>API网关功能<br>安全访问控制</p>
                    </div>
                    <div class="diagram-box" style="background: linear-gradient(135deg, #FFF5E5 0%, #FFE5B3 100%);">
                        <div class="diagram-icon">📲</div>
                        <h3>移动应用</h3>
                        <p>API接口代理<br>请求频率限制<br>数据压缩传输</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- 幻灯片 12: 总结 -->
        <div class="slide" id="slide-12">
            <div class="slide-header">
                <div class="slide-number">12</div>
                <div class="company-logo" id="company-logo-12">NGINX</div>
            </div>
            <div class="slide-content">
                <h1 class="slide-title">课程总结</h1>
                <div style="text-align: center; margin: 40px 0;">
                    <div class="highlight-box">
                        <h2>Nginx核心价值</h2>
                        <div style="display: grid; grid-template-columns: repeat(3, 1fr); gap: 30px; margin-top: 30px;">
                            <div>
                                <div style="font-size: 3em;">⚡</div>
                                <h3>高性能</h3>
                                <p>处理万级并发</p>
                            </div>
                            <div>
                                <div style="font-size: 3em;">🔒</div>
                                <h3>高安全</h3>
                                <p>多层防护机制</p>
                            </div>
                            <div>
                                <div style="font-size: 3em;">🎯</div>
                                <h3>高可用</h3>
                                <p>稳定可靠运行</p>
                            </div>
                        </div>
                    </div>
                </div>
                <div style="text-align: center; margin-top: 60px;">
                    <h2 style="color: var(--primary-blue); margin-bottom: 20px;">感谢聆听！</h2>
                    <p style="font-size: 1.3em; color: var(--text-light);">有任何问题欢迎交流讨论</p>
                </div>
            </div>
        </div>
    </div>

    <!-- 导航控制 -->
    <div class="navigation">
        <button class="nav-btn" onclick="previousSlide()">◀ 上一页</button>
        <button class="nav-btn" onclick="nextSlide()">下一页 ▶</button>
        <button class="nav-btn" onclick="toggleFullscreen()">全屏</button>
    </div>

    <script>
        let currentSlide = 1;
        const totalSlides = 12;

        // 更新幻灯片显示
        function showSlide(slideNumber) {
            // 隐藏所有幻灯片
            document.querySelectorAll('.slide').forEach(slide => {
                slide.classList.remove('active');
            });

            // 显示指定幻灯片
            document.getElementById(`slide-${slideNumber}`).classList.add('active');

            // 更新计数器
            document.getElementById('current-slide').textContent = slideNumber;

            // 更新进度条
            const progress = (slideNumber / totalSlides) * 100;
            document.getElementById('progress-fill').style.width = progress + '%';

            currentSlide = slideNumber;
        }

        // 下一张幻灯片
        function nextSlide() {
            if (currentSlide < totalSlides) {
                showSlide(currentSlide + 1);
            }
        }

        // 上一张幻灯片
        function previousSlide() {
            if (currentSlide > 1) {
                showSlide(currentSlide - 1);
            }
        }

        // 全屏切换
        function toggleFullscreen() {
            if (!document.fullscreenElement) {
                document.documentElement.requestFullscreen();
            } else {
                document.exitFullscreen();
            }
        }

        // 负载均衡演示
        function showLoadBalanceDemo() {
            const resultDiv = document.getElementById('load-balance-result');
            const servers = [
                { name: '服务器1', load: Math.floor(Math.random() * 40) + 20 },
                { name: '服务器2', load: Math.floor(Math.random() * 40) + 20 },
                { name: '服务器3', load: Math.floor(Math.random() * 40) + 20 }
            ];

            resultDiv.innerHTML = `
                <div style="display: flex; justify-content: space-around; margin-top: 20px;">
                    ${servers.map(server => `
                        <div style="text-align: center;">
                            <div style="background: var(--secondary-blue); color: white; padding: 10px; border-radius: 8px; margin-bottom: 10px;">
                                ${server.name}
                            </div>
                            <div style="background: var(--bg-light); height: 100px; width: 60px; border-radius: 8px; position: relative; border: 2px solid var(--secondary-blue);">
                                <div style="background: var(--success-green); height: ${server.load}%; width: 100%; position: absolute; bottom: 0; border-radius: 6px; transition: height 0.5s ease;"></div>
                            </div>
                            <div style="margin-top: 10px; font-weight: bold;">${server.load}%</div>
                        </div>
                    `).join('')}
                </div>
                <p style="text-align: center; margin-top: 20px; color: var(--success-green); font-weight: bold;">
                    ✅ 负载均衡成功！各服务器负载均匀分布
                </p>
            `;
        }

        // 键盘事件监听
        document.addEventListener('keydown', function(e) {
            switch(e.key) {
                case 'ArrowRight':
                case ' ':
                    e.preventDefault();
                    nextSlide();
                    break;
                case 'ArrowLeft':
                    e.preventDefault();
                    previousSlide();
                    break;
                case 'Home':
                    e.preventDefault();
                    showSlide(1);
                    break;
                case 'End':
                    e.preventDefault();
                    showSlide(totalSlides);
                    break;
                case 'F11':
                    e.preventDefault();
                    toggleFullscreen();
                    break;
                case 'Escape':
                    if (document.fullscreenElement) {
                        document.exitFullscreen();
                    }
                    break;
            }
        });

        // 鼠标滚轮事件
        let wheelTimeout;
        document.addEventListener('wheel', function(e) {
            clearTimeout(wheelTimeout);
            wheelTimeout = setTimeout(() => {
                if (e.deltaY > 0) {
                    nextSlide();
                } else {
                    previousSlide();
                }
            }, 100);
        });

        // 触摸事件支持
        let touchStartX = 0;
        let touchEndX = 0;

        document.addEventListener('touchstart', function(e) {
            touchStartX = e.changedTouches[0].screenX;
        });

        document.addEventListener('touchend', function(e) {
            touchEndX = e.changedTouches[0].screenX;
            handleSwipe();
        });

        function handleSwipe() {
            const swipeThreshold = 50;
            const diff = touchStartX - touchEndX;

            if (Math.abs(diff) > swipeThreshold) {
                if (diff > 0) {
                    nextSlide(); // 向左滑动，下一页
                } else {
                    previousSlide(); // 向右滑动，上一页
                }
            }
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 设置总页数
            document.getElementById('total-slides').textContent = totalSlides;

            // 初始化第一张幻灯片
            showSlide(1);

            // 添加点击事件（点击右半部分下一页，左半部分上一页）
            document.addEventListener('click', function(e) {
                const clickX = e.clientX;
                const windowWidth = window.innerWidth;

                if (clickX > windowWidth * 0.7) {
                    nextSlide();
                } else if (clickX < windowWidth * 0.3) {
                    previousSlide();
                }
            });

            // 阻止右键菜单（演示模式）
            document.addEventListener('contextmenu', function(e) {
                e.preventDefault();
            });
        });

        // 公司Logo定制功能
        function updateCompanyLogo(logoText) {
            for (let i = 1; i <= totalSlides; i++) {
                const logoElement = document.getElementById(`company-logo-${i}`);
                if (logoElement) {
                    logoElement.textContent = logoText || 'NGINX';
                }
            }
        }

        // 主题色彩定制
        function updateThemeColor(primaryColor, secondaryColor) {
            document.documentElement.style.setProperty('--primary-blue', primaryColor || '#1E3A8A');
            document.documentElement.style.setProperty('--secondary-blue', secondaryColor || '#3B82F6');
        }
    </script>
</body>
</html>
