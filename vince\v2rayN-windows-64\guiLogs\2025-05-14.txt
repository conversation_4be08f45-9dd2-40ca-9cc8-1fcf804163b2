2025-05-14 15:10:37.0485-INFO v2rayN start up | v2rayN - V7.11.3 - X64 | C:\Users\<USER>\Desktop\v2rayN-windows-64\ | C:\Users\<USER>\Desktop\v2rayN-windows-64\v2rayN.exe | Microsoft Windows NT 10.0.26100.0
2025-05-14 15:10:37.7384-<PERSON><PERSON>O Setup Scheduled Tasks
2025-05-14 15:10:55.2311-DEBUG DownloadService,Arg_TimeoutException
2025-05-14 15:10:55.2512-DEBUG    at ServiceLib.Services.DownloadService.DownloadStringAsync(String url, Boolean blProxy, String userAgent, Int32 timeout) in /home/<USER>/work/v2rayN/v2rayN/v2rayN/ServiceLib/Services/DownloadService.cs:line 167
2025-05-14 15:11:10.8272-DEBUG DownloadService,Arg_TimeoutException
2025-05-14 15:11:10.8272-DEBUG    at ServiceLib.Common.DownloaderHelper.DownloadStringAsync(IWebProxy webProxy, String url, String userAgent, Int32 timeout) in /home/<USER>/work/v2rayN/v2rayN/v2rayN/ServiceLib/Common/DownloaderHelper.cs:line 49
   at ServiceLib.Common.DownloaderHelper.DownloadStringAsync(IWebProxy webProxy, String url, String userAgent, Int32 timeout) in /home/<USER>/work/v2rayN/v2rayN/v2rayN/ServiceLib/Common/DownloaderHelper.cs:line 54
   at ServiceLib.Services.DownloadService.DownloadStringViaDownloader(String url, Boolean blProxy, String userAgent, Int32 timeout) in /home/<USER>/work/v2rayN/v2rayN/v2rayN/ServiceLib/Services/DownloadService.cs:line 198
2025-05-14 15:13:03.6034-INFO MyAppExitAsync Begin
2025-05-14 15:13:03.6402-DEBUG DownloadService,net_http_client_execution_error
2025-05-14 15:13:03.6402-DEBUG    at System.Net.Http.HttpConnection.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)
   at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpClient.GetStringAsyncCore(HttpRequestMessage request, CancellationToken cancellationToken)
   at ServiceLib.Common.HttpClientHelper.GetAsync(HttpClient client, String url, CancellationToken token) in /home/<USER>/work/v2rayN/v2rayN/v2rayN/ServiceLib/Common/HttpClientHelper.cs:line 59
   at ServiceLib.Services.DownloadService.DownloadStringAsync(String url, Boolean blProxy, String userAgent, Int32 timeout) in /home/<USER>/work/v2rayN/v2rayN/v2rayN/ServiceLib/Services/DownloadService.cs:line 167
2025-05-14 15:13:03.6402-ERROR System.IO.IOException: net_io_readfailure, 远程主机强迫关闭了一个现有的连接。
2025-05-14 15:13:03.9423-INFO MyAppExitAsync End
2025-05-14 15:13:03.9738-INFO OnExit
2025-05-14 15:13:04.2456-INFO v2rayN start up | v2rayN - V7.11.3 - X64 | C:\Users\<USER>\Desktop\v2rayN-windows-64\ | C:\Users\<USER>\Desktop\v2rayN-windows-64\v2rayN.exe | Microsoft Windows NT 10.0.26100.0
2025-05-14 15:13:05.0177-INFO Setup Scheduled Tasks
2025-05-14 15:13:16.3723-DEBUG DownloadService,net_http_client_execution_error
2025-05-14 15:13:16.3808-DEBUG    at System.Net.Http.HttpConnection.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)
   at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpClient.GetStringAsyncCore(HttpRequestMessage request, CancellationToken cancellationToken)
   at ServiceLib.Common.HttpClientHelper.GetAsync(HttpClient client, String url, CancellationToken token) in /home/<USER>/work/v2rayN/v2rayN/v2rayN/ServiceLib/Common/HttpClientHelper.cs:line 59
   at ServiceLib.Services.DownloadService.DownloadStringAsync(String url, Boolean blProxy, String userAgent, Int32 timeout) in /home/<USER>/work/v2rayN/v2rayN/v2rayN/ServiceLib/Services/DownloadService.cs:line 167
2025-05-14 15:13:16.3808-ERROR System.IO.IOException: net_io_readfailure, 远程主机强迫关闭了一个现有的连接。
2025-05-14 15:13:19.6794-INFO MyAppExitAsync Begin
2025-05-14 15:13:19.7192-DEBUG DownloadService,net_http_client_execution_error
2025-05-14 15:13:19.7192-DEBUG    at System.Net.Http.HttpConnection.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)
   at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpClient.GetStringAsyncCore(HttpRequestMessage request, CancellationToken cancellationToken)
   at ServiceLib.Common.HttpClientHelper.GetAsync(HttpClient client, String url, CancellationToken token) in /home/<USER>/work/v2rayN/v2rayN/v2rayN/ServiceLib/Common/HttpClientHelper.cs:line 59
   at ServiceLib.Services.DownloadService.DownloadStringAsync(String url, Boolean blProxy, String userAgent, Int32 timeout) in /home/<USER>/work/v2rayN/v2rayN/v2rayN/ServiceLib/Services/DownloadService.cs:line 167
2025-05-14 15:13:19.7192-ERROR System.IO.IOException: net_io_readfailure, 远程主机强迫关闭了一个现有的连接。
2025-05-14 15:13:19.7244-DEBUG DownloadService,net_http_client_execution_error
2025-05-14 15:13:19.7244-DEBUG    at ServiceLib.Common.DownloaderHelper.<>c.<DownloadStringAsync>b__3_0(Object sender, AsyncCompletedEventArgs value) in /home/<USER>/work/v2rayN/v2rayN/v2rayN/ServiceLib/Common/DownloaderHelper.cs:line 44
   at Downloader.AbstractDownloadService.OnDownloadFileCompleted(AsyncCompletedEventArgs e) in /Users/<USER>/Sources/Downloader/src/Downloader/AbstractDownloadService.cs:line 441
   at Downloader.DownloadService.SendDownloadCompletionSignal(DownloadStatus state, Exception error) in /Users/<USER>/Sources/Downloader/src/Downloader/DownloadService.cs:line 98
   at Downloader.DownloadService.StartDownload(Boolean forceBuildStorage) in /Users/<USER>/Sources/Downloader/src/Downloader/DownloadService.cs:line 75
   at Downloader.DownloadService.StartDownload(Boolean forceBuildStorage) in /Users/<USER>/Sources/Downloader/src/Downloader/DownloadService.cs:line 80
   at Downloader.AbstractDownloadService.DownloadFileTaskAsync(String[] urls, CancellationToken cancellationToken) in /Users/<USER>/Sources/Downloader/src/Downloader/AbstractDownloadService.cs:line 214
   at ServiceLib.Common.DownloaderHelper.DownloadStringAsync(IWebProxy webProxy, String url, String userAgent, Int32 timeout) in /home/<USER>/work/v2rayN/v2rayN/v2rayN/ServiceLib/Common/DownloaderHelper.cs:line 49
   at ServiceLib.Common.DownloaderHelper.DownloadStringAsync(IWebProxy webProxy, String url, String userAgent, Int32 timeout) in /home/<USER>/work/v2rayN/v2rayN/v2rayN/ServiceLib/Common/DownloaderHelper.cs:line 54
   at ServiceLib.Services.DownloadService.DownloadStringViaDownloader(String url, Boolean blProxy, String userAgent, Int32 timeout) in /home/<USER>/work/v2rayN/v2rayN/v2rayN/ServiceLib/Services/DownloadService.cs:line 198
2025-05-14 15:13:19.7244-ERROR System.Net.Http.HttpRequestException: net_http_client_execution_error
2025-05-14 15:13:20.0194-INFO MyAppExitAsync End
2025-05-14 15:13:20.0681-INFO OnExit
2025-05-14 15:13:20.2342-INFO v2rayN start up | v2rayN - V7.11.3 - X64 | C:\Users\<USER>\Desktop\v2rayN-windows-64\ | C:\Users\<USER>\Desktop\v2rayN-windows-64\v2rayN.exe | Microsoft Windows NT 10.0.26100.0
2025-05-14 15:13:20.8286-INFO Setup Scheduled Tasks
2025-05-14 15:13:25.3726-DEBUG DownloadService,net_http_client_execution_error
2025-05-14 15:13:25.3806-DEBUG    at System.Net.Http.HttpConnection.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)
   at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpClient.GetStringAsyncCore(HttpRequestMessage request, CancellationToken cancellationToken)
   at ServiceLib.Common.HttpClientHelper.GetAsync(HttpClient client, String url, CancellationToken token) in /home/<USER>/work/v2rayN/v2rayN/v2rayN/ServiceLib/Common/HttpClientHelper.cs:line 59
   at ServiceLib.Services.DownloadService.DownloadStringAsync(String url, Boolean blProxy, String userAgent, Int32 timeout) in /home/<USER>/work/v2rayN/v2rayN/v2rayN/ServiceLib/Services/DownloadService.cs:line 167
2025-05-14 15:13:25.3806-ERROR System.IO.IOException: net_io_readfailure, 远程主机强迫关闭了一个现有的连接。
2025-05-14 15:13:41.7086-DEBUG DownloadService,Arg_TimeoutException
2025-05-14 15:13:41.7086-DEBUG    at ServiceLib.Common.DownloaderHelper.DownloadStringAsync(IWebProxy webProxy, String url, String userAgent, Int32 timeout) in /home/<USER>/work/v2rayN/v2rayN/v2rayN/ServiceLib/Common/DownloaderHelper.cs:line 49
   at ServiceLib.Common.DownloaderHelper.DownloadStringAsync(IWebProxy webProxy, String url, String userAgent, Int32 timeout) in /home/<USER>/work/v2rayN/v2rayN/v2rayN/ServiceLib/Common/DownloaderHelper.cs:line 54
   at ServiceLib.Services.DownloadService.DownloadStringViaDownloader(String url, Boolean blProxy, String userAgent, Int32 timeout) in /home/<USER>/work/v2rayN/v2rayN/v2rayN/ServiceLib/Services/DownloadService.cs:line 198
2025-05-14 15:13:42.9381-DEBUG DownloadService,Arg_TimeoutException
2025-05-14 15:13:42.9381-DEBUG    at ServiceLib.Services.DownloadService.DownloadStringAsync(String url, Boolean blProxy, String userAgent, Int32 timeout) in /home/<USER>/work/v2rayN/v2rayN/v2rayN/ServiceLib/Services/DownloadService.cs:line 167
2025-05-14 15:13:42.9474-DEBUG TaskScheduler_UnobservedTaskException,TaskExceptionHolder_UnhandledException (net_webstatus_Timeout)
2025-05-14 15:13:42.9474-DEBUG 
2025-05-14 15:13:42.9474-ERROR System.Net.WebException: net_webstatus_Timeout
2025-05-14 15:13:56.0451-DEBUG DownloadService,net_http_ssl_connection_failed
2025-05-14 15:13:56.0451-DEBUG    at ServiceLib.Common.DownloaderHelper.<>c.<DownloadStringAsync>b__3_0(Object sender, AsyncCompletedEventArgs value) in /home/<USER>/work/v2rayN/v2rayN/v2rayN/ServiceLib/Common/DownloaderHelper.cs:line 44
   at Downloader.AbstractDownloadService.OnDownloadFileCompleted(AsyncCompletedEventArgs e) in /Users/<USER>/Sources/Downloader/src/Downloader/AbstractDownloadService.cs:line 441
   at Downloader.DownloadService.SendDownloadCompletionSignal(DownloadStatus state, Exception error) in /Users/<USER>/Sources/Downloader/src/Downloader/DownloadService.cs:line 98
   at Downloader.DownloadService.StartDownload(Boolean forceBuildStorage) in /Users/<USER>/Sources/Downloader/src/Downloader/DownloadService.cs:line 75
   at Downloader.DownloadService.StartDownload(Boolean forceBuildStorage) in /Users/<USER>/Sources/Downloader/src/Downloader/DownloadService.cs:line 80
   at Downloader.AbstractDownloadService.DownloadFileTaskAsync(String[] urls, CancellationToken cancellationToken) in /Users/<USER>/Sources/Downloader/src/Downloader/AbstractDownloadService.cs:line 214
   at ServiceLib.Common.DownloaderHelper.DownloadStringAsync(IWebProxy webProxy, String url, String userAgent, Int32 timeout) in /home/<USER>/work/v2rayN/v2rayN/v2rayN/ServiceLib/Common/DownloaderHelper.cs:line 49
   at ServiceLib.Common.DownloaderHelper.DownloadStringAsync(IWebProxy webProxy, String url, String userAgent, Int32 timeout) in /home/<USER>/work/v2rayN/v2rayN/v2rayN/ServiceLib/Common/DownloaderHelper.cs:line 54
   at ServiceLib.Services.DownloadService.DownloadStringViaDownloader(String url, Boolean blProxy, String userAgent, Int32 timeout) in /home/<USER>/work/v2rayN/v2rayN/v2rayN/ServiceLib/Services/DownloadService.cs:line 198
2025-05-14 15:13:56.0451-ERROR System.Net.Http.HttpRequestException: net_http_ssl_connection_failed
2025-05-14 15:14:00.2390-DEBUG TaskScheduler_UnobservedTaskException,TaskExceptionHolder_UnhandledException (net_http_ssl_connection_failed)
2025-05-14 15:14:00.2390-DEBUG 
2025-05-14 15:14:00.2390-ERROR System.Net.Http.HttpRequestException: net_http_ssl_connection_failed
2025-05-14 15:14:13.2067-INFO ProcUtils, KillProcess not completing the job, procId
2025-05-14 15:14:13.5086-DEBUG ProcUtils,NoAssociatedProcess
2025-05-14 15:14:13.5086-DEBUG    at System.Diagnostics.Process.EnsureState(State state)
   at System.Diagnostics.Process.EnsureState(State state)
   at System.Diagnostics.Process.get_MainModule()
   at ServiceLib.Common.ProcUtils.ProcessKillByKeyInfo(Boolean review, Nullable`1 procId, String fileName, String processName) in /home/<USER>/work/v2rayN/v2rayN/v2rayN/ServiceLib/Common/ProcUtils.cs:line 170
2025-05-14 15:14:13.8421-INFO ProcUtils, KillProcess not completing the job, procId
2025-05-14 15:14:14.1427-DEBUG ProcUtils,NoAssociatedProcess
2025-05-14 15:14:14.1427-DEBUG    at System.Diagnostics.Process.EnsureState(State state)
   at System.Diagnostics.Process.EnsureState(State state)
   at System.Diagnostics.Process.get_MainModule()
   at ServiceLib.Common.ProcUtils.ProcessKillByKeyInfo(Boolean review, Nullable`1 procId, String fileName, String processName) in /home/<USER>/work/v2rayN/v2rayN/v2rayN/ServiceLib/Common/ProcUtils.cs:line 170
2025-05-14 15:14:26.0263-INFO ProcUtils, KillProcess not completing the job, procId
2025-05-14 15:14:26.3271-DEBUG ProcUtils,NoAssociatedProcess
2025-05-14 15:14:26.3271-DEBUG    at System.Diagnostics.Process.EnsureState(State state)
   at System.Diagnostics.Process.EnsureState(State state)
   at System.Diagnostics.Process.get_MainModule()
   at ServiceLib.Common.ProcUtils.ProcessKillByKeyInfo(Boolean review, Nullable`1 procId, String fileName, String processName) in /home/<USER>/work/v2rayN/v2rayN/v2rayN/ServiceLib/Common/ProcUtils.cs:line 170
2025-05-14 15:14:45.0044-DEBUG DownloadService,Arg_TimeoutException
2025-05-14 15:14:45.0044-DEBUG    at ServiceLib.Services.DownloadService.DownloadStringAsync(String url, Boolean blProxy, String userAgent, Int32 timeout) in /home/<USER>/work/v2rayN/v2rayN/v2rayN/ServiceLib/Services/DownloadService.cs:line 167
2025-05-14 15:14:45.0380-DEBUG DownloadService,Arg_TimeoutException
2025-05-14 15:14:45.0380-DEBUG    at ServiceLib.Services.DownloadService.DownloadStringAsync(String url, Boolean blProxy, String userAgent, Int32 timeout) in /home/<USER>/work/v2rayN/v2rayN/v2rayN/ServiceLib/Services/DownloadService.cs:line 167
2025-05-14 15:15:00.0096-DEBUG DownloadService,Arg_TimeoutException
2025-05-14 15:15:00.0096-DEBUG    at ServiceLib.Common.DownloaderHelper.DownloadStringAsync(IWebProxy webProxy, String url, String userAgent, Int32 timeout) in /home/<USER>/work/v2rayN/v2rayN/v2rayN/ServiceLib/Common/DownloaderHelper.cs:line 49
   at ServiceLib.Common.DownloaderHelper.DownloadStringAsync(IWebProxy webProxy, String url, String userAgent, Int32 timeout) in /home/<USER>/work/v2rayN/v2rayN/v2rayN/ServiceLib/Common/DownloaderHelper.cs:line 54
   at ServiceLib.Services.DownloadService.DownloadStringViaDownloader(String url, Boolean blProxy, String userAgent, Int32 timeout) in /home/<USER>/work/v2rayN/v2rayN/v2rayN/ServiceLib/Services/DownloadService.cs:line 198
2025-05-14 15:15:00.0405-DEBUG DownloadService,Arg_TimeoutException
2025-05-14 15:15:00.0405-DEBUG    at ServiceLib.Common.DownloaderHelper.DownloadStringAsync(IWebProxy webProxy, String url, String userAgent, Int32 timeout) in /home/<USER>/work/v2rayN/v2rayN/v2rayN/ServiceLib/Common/DownloaderHelper.cs:line 49
   at ServiceLib.Common.DownloaderHelper.DownloadStringAsync(IWebProxy webProxy, String url, String userAgent, Int32 timeout) in /home/<USER>/work/v2rayN/v2rayN/v2rayN/ServiceLib/Common/DownloaderHelper.cs:line 54
   at ServiceLib.Services.DownloadService.DownloadStringViaDownloader(String url, Boolean blProxy, String userAgent, Int32 timeout) in /home/<USER>/work/v2rayN/v2rayN/v2rayN/ServiceLib/Services/DownloadService.cs:line 198
2025-05-14 15:17:11.6188-DEBUG TaskScheduler_UnobservedTaskException,TaskExceptionHolder_UnhandledException (net_webstatus_Timeout)
2025-05-14 15:17:11.6188-DEBUG 
2025-05-14 15:17:11.6188-ERROR System.Net.WebException: net_webstatus_Timeout
2025-05-14 15:17:11.6188-DEBUG TaskScheduler_UnobservedTaskException,TaskExceptionHolder_UnhandledException (net_webstatus_Timeout)
2025-05-14 15:17:11.6188-DEBUG 
2025-05-14 15:17:11.6188-ERROR System.Net.WebException: net_webstatus_Timeout
2025-05-14 15:19:04.6268-DEBUG DownloadService,net_http_ssl_connection_failed
2025-05-14 15:19:04.6268-DEBUG    at System.Net.Http.ConnectHelper.EstablishSslConnectionAsync(SslClientAuthenticationOptions sslOptions, HttpRequestMessage request, Boolean async, Stream stream, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)
   at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)
   at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpClient.GetStringAsyncCore(HttpRequestMessage request, CancellationToken cancellationToken)
   at ServiceLib.Common.HttpClientHelper.GetAsync(HttpClient client, String url, CancellationToken token) in /home/<USER>/work/v2rayN/v2rayN/v2rayN/ServiceLib/Common/HttpClientHelper.cs:line 59
   at ServiceLib.Services.DownloadService.DownloadStringAsync(String url, Boolean blProxy, String userAgent, Int32 timeout) in /home/<USER>/work/v2rayN/v2rayN/v2rayN/ServiceLib/Services/DownloadService.cs:line 167
2025-05-14 15:19:04.6268-ERROR System.IO.IOException: net_io_readfailure, 远程主机强迫关闭了一个现有的连接。
2025-05-14 15:19:05.1918-INFO ProcUtils, KillProcess not completing the job, procId
2025-05-14 15:19:05.4930-DEBUG ProcUtils,NoAssociatedProcess
2025-05-14 15:19:05.4930-DEBUG    at System.Diagnostics.Process.EnsureState(State state)
   at System.Diagnostics.Process.EnsureState(State state)
   at System.Diagnostics.Process.get_MainModule()
   at ServiceLib.Common.ProcUtils.ProcessKillByKeyInfo(Boolean review, Nullable`1 procId, String fileName, String processName) in /home/<USER>/work/v2rayN/v2rayN/v2rayN/ServiceLib/Common/ProcUtils.cs:line 170
2025-05-14 15:19:11.1870-DEBUG DownloadService,net_http_client_execution_error
2025-05-14 15:19:11.1876-DEBUG    at System.Net.Http.HttpConnection.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)
   at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpClient.GetStringAsyncCore(HttpRequestMessage request, CancellationToken cancellationToken)
   at ServiceLib.Common.HttpClientHelper.GetAsync(HttpClient client, String url, CancellationToken token) in /home/<USER>/work/v2rayN/v2rayN/v2rayN/ServiceLib/Common/HttpClientHelper.cs:line 59
   at ServiceLib.Services.DownloadService.DownloadStringAsync(String url, Boolean blProxy, String userAgent, Int32 timeout) in /home/<USER>/work/v2rayN/v2rayN/v2rayN/ServiceLib/Services/DownloadService.cs:line 167
2025-05-14 15:19:11.1876-ERROR System.IO.IOException: net_io_readfailure, 远程主机强迫关闭了一个现有的连接。
2025-05-14 15:19:11.1876-DEBUG DownloadService,net_http_client_execution_error
2025-05-14 15:19:11.1876-DEBUG    at ServiceLib.Common.DownloaderHelper.<>c.<DownloadStringAsync>b__3_0(Object sender, AsyncCompletedEventArgs value) in /home/<USER>/work/v2rayN/v2rayN/v2rayN/ServiceLib/Common/DownloaderHelper.cs:line 44
   at Downloader.AbstractDownloadService.OnDownloadFileCompleted(AsyncCompletedEventArgs e) in /Users/<USER>/Sources/Downloader/src/Downloader/AbstractDownloadService.cs:line 441
   at Downloader.DownloadService.SendDownloadCompletionSignal(DownloadStatus state, Exception error) in /Users/<USER>/Sources/Downloader/src/Downloader/DownloadService.cs:line 98
   at Downloader.DownloadService.StartDownload(Boolean forceBuildStorage) in /Users/<USER>/Sources/Downloader/src/Downloader/DownloadService.cs:line 75
   at Downloader.DownloadService.StartDownload(Boolean forceBuildStorage) in /Users/<USER>/Sources/Downloader/src/Downloader/DownloadService.cs:line 80
   at Downloader.AbstractDownloadService.DownloadFileTaskAsync(String[] urls, CancellationToken cancellationToken) in /Users/<USER>/Sources/Downloader/src/Downloader/AbstractDownloadService.cs:line 214
   at ServiceLib.Common.DownloaderHelper.DownloadStringAsync(IWebProxy webProxy, String url, String userAgent, Int32 timeout) in /home/<USER>/work/v2rayN/v2rayN/v2rayN/ServiceLib/Common/DownloaderHelper.cs:line 49
   at ServiceLib.Common.DownloaderHelper.DownloadStringAsync(IWebProxy webProxy, String url, String userAgent, Int32 timeout) in /home/<USER>/work/v2rayN/v2rayN/v2rayN/ServiceLib/Common/DownloaderHelper.cs:line 54
   at ServiceLib.Services.DownloadService.DownloadStringViaDownloader(String url, Boolean blProxy, String userAgent, Int32 timeout) in /home/<USER>/work/v2rayN/v2rayN/v2rayN/ServiceLib/Services/DownloadService.cs:line 198
2025-05-14 15:19:11.1876-ERROR System.Net.Http.HttpRequestException: net_http_client_execution_error
2025-05-14 15:19:13.3881-DEBUG DownloadService,net_http_client_execution_error
2025-05-14 15:19:13.3881-DEBUG    at System.Net.Http.HttpConnection.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)
   at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpClient.GetStringAsyncCore(HttpRequestMessage request, CancellationToken cancellationToken)
   at ServiceLib.Common.HttpClientHelper.GetAsync(HttpClient client, String url, CancellationToken token) in /home/<USER>/work/v2rayN/v2rayN/v2rayN/ServiceLib/Common/HttpClientHelper.cs:line 59
   at ServiceLib.Services.DownloadService.DownloadStringAsync(String url, Boolean blProxy, String userAgent, Int32 timeout) in /home/<USER>/work/v2rayN/v2rayN/v2rayN/ServiceLib/Services/DownloadService.cs:line 167
2025-05-14 15:19:13.3881-ERROR System.IO.IOException: net_io_readfailure, 远程主机强迫关闭了一个现有的连接。
2025-05-14 15:19:13.4399-DEBUG DownloadService,The downloader cannot continue downloading because the network or server failed to download in range.
2025-05-14 15:19:13.4399-DEBUG    at ServiceLib.Common.DownloaderHelper.<>c.<DownloadStringAsync>b__3_0(Object sender, AsyncCompletedEventArgs value) in /home/<USER>/work/v2rayN/v2rayN/v2rayN/ServiceLib/Common/DownloaderHelper.cs:line 44
   at Downloader.AbstractDownloadService.OnDownloadFileCompleted(AsyncCompletedEventArgs e) in /Users/<USER>/Sources/Downloader/src/Downloader/AbstractDownloadService.cs:line 441
   at Downloader.DownloadService.SendDownloadCompletionSignal(DownloadStatus state, Exception error) in /Users/<USER>/Sources/Downloader/src/Downloader/DownloadService.cs:line 98
   at Downloader.DownloadService.StartDownload(Boolean forceBuildStorage) in /Users/<USER>/Sources/Downloader/src/Downloader/DownloadService.cs:line 75
   at Downloader.DownloadService.StartDownload(Boolean forceBuildStorage) in /Users/<USER>/Sources/Downloader/src/Downloader/DownloadService.cs:line 80
   at Downloader.AbstractDownloadService.DownloadFileTaskAsync(String[] urls, CancellationToken cancellationToken) in /Users/<USER>/Sources/Downloader/src/Downloader/AbstractDownloadService.cs:line 214
   at ServiceLib.Common.DownloaderHelper.DownloadStringAsync(IWebProxy webProxy, String url, String userAgent, Int32 timeout) in /home/<USER>/work/v2rayN/v2rayN/v2rayN/ServiceLib/Common/DownloaderHelper.cs:line 49
   at ServiceLib.Common.DownloaderHelper.DownloadStringAsync(IWebProxy webProxy, String url, String userAgent, Int32 timeout) in /home/<USER>/work/v2rayN/v2rayN/v2rayN/ServiceLib/Common/DownloaderHelper.cs:line 54
   at ServiceLib.Services.DownloadService.DownloadStringViaDownloader(String url, Boolean blProxy, String userAgent, Int32 timeout) in /home/<USER>/work/v2rayN/v2rayN/v2rayN/ServiceLib/Services/DownloadService.cs:line 198
2025-05-14 15:19:27.7444-DEBUG DownloadService,不知道这样的主机。 (api.ip.sb:443)
2025-05-14 15:19:27.7444-DEBUG    at ServiceLib.Common.DownloaderHelper.<>c.<DownloadStringAsync>b__3_0(Object sender, AsyncCompletedEventArgs value) in /home/<USER>/work/v2rayN/v2rayN/v2rayN/ServiceLib/Common/DownloaderHelper.cs:line 44
   at Downloader.AbstractDownloadService.OnDownloadFileCompleted(AsyncCompletedEventArgs e) in /Users/<USER>/Sources/Downloader/src/Downloader/AbstractDownloadService.cs:line 441
   at Downloader.DownloadService.SendDownloadCompletionSignal(DownloadStatus state, Exception error) in /Users/<USER>/Sources/Downloader/src/Downloader/DownloadService.cs:line 98
   at Downloader.DownloadService.StartDownload(Boolean forceBuildStorage) in /Users/<USER>/Sources/Downloader/src/Downloader/DownloadService.cs:line 75
   at Downloader.DownloadService.StartDownload(Boolean forceBuildStorage) in /Users/<USER>/Sources/Downloader/src/Downloader/DownloadService.cs:line 80
   at Downloader.AbstractDownloadService.DownloadFileTaskAsync(String[] urls, CancellationToken cancellationToken) in /Users/<USER>/Sources/Downloader/src/Downloader/AbstractDownloadService.cs:line 214
   at ServiceLib.Common.DownloaderHelper.DownloadStringAsync(IWebProxy webProxy, String url, String userAgent, Int32 timeout) in /home/<USER>/work/v2rayN/v2rayN/v2rayN/ServiceLib/Common/DownloaderHelper.cs:line 49
   at ServiceLib.Common.DownloaderHelper.DownloadStringAsync(IWebProxy webProxy, String url, String userAgent, Int32 timeout) in /home/<USER>/work/v2rayN/v2rayN/v2rayN/ServiceLib/Common/DownloaderHelper.cs:line 54
   at ServiceLib.Services.DownloadService.DownloadStringViaDownloader(String url, Boolean blProxy, String userAgent, Int32 timeout) in /home/<USER>/work/v2rayN/v2rayN/v2rayN/ServiceLib/Services/DownloadService.cs:line 198
2025-05-14 15:19:27.7444-ERROR System.Net.Http.HttpRequestException: 不知道这样的主机。 (api.ip.sb:443)
2025-05-14 15:19:43.6264-INFO ProcUtils, KillProcess not completing the job, procId
2025-05-14 15:19:43.9264-DEBUG ProcUtils,NoAssociatedProcess
2025-05-14 15:19:43.9264-DEBUG    at System.Diagnostics.Process.EnsureState(State state)
   at System.Diagnostics.Process.EnsureState(State state)
   at System.Diagnostics.Process.get_MainModule()
   at ServiceLib.Common.ProcUtils.ProcessKillByKeyInfo(Boolean review, Nullable`1 procId, String fileName, String processName) in /home/<USER>/work/v2rayN/v2rayN/v2rayN/ServiceLib/Common/ProcUtils.cs:line 170
2025-05-14 15:19:59.8511-INFO ProcUtils, KillProcess not completing the job, procId
2025-05-14 15:20:00.1526-DEBUG ProcUtils,NoAssociatedProcess
2025-05-14 15:20:00.1526-DEBUG    at System.Diagnostics.Process.EnsureState(State state)
   at System.Diagnostics.Process.EnsureState(State state)
   at System.Diagnostics.Process.get_MainModule()
   at ServiceLib.Common.ProcUtils.ProcessKillByKeyInfo(Boolean review, Nullable`1 procId, String fileName, String processName) in /home/<USER>/work/v2rayN/v2rayN/v2rayN/ServiceLib/Common/ProcUtils.cs:line 170
2025-05-14 15:20:17.7820-DEBUG DownloadService,Arg_TimeoutException
2025-05-14 15:20:17.7820-DEBUG    at ServiceLib.Services.DownloadService.DownloadStringAsync(String url, Boolean blProxy, String userAgent, Int32 timeout) in /home/<USER>/work/v2rayN/v2rayN/v2rayN/ServiceLib/Services/DownloadService.cs:line 167
2025-05-14 15:20:32.7861-DEBUG DownloadService,Arg_TimeoutException
2025-05-14 15:20:32.7861-DEBUG    at ServiceLib.Common.DownloaderHelper.DownloadStringAsync(IWebProxy webProxy, String url, String userAgent, Int32 timeout) in /home/<USER>/work/v2rayN/v2rayN/v2rayN/ServiceLib/Common/DownloaderHelper.cs:line 49
   at ServiceLib.Common.DownloaderHelper.DownloadStringAsync(IWebProxy webProxy, String url, String userAgent, Int32 timeout) in /home/<USER>/work/v2rayN/v2rayN/v2rayN/ServiceLib/Common/DownloaderHelper.cs:line 54
   at ServiceLib.Services.DownloadService.DownloadStringViaDownloader(String url, Boolean blProxy, String userAgent, Int32 timeout) in /home/<USER>/work/v2rayN/v2rayN/v2rayN/ServiceLib/Services/DownloadService.cs:line 198
2025-05-14 15:20:48.7629-DEBUG TaskScheduler_UnobservedTaskException,TaskExceptionHolder_UnhandledException (net_webstatus_Timeout)
2025-05-14 15:20:48.7629-DEBUG 
2025-05-14 15:20:48.7629-ERROR System.Net.WebException: net_webstatus_Timeout
2025-05-14 15:31:09.6093-INFO MyAppExitAsync Begin
2025-05-14 15:31:09.9340-INFO MyAppExitAsync End
2025-05-14 15:31:09.9459-INFO OnExit
