# 更新日志

## [1.3.34] - 2024-03-21

### 改进
- 优化数据库导入操作
  - 使用MySQL的source命令替代重定向导入
  - 增强SQL文件导入的兼容性和宽容度
  - 统一添加-uroot参数，确保权限一致
  - 简化错误处理流程
  - 提高导入成功率

## [1.3.33] - 2024-03-21

### 修复
- 移除SQL文件格式检查功能
  - 直接导入原始SQL文件，不进行格式修改
  - 避免因格式检查导致的文件不存在错误
  - 改进错误处理，增加错误文件存在性检查
  - 优化错误信息显示逻辑
  - 简化文件处理流程

## [1.3.32] - 2024-03-21

### 改进
- 优化无密码MySQL支持
  - 使用布尔变量明确标记是否需要密码
  - 统一有密码和无密码情况下的处理逻辑
  - 确保在无密码环境中正确执行数据库操作
  - 改进SQL文件格式修复在还原功能中的应用
  - 优化临时文件处理和清理机制

## [1.3.31] - 2024-03-21

### 修复
- 修复MySQL密码重复输入问题
  - 在数据库创建和导入过程中复用密码
  - 使用环境变量MYSQL_PWD传递密码
  - 避免用户重复输入密码
  - 优化密码处理逻辑
  - 改进函数间密码传递机制

- 修复SQL文件格式错误问题
  - 添加SQL文件格式自动修复功能
  - 处理Windows格式文件的行尾符号
  - 修复特殊字符和转义问题（如\-）
  - 使用临时文件进行格式修复
  - 添加dos2unix支持（如果可用）

## [1.3.30] - 2024-03-21

### 修复
- 修复MySQL密码检查和获取功能
  - 确保密码提示正确显示在标准错误输出
  - 添加MySQL验证状态提示
  - 修正还原功能中的MySQL密码处理逻辑
  - 改进数据库操作的错误处理
  - 确保在管道中也能正确显示密码提示

## [1.3.29] - 2024-03-21

### 修复
- 修复站点还原时多SQL文件处理问题
  - 添加SQL文件列表显示和选择功能
  - 支持多SQL文件的选择性导入
  - 优化SQL文件路径显示
  - 改进导入提示信息
  - 避免因多SQL文件导致的还原失败

## [1.3.28] - 2024-03-21

### 修复
- 修复shell输出污染数据库密码问题
  - 调整输出方向避免影响函数返回值
  - 清理密码中可能包含的ANSI颜色代码
  - 移除密码中的提示信息和控制字符
  - 改进状态消息的显示方式
  - 确保配置文件中只包含纯净的密码

## [1.3.27] - 2024-03-21

### 修复
- 修复wp-config.php配置更新问题
  - 改进配置文件格式检测逻辑
  - 处理多行定义和引号不匹配问题
  - 自动检测使用的引号类型并保持一致
  - 优化特殊字符处理逻辑
  - 设置安全的文件权限

## [1.3.26] - 2024-03-21

### 修复
- 修复数据库信息提取逻辑
  - 添加新函数extract_db_credentials提取数据库信息
  - 支持多种wp-config.php格式（单引号、双引号和变量赋值）
  - 解决整行PHP代码被错误识别为用户名的问题
  - 备份失败时尝试使用root账户备份
  - 增强还原功能中的错误处理

## [1.3.25] - 2024-03-21

### 新增
- 添加MySQL密码检测功能
  - 自动检测MySQL是否需要密码
  - 无需密码时自动执行数据库操作
  - 需要密码时提示用户输入
  - 支持密码验证和重试机制
  - 适用于安装、备份和还原功能

## [1.3.24] - 2024-03-21

### 修复
- 修复wp-config.php更新问题
  - 解决特殊字符导致sed命令失败的问题
  - 使用awk进行安全的配置替换
  - 支持不同版本WordPress的配置文件格式
  - 增加单引号和双引号格式的自动检测
  - 添加通用格式的备用匹配方案

## [1.3.23] - 2024-03-21

### 改进
- 优化批量备份功能
  - 在全部备份时不再每个站点询问清理
  - 命令行参数backupall模式下不询问清理
  - 添加站点备份计数和进度显示
  - 显示备份成功率统计
  - 改进批量备份用户体验

## [1.3.22] - 2024-03-21

### 安全性
- 移除自动从wp-config-sample.php创建配置文件的功能
  - 不再自动创建wp-config.php
  - 只更新已存在的配置文件
  - 增强安装过程安全性

## [1.3.21] - 2024-03-21

### 增强
- 增强数据库配置功能
  - 确保在导入数据库后更新wp-config.php
  - 备份原始配置文件
  - 支持从wp-config-sample.php创建配置
  - 更新所有必要的数据库连接参数
  - 改进错误提示和状态显示

## [1.3.20] - 2024-03-21

### 修复
- 修复站点路径处理逻辑
  - 不再自动修改用户输入的站点名称
  - 添加明确的目录覆盖确认
  - 修复Nginx配置文件处理
  - 修复备份文件路径处理
  - 改进用户交互提示

## [1.3.19] - 2024-03-21

### 改进
- 优化SQL文件选择流程
  - 移除extract_sql_from_archive函数
  - 直接在安装函数中实现SQL文件搜索和选择
  - 统一的文件搜索和选择逻辑
  - 符合原始需求的用户选择设计

## [1.3.18] - 2024-03-21

### 修正
- 修正安装流程顺序
  - 先完全解压文件并移动到目标目录
  - 再验证WordPress文件
  - 然后搜索SQL文件
  - 符合原始需求设计

## [1.3.17] - 2024-03-21

### 增强
- 增强SQL文件搜索功能
  - 增加对WordPress目录中嵌套ZIP文件的支持
  - 优先检查WordPress目录中的ZIP文件
  - 提供ZIP文件中SQL文件的选择功能
  - 完善SQL文件提取和导入流程

## [1.3.16] - 2024-03-21

### 改进
- 优化SQL文件搜索逻辑
  - 先在WordPress目录中查找SQL文件
  - 未找到时再检查压缩包
  - 改进文件提取和复制逻辑
  - 优化提示信息的准确性

## [1.3.15] - 2024-03-21

### 改进
- 优化安装流程检查顺序
  - 先验证WordPress文件
  - 再检查SQL文件
  - 改进错误处理逻辑
  - 优化提示信息

## [1.3.14] - 2024-03-21

### 改进
- 优化数据库导入提示
  - 显示导入的SQL文件名
  - 显示详细的错误信息
  - 添加无SQL文件时的提示
  - 改进状态消息的可读性

## [1.3.13] - 2024-03-21

### 改进
- 优化WordPress安装流程
  - 移除不必要的wp-config.php检查
  - 简化安装逻辑
  - 直接显示数据库配置信息

## [1.3.12] - 2024-03-21

### 修复
- 修正wp-config.php文件检索逻辑
  - 移除对上级目录的错误检查
  - 仅在站点目录内检索配置文件
  - 符合WordPress标准安装规范

## [1.3.11] - 2024-03-21

### 改进
- 优化wp-config.php文件检索逻辑
  - 同时检查站点目录和上级目录
  - 支持WordPress标准安装的两种配置文件位置
  - 统一所有wp-config.php相关操作的检索逻辑

## [1.3.10] - 2024-03-21

### 安全性改进
- 移除自动创建wp-config.php的功能
  - 改为显示必要的数据库信息
  - 提示用户手动配置WordPress
  - 增强安装过程的安全性

## [1.3.9] - 2024-03-21

### 修复
- 修复wp-config.php文件处理问题
  - 移除wp-config.php作为WordPress特征文件的检查
  - 添加自动从wp-config-sample.php创建wp-config.php的功能
  - 优化WordPress文件验证逻辑
  - 改进配置文件处理流程

## [1.3.8] - 2024-03-21

### 修复
- 修复数据库名称处理问题
  - 使用原始输入的站点名作为数据库名
  - 分离站点路径名和数据库名
  - 保持数据库名称不变
  - 确保配置文件使用正确的数据库名

## [1.3.7] - 2024-03-21

### 修复
- 修复SQL文件处理问题
  - 修复文件选择逻辑
  - 添加目录结构检查
  - 改进文件存在性验证
  - 优化错误处理和提示
  - 添加成功导入提示

## [1.3.6] - 2024-03-21

### 改进
- 优化SQL文件处理逻辑
  - 在解压前检查压缩包中的SQL文件
  - 支持从zip和tar.gz中提取SQL
  - 允许用户选择要导入的SQL文件
  - 改进文件提取和清理流程
  - 添加详细的进度提示

## [1.3.5] - 2024-03-21

### 恢复
- 恢复数据库相关功能
  - 添加数据库创建和配置
  - 添加SQL文件检查和导入
  - 改进数据库用户管理
  - 优化配置文件更新
  - 添加详细的状态提示

## [1.3.4] - 2024-03-21

### 改进
- 改进路径处理机制
  - 自动处理重名文件和目录
  - 添加数字后缀进行重命名
  - 保持原始文件完整性
  - 优化路径处理逻辑
  - 添加用户友好的提示

## [1.3.3] - 2024-03-21

### 修复
- 修复文件大小计算问题
  - 使用du命令替代stat命令
  - 改进错误处理机制
  - 添加数值验证
  - 优化错误提示

## [1.3.2] - 2024-03-21

### 优化
- 合并解压和验证过程
  - 移除重复的解压操作
  - 直接在目标目录验证文件
  - 优化错误处理和清理
  - 提高安装效率

## [1.3.1] - 2024-03-21

### 改进
- 优化文件解压逻辑
  - 直接解压到目标站点目录
  - 自动处理嵌套目录结构
  - 移除临时目录使用
  - 简化文件移动过程

## [1.3.0] - 2024-03-21

### 改进
- 重构WordPress安装流程
  - 优化安装步骤顺序
  - 先验证压缩包再检查空间
  - 改进文件大小获取方法
  - 移除重复的文件检查
  - 移除数据库相关操作
  - 简化安装过程

## [1.2.9] - 2024-03-21

### 改进
- 修改WordPress压缩包验证逻辑
  - 改为检查wp-config.php而不是wp-config-sample.php
  - 移除wp-config.php的复制步骤
  - 修复unzip命令参数错误

## [1.2.8] - 2024-03-21

### 改进
- 优化WordPress压缩包验证逻辑
  - 增加更多特征文件检查
  - 使用find命令递归查找文件
  - 至少需要匹配3个特征文件
  - 添加详细的检查过程提示
  - 修复unzip和tar命令参数

## [1.2.7] - 2024-03-21

### 修复
- 修复压缩包列表显示问题
  - 使用find命令精确查找压缩包
  - 添加文件名排序功能
  - 优化编号显示格式
  - 消除重复显示问题

## [1.2.6] - 2024-03-21

### 改进
- 调整磁盘空间预估算法
  - 将解压空间预估从5倍调整为1.5倍
  - 临时目录空间需求调整为2倍
  - 更准确的空间预估计算

## [1.2.5] - 2024-03-21

### 改进
- 优化磁盘空间检查功能
  - 根据压缩包实际大小计算所需空间
  - 预估解压后所需空间（约5倍压缩包大小）
  - 显示更详细的空间信息
  - 分别检查网站目录和临时目录空间

## [1.2.4] - 2024-03-21

### 新增
- 添加安装前磁盘空间检查功能
  - 检查/var/www目录可用空间
  - 检查临时目录可用空间
  - 显示详细的空间信息

## [1.2.3] - 2024-03-21

### 改进
- 增加WordPress压缩包验证功能
- 改进安装过程的错误处理
- 添加安装过程的状态提示
- 优化文件解压和目录处理逻辑
- 增加临时文件清理机制

## [1.2.2] - 2024-03-21

### 新增
- 增加`cleanbackup`命令行参数用于直接执行备份清理

## [1.2.1] - 2024-03-21

### 改进
- 移除外部配置文件，配置项直接集成到脚本中
- 简化配置管理方式

## [1.2.0] - 2024-03-21

### 新增
- 添加备份文件自动清理功能
- 添加文件路径检查功能
- 添加安全权限设置功能
- 添加主菜单"清理旧备份"选项

### 改进
- 备份目录权限设置为700
- 备份文件权限设置为600
- 增加文件覆盖确认机制
- 备份完成后可选择是否清理旧备份

## [1.1.2] - 2024-03-21

### 新增
- 添加站点权限自动设置功能
  - 自动设置所有者为www-data
  - 自动设置目录权限为755
  - 自动设置文件权限为644
- 在安装和还原时自动应用权限设置

## [1.1.1] - 2024-03-21

### 改进
- 添加ASCII艺术标题
- 添加彩色进度条显示
- 优化界面布局和颜色方案
- 添加操作提示和分隔线
- 统一消息提示样式
- 改进错误和成功提示

## [1.1.0] - 2024-03-21

### 新增
- 增加命令行参数支持
- 增加`backupall`参数用于直接执行全量备份
- 增加`-h/--help`参数显示帮助信息

## [1.0.0] - 2024-03-21

### 新增
- 初始版本发布
- 支持WordPress官方包下载功能
- 支持WordPress站点安装功能
- 支持WordPress站点备份功能(单个/全部)
- 支持WordPress站点还原功能
- 支持自定义备份路径
- 支持多站点管理 