server {
    listen 80;
    server_name blog.example.com;  # 请替换为您的域名
    root /var/www/blog.example.com;  # 请替换为您的WordPress安装目录
    index index.php index.html;
    
    # 日志配置
    access_log /var/log/nginx/blog.example.com.access.log;
    error_log /var/log/nginx/blog.example.com.error.log;
    
    # 引入通用规则
    include /etc/nginx/rules/wordpress_common.conf;
    include /etc/nginx/rules/php_security.conf;
    include /etc/nginx/rules/wordpress_security.conf;
    include /etc/nginx/rules/static_files.conf;
    include /etc/nginx/rules/file_access.conf;
    
    # 该站点特定配置可以在这里添加
} 