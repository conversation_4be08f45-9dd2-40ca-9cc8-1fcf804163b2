<?php
/* ============================================================================================================
	 This software is provided "as is" and any express or implied warranties, including, but not limited to, the
	 implied warranties of merchantibility and fitness for a particular purpose are disclaimed. In no event shall
	 the copyright owner or contributors be liable for any direct, indirect, incidental, special, exemplary, or
	 consequential damages (including, but not limited to, procurement of substitute goods or services; loss of
	 use, data, or profits; or business interruption) however caused and on any theory of liability, whether in
	 contract, strict liability, or tort (including negligence or otherwise) arising in any way out of the use of
	 this software, even if advised of the possibility of such damage.

	 This software is provided free-to-use, but is not free software.  The copyright and ownership remains
	 entirely with the author.  Please distribute and use as necessary, in a personal or commercial environment,
	 but it cannot be sold or re-used without express consent from the author.
   ============================================================================================================ */

/**
 * CSV导出功能
 * 
 * 安全设计:
 * 1. 使用nonce验证请求的合法性
 * 2. 检查当前用户是否有权限查看审核日志
 * 3. 直接输出CSV内容，不在服务器上保存文件
 */

// 确保是WordPress环境
if (!defined('ABSPATH')) {
	// 将日志数据临时保存为session数据而非直接输出文件
	session_start();
	require_once(dirname(dirname(dirname(dirname(__FILE__)))) . '/wp-load.php');
}

// 检查用户权限
if (!current_user_can('publish_posts') && !current_user_can('audit_trail') && !current_user_can('edit_plugins')) {
	wp_die(__('您没有足够的权限导出数据', 'audit-trail'), __('权限错误', 'audit-trail'), array('response' => 403));
}

// 验证nonce以确保请求的合法性
if (!isset($_GET['_wpnonce']) || !wp_verify_nonce($_GET['_wpnonce'], 'audit_csv_export')) {
	wp_die(__('安全验证失败', 'audit-trail'), __('安全错误', 'audit-trail'), array('response' => 403));
}

// 检查当前用户是否被禁止使用插件
$current_user_id = get_current_user_id();
$forbidden_users = explode(',', get_option('audit_forbidden_users', ''));
if (in_array($current_user_id, $forbidden_users)) {
	wp_die(__('您没有权限导出数据', 'audit-trail'), __('权限错误', 'audit-trail'), array('response' => 403));
}

global $wpdb;

// 构建SQL查询
$sql = "SELECT {$wpdb->prefix}audit_trail.happened_at as date, 
			   {$wpdb->users}.user_login as user, 
			   {$wpdb->prefix}audit_trail.operation, 
			   {$wpdb->prefix}audit_trail.data, 
			   {$wpdb->prefix}audit_trail.ip 
		FROM {$wpdb->prefix}audit_trail
		LEFT JOIN {$wpdb->users} ON {$wpdb->users}.ID = {$wpdb->prefix}audit_trail.user_id 
		WHERE 1=1";

// 处理搜索条件
$conditions = array();
$values = array();

if (isset($_GET['search_user']) && !empty($_GET['search_user'])) {
	$search_user = sanitize_text_field($_GET['search_user']);
	$sql .= " AND ({$wpdb->users}.user_login LIKE %s OR {$wpdb->users}.user_nicename LIKE %s)";
	$values[] = '%' . $wpdb->esc_like($search_user) . '%';
	$values[] = '%' . $wpdb->esc_like($search_user) . '%';
}

if (isset($_GET['search_operation']) && !empty($_GET['search_operation'])) {
	$search_operation = sanitize_text_field($_GET['search_operation']);
	$sql .= " AND {$wpdb->prefix}audit_trail.operation LIKE %s";
	$values[] = '%' . $wpdb->esc_like($search_operation) . '%';
}

if (isset($_GET['search_ip']) && !empty($_GET['search_ip'])) {
	$search_ip = sanitize_text_field($_GET['search_ip']);
	// 将IP地址转换为数字处理
	if (filter_var($search_ip, FILTER_VALIDATE_IP, FILTER_FLAG_IPV4)) {
		$ip_num = sprintf('%u', ip2long($search_ip));
		$sql .= " AND {$wpdb->prefix}audit_trail.ip = %d";
		$values[] = $ip_num;
	} else {
		// 模糊匹配IP地址
		$sql .= " AND %s LIKE CONCAT('%', INET_NTOA({$wpdb->prefix}audit_trail.ip), '%')";
		$values[] = $search_ip;
	}
}

if (isset($_GET['search_date_from']) && !empty($_GET['search_date_from'])) {
	$date_from = sanitize_text_field($_GET['search_date_from']);
	$sql .= " AND {$wpdb->prefix}audit_trail.happened_at >= %s";
	$values[] = $date_from . ' 00:00:00';
}

if (isset($_GET['search_date_to']) && !empty($_GET['search_date_to'])) {
	$date_to = sanitize_text_field($_GET['search_date_to']);
	$sql .= " AND {$wpdb->prefix}audit_trail.happened_at <= %s";
	$values[] = $date_to . ' 23:59:59';
}

// 排除"访问页面"日志
if (isset($_GET['exclude_page_views']) && $_GET['exclude_page_views'] == '1') {
	$sql .= " AND {$wpdb->prefix}audit_trail.operation != %s";
	$values[] = 'template_redirect';
}

$sql .= " ORDER BY {$wpdb->prefix}audit_trail.happened_at DESC";

// 准备和执行查询
if (!empty($values)) {
	$sql = $wpdb->prepare($sql, $values);
}

$results = $wpdb->get_results($sql, ARRAY_A);

/**
 * 转义CSV数据中的逗号和引号
 */
function audit_csv_escape($data) {
	if (is_serialized($data)) {
		$data = maybe_unserialize($data);
		if (is_array($data)) {
			$lines = array();
			foreach ($data as $key => $value) {
				if (is_array($value)) {
					$value = json_encode($value);
				}
				$lines[] = "$key: $value";
			}
			$data = implode(" | ", $lines);
		}
	}
	
	return $data;
}

/**
 * 获取操作类型的友好名称
 */
function get_operation_label($operation) {
	$operations = array(
		'switch_theme'         => '切换主题',
		'wp_login'             => '用户登录',
		'wp_logout'            => '用户退出',
		'wp_login_failed'      => '登录失败',
		'retrieve_password'    => '找回密码',
		'delete_user'          => '删除用户',
		'delete_link'          => '删除链接',
		'delete_comment'       => '删除评论',
		'delete_post'          => '删除文章',
		'private_to_published' => '私密改为发布',
		'delete_category'      => '删除分类',
		'delete_attachment'    => '删除附件',
		'template_redirect'    => '访问页面',
		'activate_plugin'      => '激活插件',
		'deactivate_plugin'    => '停用插件',
		'profile_update'       => '更新个人资料',
		'user_register'        => '注册新用户',
		'add_link'             => '添加链接',
		'edit_link'            => '编辑链接',
		'edit_category'        => '编辑分类',
		'add_category'         => '添加分类',
		'edit_comment'         => '编辑评论',
		'save_post'            => '保存文章/页面',
		'add_attachment'       => '添加附件',
		'edit_attachment'      => '编辑附件',
		'draft_to_publish'     => '草稿改为发布',
		'pending_to_publish'   => '待审改为发布',
		'publish_to_draft'     => '发布改为草稿',
		'publish_to_private'   => '发布改为私密',
		'publish_to_pending'   => '发布改为待审',
		'publish_to_trash'     => '发布改为回收站',
		'draft_to_pending'     => '草稿改为待审',
		'draft_to_trash'       => '草稿改为回收站',
		'pending_to_draft'     => '待审改为草稿',
		'pending_to_trash'     => '待审改为回收站',
		'trash_to_publish'     => '回收站改为发布',
		'trash_to_draft'       => '回收站改为草稿',
		'trash_to_pending'     => '回收站改为待审',
		'future_to_publish'    => '定时发布',
		// Elementor操作
		'elementor/editor/after_save'     => 'Elementor编辑器保存',
		'elementor/document/after_save'   => 'Elementor文档保存',
		'elementor/editor/before_save'    => 'Elementor编辑器保存前',
		// Beaver Builder操作
		'fl_builder_after_save_layout'    => 'Beaver Builder保存',
		'fl_builder_before_save_layout'   => 'Beaver Builder保存前',
		'fl_builder_after_layout_rendered' => 'Beaver Builder渲染后',
		// SiteOrigin操作
		'siteorigin_panels_save_post'     => 'SiteOrigin保存',
		'siteorigin_panels_after_render'  => 'SiteOrigin渲染后',
		// WPBakery操作
		'vc_after_save_post'              => 'WPBakery保存',
		'vc_before_save_post'             => 'WPBakery保存前',
		'vc_after_update'                 => 'WPBakery更新后',
		// Divi操作
		'et_fb_save_layout'               => 'Divi Builder保存',
		'et_fb_ajax_save'                 => 'Divi Builder AJAX保存',
		'et_builder_after_save_layout'    => 'Divi Builder布局保存后',
		// Gutenberg操作
		'blocks_parsed'                   => 'Gutenberg编辑',
		'gutenberg_render_block'          => 'Gutenberg块渲染',
	);
	
	return isset($operations[$operation]) ? $operations[$operation] : $operation;
}

// 转换IP地址
function convert_ip($ip) {
	if (is_numeric($ip)) {
		return long2ip($ip);
	}
	return $ip;
}

// 准备CSV数据
$csv_data = array();

// 添加CSV表头
$csv_data[] = array(
	__('日期', 'audit-trail'),
	__('用户', 'audit-trail'),
	__('操作类型', 'audit-trail'),
	__('数据', 'audit-trail'),
	__('IP地址', 'audit-trail')
);

// 处理结果数据
foreach ($results as $row) {
	// 转换操作类型为友好名称
	$row['operation'] = get_operation_label($row['operation']);
	$row['data'] = audit_csv_escape($row['data']);
	$row['ip'] = convert_ip($row['ip']);
	$csv_data[] = $row;
}

// 设置响应头，指定内容类型和文件名
header('Content-Type: text/csv; charset=utf-8');
header('Content-Disposition: attachment; filename=audit-trail-' . date('Y-m-d') . '.csv');
header('Pragma: no-cache');
header('Expires: 0');

// 打开输出缓冲区
$output = fopen('php://output', 'w');

// 输出CSV数据
foreach ($csv_data as $row) {
	fputcsv($output, $row);
}

// 关闭输出流
fclose($output);

// 确保脚本终止
exit;

