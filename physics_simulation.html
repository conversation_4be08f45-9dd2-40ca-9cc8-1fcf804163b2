<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>3D 物理碰撞模拟</title>
    <style>
        body {
            margin: 0;
            overflow: hidden;
            background-color: #f0f0f0;
        }
        canvas {
            display: block;
        }
        .info {
            position: absolute;
            top: 10px;
            left: 10px;
            background: rgba(0, 0, 0, 0.7);
            color: white;
            padding: 10px;
            font-family: Arial, sans-serif;
            font-size: 14px;
            border-radius: 5px;
        }
        .controls {
            position: absolute;
            bottom: 10px;
            left: 10px;
            background: rgba(0, 0, 0, 0.7);
            color: white;
            padding: 10px;
            font-family: Arial, sans-serif;
            font-size: 14px;
            border-radius: 5px;
        }
        button {
            background: #4CAF50;
            border: none;
            color: white;
            padding: 8px 16px;
            text-align: center;
            text-decoration: none;
            display: inline-block;
            font-size: 14px;
            margin: 4px 2px;
            cursor: pointer;
            border-radius: 4px;
        }
    </style>
</head>
<body>
    <div class="info">
        <h3>3D 物理碰撞模拟</h3>
        <p>黑色大质量球体碰撞小正方体堆砌的大立方体</p>
    </div>
    <div class="controls">
        <button id="resetBtn">重置模拟</button>
        <button id="toggleGravityBtn">切换重力</button>
    </div>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r128/three.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/cannon.js/0.6.2/cannon.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/three@0.128.0/examples/js/controls/OrbitControls.min.js"></script>
    <script>
        // 初始化场景、相机和渲染器
        const scene = new THREE.Scene();
        scene.background = new THREE.Color(0xaaaaaa);
        
        const camera = new THREE.PerspectiveCamera(75, window.innerWidth / window.innerHeight, 0.1, 1000);
        camera.position.set(0, 5, 15);
        camera.lookAt(0, 0, 0);
        
        const renderer = new THREE.WebGLRenderer({ antialias: true });
        renderer.setSize(window.innerWidth, window.innerHeight);
        renderer.shadowMap.enabled = true;
        document.body.appendChild(renderer.domElement);
        
        // 添加轨道控制器
        const controls = new THREE.OrbitControls(camera, renderer.domElement);
        controls.enableDamping = true;
        controls.dampingFactor = 0.05;
        controls.screenSpacePanning = false;
        controls.minDistance = 5;
        controls.maxDistance = 50;
        controls.maxPolarAngle = Math.PI / 2;
        
        // 添加环境光和定向光
        const ambientLight = new THREE.AmbientLight(0x404040);
        scene.add(ambientLight);
        
        const directionalLight = new THREE.DirectionalLight(0xffffff, 1);
        directionalLight.position.set(10, 20, 10);
        directionalLight.castShadow = true;
        directionalLight.shadow.camera.left = -20;
        directionalLight.shadow.camera.right = 20;
        directionalLight.shadow.camera.top = 20;
        directionalLight.shadow.camera.bottom = -20;
        directionalLight.shadow.mapSize.width = 2048;
        directionalLight.shadow.mapSize.height = 2048;
        scene.add(directionalLight);
        
        // 初始化物理世界
        let world = new CANNON.World();
        world.gravity.set(0, -9.82, 0);
        world.broadphase = new CANNON.NaiveBroadphase();
        world.solver.iterations = 20;
        
        // 物理材质
        const physicsMaterial = new CANNON.Material("material");
        const contactMaterial = new CANNON.ContactMaterial(
            physicsMaterial, 
            physicsMaterial, 
            {
                friction: 0.4,
                restitution: 0.3
            }
        );
        world.addContactMaterial(contactMaterial);
        
        // 创建地面
        const groundGeometry = new THREE.PlaneGeometry(1000, 1000);
        const groundMaterial = new THREE.MeshStandardMaterial({ 
            color: 0x999999,
            roughness: 0.8,
            metalness: 0.2
        });
        const ground = new THREE.Mesh(groundGeometry, groundMaterial);
        ground.rotation.x = -Math.PI / 2;
        ground.receiveShadow = true;
        scene.add(ground);
        
        // 添加网格以增强无限延伸的视觉效果
        const gridHelper = new THREE.GridHelper(1000, 100, 0x888888, 0xaaaaaa);
        scene.add(gridHelper);
        
        // 添加雾效果使远处淡出，增强无限感
        scene.fog = new THREE.Fog(0xaaaaaa, 50, 500);
        
        const groundShape = new CANNON.Plane();
        const groundBody = new CANNON.Body({ mass: 0 });
        groundBody.addShape(groundShape);
        groundBody.quaternion.setFromAxisAngle(new CANNON.Vec3(1, 0, 0), -Math.PI / 2);
        world.addBody(groundBody);
        
        // 存储物体的数组
        let boxes = [];
        let sphere = null;
        let cubeActivated = false; // 标记立方体是否已激活
        
        // 随机函数
        function random(min, max) {
            return Math.random() * (max - min) + min;
        }
        
        // 创建立方体堆
        function createCubeStack() {
            const boxMaterial = new THREE.MeshStandardMaterial({ 
                color: 0x3399ff,
                roughness: 0.5,
                metalness: 0.5
            });
            
            const cubeSize = 6;
            const segments = 5;
            const boxSize = (cubeSize / segments) * 0.95;
            
            const startX = -cubeSize / 2 + boxSize / 2;
            const startY = boxSize / 2;
            const startZ = -cubeSize / 2 + boxSize / 2;
            
            for (let x = 0; x < segments; x++) {
                for (let y = 0; y < segments; y++) {
                    for (let z = 0; z < segments; z++) {
                        const posX = startX + x * (boxSize / 0.95);
                        const posY = startY + y * (boxSize / 0.95);
                        const posZ = startZ + z * (boxSize / 0.95);
                        
                        // 创建视觉立方体
                        const boxGeometry = new THREE.BoxGeometry(boxSize, boxSize, boxSize);
                        const boxMesh = new THREE.Mesh(boxGeometry, boxMaterial);
                        boxMesh.position.set(posX, posY, posZ);
                        boxMesh.castShadow = true;
                        boxMesh.receiveShadow = true;
                        scene.add(boxMesh);
                        
                        // 创建物理立方体 - 初始设置为静态（质量为0）
                        const boxShape = new CANNON.Box(new CANNON.Vec3(boxSize/2, boxSize/2, boxSize/2));
                        const boxBody = new CANNON.Body({ 
                            mass: 0, // 静态物体
                            material: physicsMaterial
                        });
                        boxBody.addShape(boxShape);
                        boxBody.position.set(posX, posY, posZ);
                        world.addBody(boxBody);
                        
                        // 存储引用和原始位置
                        boxes.push({
                            mesh: boxMesh,
                            body: boxBody,
                            originalPosition: new CANNON.Vec3(posX, posY, posZ)
                        });
                    }
                }
            }
        }
        
        // 创建球体
        function createSphere() {
            const radius = 3;
            const sphereGeometry = new THREE.SphereGeometry(radius, 32, 32);
            const sphereMaterial = new THREE.MeshStandardMaterial({ 
                color: 0x000000,
                roughness: 0.2,
                metalness: 0.8
            });
            const sphereMesh = new THREE.Mesh(sphereGeometry, sphereMaterial);
            sphereMesh.castShadow = true;
            scene.add(sphereMesh);
            
            const sphereShape = new CANNON.Sphere(radius);
            const sphereBody = new CANNON.Body({ 
                mass: 100,
                material: physicsMaterial
            });
            sphereBody.addShape(sphereShape);
            sphereBody.position.set(-15, radius, 0);
            sphereBody.velocity.set(15, 0, 0); // 向右的初始速度
            
            // 添加碰撞检测
            sphereBody.addEventListener("collide", handleCollision);
            
            world.addBody(sphereBody);
            
            sphere = {
                mesh: sphereMesh,
                body: sphereBody
            };
        }
        
        // 处理碰撞事件
        function handleCollision(event) {
            if (!cubeActivated) {
                activateCubes();
                cubeActivated = true;
            }
        }
        
        // 激活立方体（将静态立方体变为动态）
        function activateCubes() {
            console.log("激活立方体！");
            boxes.forEach(box => {
                // 从世界中移除静态物体
                world.removeBody(box.body);
                
                // 创建新的动态物体（有质量）
                const shape = box.body.shapes[0];
                const newBody = new CANNON.Body({
                    mass: 1, // 动态物体
                    material: physicsMaterial
                });
                newBody.addShape(shape);
                newBody.position.copy(box.body.position);
                
                // 添加随机性
                newBody.velocity.set(
                    random(-0.1, 0.1),
                    random(-0.1, 0.1),
                    random(-0.1, 0.1)
                );
                
                world.addBody(newBody);
                
                // 更新引用
                box.body = newBody;
            });
        }
        
        // 初始化场景
        function initScene() {
            // 重置标记
            cubeActivated = false;
            
            // 清除现有物体
            boxes.forEach(box => {
                scene.remove(box.mesh);
            });
            if (sphere) {
                scene.remove(sphere.mesh);
            }
            
            // 重新创建物理世界
            world = new CANNON.World();
            world.gravity.set(0, -9.82, 0);
            world.broadphase = new CANNON.NaiveBroadphase();
            world.solver.iterations = 20;
            world.addContactMaterial(contactMaterial);
            
            // 添加地面
            world.addBody(groundBody);
            
            // 清空数组
            boxes = [];
            sphere = null;
            
            // 创建新物体
            createCubeStack();
            createSphere();
        }
        
        // 重置按钮
        document.getElementById('resetBtn').addEventListener('click', initScene);
        
        // 切换重力按钮
        let gravityEnabled = true;
        document.getElementById('toggleGravityBtn').addEventListener('click', () => {
            gravityEnabled = !gravityEnabled;
            world.gravity.set(0, gravityEnabled ? -9.82 : 0, 0);
        });
        
        // 窗口大小调整
        window.addEventListener('resize', () => {
            camera.aspect = window.innerWidth / window.innerHeight;
            camera.updateProjectionMatrix();
            renderer.setSize(window.innerWidth, window.innerHeight);
        });
        
        // 初始化场景
        initScene();
        
        // 动画循环
        const timeStep = 1 / 60;
        function animate() {
            requestAnimationFrame(animate);
            
            // 更新物理
            world.step(timeStep);
            
            // 更新立方体位置
            boxes.forEach(box => {
                box.mesh.position.copy(box.body.position);
                box.mesh.quaternion.copy(box.body.quaternion);
            });
            
            // 更新球体位置
            if (sphere) {
                sphere.mesh.position.copy(sphere.body.position);
                sphere.mesh.quaternion.copy(sphere.body.quaternion);
            }
            
            // 更新控制器
            controls.update();
            
            renderer.render(scene, camera);
        }
        
        animate();
    </script>
</body>
</html> 