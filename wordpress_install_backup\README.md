# WordPress 站点管理脚本

这是一个用于管理多个WordPress站点的脚本，支持以下功能：

1. 下载WordPress官方安装包
2. 安装WordPress站点
3. 备份WordPress站点(单个/全部)
4. 还原WordPress站点

## 环境要求

- Linux系统
- Nginx
- PHP
- MySQL/MariaDB
- zip/unzip工具
- tar工具

## 目录结构

```
/var/www/                # WordPress站点根目录
├── site1/              # 站点1
├── site2/              # 站点2
└── ...                 # 其他站点

/etc/nginx/sites-enabled/ # Nginx站点配置目录
```

## 使用说明

1. 给予脚本执行权限：
```bash
chmod +x wordpress_manager.sh
```

2. 运行脚本：

交互式菜单模式：
```bash
./wordpress_manager.sh
```

命令行参数模式：
```bash
./wordpress_manager.sh backupall  # 直接执行全量备份
./wordpress_manager.sh --help     # 显示帮助信息
```

## 注意事项

- 脚本需要root权限运行
- 备份文件存储在自定义的backup_path路径下
- 数据库root默认无密码，如有密码需要手动输入 