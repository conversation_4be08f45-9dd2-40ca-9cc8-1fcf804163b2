# WordPress Audit Trail 插件

## 插件概述
Audit Trail 是一款全面的 WordPress 管理活动审计和日志记录插件，专为详细追踪博客后台的所有操作而设计，支持数据恢复和版本控制功能。插件使用中文界面，提供强大的日志记录和搜索功能，同时注重性能优化和资源消耗控制。

## 当前版本2.5.15 [已更新]

## 核心功能

### 全面的操作日志
- **文章与页面管理**：记录创建、编辑、删除、状态变更等操作 [完成]
- **页面构建器操作**：支持主流页面构建器（Elementor、Beaver Builder、Divi、WPBakery、SiteOrigin、Gutenberg） [完成]
- **用户活动**：记录登录、登出、密码重置、注册、个人资料更新、登录失败等 [完成]
- **媒体库操作**：记录文件上传、编辑、删除等 [完成]
- **评论管理**：记录评论编辑和删除操作 [完成]
- **分类目录和链接**：记录分类和链接的添加、编辑、删除 [完成]
- **主题和插件**：记录主题切换和插件激活/停用 [完成]
- **页面访问**：可选择记录用户和访客的页面访问情况 [完成]

### 日志管理功能
- **高级搜索**：按用户名、操作类型、IP地址、日期范围搜索 [完成]
- **CSV导出**：支持将日志导出为CSV文件进行离线分析 [完成]
- **权限控制**：可禁止特定用户使用审计插件 [完成]
- **数据过滤**：包括排除页面访问记录等功能 [完成]
- **自动清理**：支持自动清理过期日志，避免数据库膨胀 [完成]
- **批量处理**：支持按日期和操作类型批量删除日志记录 [完成]
- **系统状态**：显示日志统计信息和清理历史 [完成]

### 性能优化功能
- **高流量模式**：针对高流量网站优化，减少数据库压力 [完成]
- **智能采样**：页面访问日志的采样记录，可自定义采样率（1-100%） [完成]
- **批量日志记录**：减少数据库写入频率，提高性能 [完成]
- **静态资源排除**：自动排除静态资源路径，减少无用日志 [完成]
- **排除路径**：可配置不记录日志的URL路径 [完成]
- **数据库优化**：通过索引提高查询性能 [完成]

### 安全增强特性
- **敏感数据保护**：自动屏蔽密码等敏感信息 [完成]
- **IPv6支持**：完整支持IPv4和IPv6地址记录 [完成]
- **用户权限控制**：精细化的访问权限管理 [完成]
- **安全验证**：使用WordPress nonce验证所有操作 [完成]
- **XSS防护**：添加安全头部和内容安全策略，防止跨站攻击 [新增]
- **SQL注入防护**：全面采用预处理语句，防止SQL注入 [新增]
- **敏感信息识别**：自动识别并掩盖信用卡号、社会安全号等敏感信息 [新增]
- **防爬虫保护**：智能识别并阻止爬虫访问 [新增]
- **事务处理**：确保数据库操作的完整性 [新增]

## 默认配置
为方便快速部署和使用，插件采用以下默认配置：
- **监控选项**：除"用户页访问"外，其余操作类型默认启用监控 [完成]
- **自动清理**：默认启用，保留最近30天的日志记录 [完成]
- **高流量模式**：默认启用，可减轻数据库压力 [完成]
- **采样率**：页面访问记录默认采用10%的采样率 [完成]
- **安全头部**：默认启用安全头部，提供防XSS保护 [新增]

## 技术实现与架构

### 数据存储结构
插件在WordPress数据库中创建`{wp_prefix}audit_trail`表，包含以下字段：
- `id`：日志记录的唯一标识符
- `operation`：操作类型（如save_post, wp_login等）
- `user_id`：执行操作的用户ID
- `ip`：用户IP地址（支持IPv4和IPv6）
- `happened_at`：操作发生的时间
- `item_id`：相关项目的ID（如文章ID、用户ID等）
- `data`：操作相关的详细数据（JSON格式）
- `title`：操作项目的标题或名称

### 数据库优化
- **索引优化**：为关键字段（operation, user_id, happened_at, ip）添加索引 [完成]
- **IP字段升级**：从int类型升级为varchar(45)以支持IPv6格式 [完成]
- **数据清理**：自动或手动清理过期数据，避免数据库膨胀 [完成]
- **批量操作**：批量插入和删除优化 [完成]

### 核心类与文件结构
1. **主要类**
   - `Audit_Trail`：主插件类，负责初始化、菜单创建和基本功能 [完成]
   - `AT_Auditor`：核心监听类，处理所有操作的记录 [完成]
   - `AT_Audit`：日志记录模型类，提供数据库操作接口 [完成]
   - `Audit_Trail_Table`：表格显示类，继承WP_List_Table [完成]
   - `AuditAjax`：处理AJAX请求的类 [完成]
   - `AuditTrailBatchLogger`：批量日志处理类，优化高流量站点性能 [完成]

2. **文件结构**
   - `audit-trail.php`：主插件文件，包含初始化代码 [完成]
   - `models/auditor.php`：包含监听和记录操作的核心逻辑 [完成]
   - `models/audit.php`：数据模型和数据库操作 [完成]
   - `models/pager.php`：分页和表格显示逻辑 [完成]
   - `models/batch-logger.php`：批量日志处理逻辑 [完成]
   - `view/`：包含界面模板 [完成]
   - `locale/`：多语言支持文件 [完成]
   - `ajax.php`：处理AJAX请求 [完成]
   - `csv.php`：处理CSV导出功能 [完成]

### 钩子系统
插件通过WordPress钩子系统监听各类操作，主要包括：
- 文章相关：`save_post`, `delete_post`, 多种状态转换钩子 [完成]
- 用户相关：`wp_login`, `wp_logout`, `user_register`, `profile_update`, `login_errors`, `wp_login_failed`等 [完成]
- 评论相关：`edit_comment`, `delete_comment` [完成]
- 附件相关：`add_attachment`, `edit_attachment`, `delete_attachment` [完成]
- 分类相关：`add_category`, `edit_category`, `delete_category` [完成]
- 链接相关：`add_link`, `edit_link`, `delete_link` [完成]
- 主题相关：`switch_theme` [完成]
- 插件相关：`activate_plugin`, `deactivate_plugin` [完成]
- 页面访问：`template_redirect` [完成]
- 页面构建器相关：
  - Elementor: `elementor/editor/after_save`, `elementor/document/after_save`, `elementor/editor/before_save` [完成]
  - Beaver Builder: `fl_builder_after_save_layout`, `fl_builder_before_save_layout`, `fl_builder_after_layout_rendered` [完成]
  - SiteOrigin: `siteorigin_panels_save_post`, `siteorigin_panels_after_render` [完成]
  - WPBakery: `vc_after_save_post`, `vc_before_save_post`, `vc_after_update` [完成]
  - Divi: `et_fb_save_layout`, `et_fb_ajax_save`, `et_builder_after_save_layout` [完成]
  - Gutenberg: `blocks_parsed` [完成]

### 自动任务系统
- **计划任务**：使用WordPress cron系统实现自动清理过期日志 [完成]
- **关机钩子**：使用`shutdown`钩子确保批量日志在页面加载完成后记录 [完成]
- **版本升级**：自动检测和平滑处理插件版本升级 [完成]

## 安全设计
- **权限控制**：限制只有具有`publish_posts`、`audit_trail`或`edit_plugins`权限的用户才能访问审计日志 [完成]
- **敏感数据处理**：自动识别并屏蔽密码、令牌、密钥等敏感信息 [完成]
- **用户过滤**：可配置忽略特定用户的操作 [完成]
- **禁用权限**：可设置禁止特定用户使用审计插件 [完成]
- **IPv6支持**：适当处理IPv4和IPv6地址 [完成]
- **CSV导出保护**：验证nonce和用户权限，防止未授权导出 [完成]

## 性能优化设计
- **批量日志记录**：通过`AuditTrailBatchLogger`类实现页面访问日志的批量提交 [完成]
- **智能采样**：可配置页面访问日志的采样率，减少数据库写入 [完成]
- **静态资源排除**：自动排除CSS、JS、图片等静态资源的访问记录 [完成]
- **自动清理机制**：定期清理过期日志，防止数据库膨胀 [完成]
- **数据库索引**：优化查询性能的索引设计 [完成]
- **分批删除**：使用LIMIT限制每次删除的数量，避免锁表 [完成]

## 自定义扩展
插件提供多个过滤器和动作钩子，便于开发者自定义和扩展功能：
- `audit_collect`：添加新的监听操作类型 [完成]
- `audit_show_operation`：自定义操作类型的显示方式 [完成]
- `audit_show_item`：自定义操作项目的显示方式 [完成]
- `audit_show_details`：自定义操作详情的显示方式 [完成]
- `audit_listen`：添加自定义操作的监听钩子 [完成]
- `audit_operation`：进一步自定义操作显示方式 [完成]

## 用户界面特点
- **AJAX技术**：使用AJAX加载详细信息，减少页面刷新 [完成]
- **响应式设计**：适应不同屏幕尺寸的管理界面 [完成]
- **数据过滤**：提供多种过滤选项，便于管理大量日志数据 [完成]
- **批量操作**：支持批量删除日志记录 [完成]
- **自定义搜索**：高级搜索和过滤功能 [完成]
- **统计信息**：显示日志数量和系统状态 [完成]

## 配置选项
- **日志保留期**：设置自动清理日志的保留天数（默认30天） [完成]
- **高流量模式**：启用/禁用高流量优化功能（默认启用） [完成]
- **采样率设置**：设置页面访问日志的记录百分比（默认10%） [完成]
- **排除路径**：设置不记录日志的URL路径 [完成]
- **忽略用户**：设置不记录其操作的用户ID [完成]
- **禁止用户**：设置禁止访问审计功能的用户ID [完成]

## 语言支持
默认使用简体中文界面，同时支持多种语言：
- 简体中文（默认） [完成]
- 英语 [完成]
- 爱沙尼亚语 [完成]
- 白俄罗斯语 [完成]
- 德语 [完成]
- 日语 [完成]
- 罗马尼亚语 [完成]
- 立陶宛语 [完成]

## 版本更新与升级
插件包含自动升级机制，可以平滑处理数据库结构变更：
- 版本2.5.0引入了性能优化和安全增强功能 [完成]
- 自动添加数据库索引提高查询性能 [完成]
- IP字段从int类型升级为varchar(45)以支持IPv6 [完成]
- 引入计划任务实现自动日志清理 [完成]

## 未来功能规划
- **智能日志聚合**：合并短时间内的重复操作，进一步减少数据库负担
- **轻量级统计图表**：以简单图表展示用户活动情况，不增加过多资源消耗
- **关键操作通知**：仅针对重要操作发送通知，减少干扰
- **搜索结果导出优化**：仅导出必要字段，提高导出效率
- **更多页面构建器支持**：扩展对其他流行页面构建器的支持

## 故障排除指南

### 常见问题与解决方案
#### 安装后打开插件页面报500错误
此问题已在2.5.8版本中修复。如果您仍然遇到此问题，请尝试以下解决方法：
- 确保您的PHP版本在7.2-8.4范围内
- 检查WordPress数据库权限，确保插件可以创建表
- 增加PHP内存限制，在wp-config.php中添加：`define('WP_MEMORY_LIMIT', '256M');`
- 禁用所有其他插件，检查是否存在冲突
- 如果问题仍然存在，请尝试重新安装最新版本的插件

#### 数据库表未创建问题
如果在使用中发现数据库表未正确创建，可以尝试：
- 停用并重新激活插件
- 通过插件选项页面的"系统检查"功能验证表结构
- 如果问题仍然存在，可以手动访问`/wp-admin/admin.php?page=audit-trail.php&check_db=1`触发数据库检查

#### 性能优化建议
对于高流量网站：
- 启用"高流量模式"选项
- 设置合理的采样率（建议值：1-5%）
- 启用自动清理功能，设置适当的日志保留期（建议值：7-14天）
- 配置排除路径，避免记录不必要的页面访问
