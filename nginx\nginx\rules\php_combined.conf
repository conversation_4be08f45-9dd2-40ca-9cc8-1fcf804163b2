# PHP处理与安全规则
# 此文件合并了php_handling.conf和php_security.conf的所有功能

# PHP文件处理
location ~ \.php$ {
    try_files $uri =404;
    
    # 防止PHP路径解析漏洞
    if ($fastcgi_script_name ~ \..*\/.*php) {
        return 403;
    }
    
    # 常规PHP文件处理
    fastcgi_split_path_info ^(.+\.php)(/.+)$;
    fastcgi_pass unix:/var/run/php/php8.4-fpm.sock;  # 根据您的PHP版本调整
    fastcgi_index index.php;
    include fastcgi_params;
    fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
    fastcgi_param PATH_INFO $fastcgi_path_info;
    fastcgi_intercept_errors on;
    
    # 隐藏PHP版本
    fastcgi_hide_header X-Powered-By;
    proxy_hide_header X-Powered-By;
    
    # 安全响应头
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header Referrer-Policy "strict-origin-when-cross-origin" always;
    
    # 增加FastCGI超时时间
    fastcgi_read_timeout 300;
}

# 增强安全性 - 隐藏PHP错误日志
fastcgi_param PHP_VALUE "display_errors=0\nlog_errors=1";

# 增加buffer size - 提高性能
fastcgi_buffers 16 16k;
fastcgi_buffer_size 32k; 
