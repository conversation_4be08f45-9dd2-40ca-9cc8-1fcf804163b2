# 练习2：配置多个网站（虚拟主机）

## 🎯 练习目标
学会在一台服务器上托管多个不同的网站

## 📚 背景知识

**虚拟主机** 就像一栋公寓楼：
- 一栋楼（一台服务器）
- 多个房间（多个网站）
- 每个房间有不同的门牌号（域名）
- 访客根据门牌号找到对应的房间

## 📋 练习步骤

### 步骤1：规划网站

我们将创建三个不同的网站：
1. **主网站**：www.mycompany.local（公司官网）
2. **博客网站**：blog.mycompany.local（技术博客）
3. **商店网站**：shop.mycompany.local（在线商店）

### 步骤2：创建网站目录和文件

```bash
# 创建网站目录
sudo mkdir -p /var/www/company
sudo mkdir -p /var/www/blog  
sudo mkdir -p /var/www/shop
```

### 步骤3：创建主网站（公司官网）

```bash
sudo nano /var/www/company/index.html
```

```html
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <title>我的公司 - 官方网站</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 0; padding: 20px; background: #f0f8ff; }
        .header { background: #2c3e50; color: white; padding: 20px; text-align: center; }
        .content { max-width: 800px; margin: 20px auto; background: white; padding: 30px; border-radius: 10px; }
        .nav { background: #34495e; padding: 10px; text-align: center; }
        .nav a { color: white; text-decoration: none; margin: 0 15px; }
    </style>
</head>
<body>
    <div class="header">
        <h1>🏢 我的科技公司</h1>
        <p>创新科技，改变未来</p>
    </div>
    
    <div class="nav">
        <a href="/">首页</a>
        <a href="http://blog.mycompany.local">技术博客</a>
        <a href="http://shop.mycompany.local">在线商店</a>
    </div>
    
    <div class="content">
        <h2>欢迎来到我们公司</h2>
        <p>我们是一家专注于创新技术的公司，致力于为客户提供最优质的服务。</p>
        
        <h3>我们的服务</h3>
        <ul>
            <li>网站开发</li>
            <li>移动应用开发</li>
            <li>云服务解决方案</li>
            <li>技术咨询</li>
        </ul>
        
        <h3>联系我们</h3>
        <p>电话：400-123-4567</p>
        <p>邮箱：<EMAIL></p>
        <p>地址：北京市朝阳区科技园区</p>
    </div>
</body>
</html>
```

### 步骤4：创建博客网站

```bash
sudo nano /var/www/blog/index.html
```

```html
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <title>技术博客 - 分享技术心得</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 0; padding: 20px; background: #f5f5f5; }
        .header { background: #e74c3c; color: white; padding: 20px; text-align: center; }
        .content { max-width: 800px; margin: 20px auto; }
        .post { background: white; margin: 20px 0; padding: 20px; border-radius: 8px; box-shadow: 0 2px 5px rgba(0,0,0,0.1); }
        .nav { background: #c0392b; padding: 10px; text-align: center; }
        .nav a { color: white; text-decoration: none; margin: 0 15px; }
    </style>
</head>
<body>
    <div class="header">
        <h1>📝 技术博客</h1>
        <p>分享技术知识，记录学习心得</p>
    </div>
    
    <div class="nav">
        <a href="http://www.mycompany.local">返回主站</a>
        <a href="/">博客首页</a>
        <a href="http://shop.mycompany.local">在线商店</a>
    </div>
    
    <div class="content">
        <div class="post">
            <h2>🚀 Nginx 学习心得</h2>
            <p><small>发布时间：2024-01-15</small></p>
            <p>今天学习了 Nginx 的基本配置，发现它真的很强大！可以轻松配置多个网站...</p>
            <a href="#">阅读全文</a>
        </div>
        
        <div class="post">
            <h2>🐧 Linux 服务器管理技巧</h2>
            <p><small>发布时间：2024-01-10</small></p>
            <p>分享一些 Linux 服务器日常管理的实用技巧，包括日志查看、性能监控等...</p>
            <a href="#">阅读全文</a>
        </div>
        
        <div class="post">
            <h2>🌐 Web 开发最佳实践</h2>
            <p><small>发布时间：2024-01-05</small></p>
            <p>总结了一些 Web 开发中的最佳实践，包括性能优化、安全防护等方面...</p>
            <a href="#">阅读全文</a>
        </div>
    </div>
</body>
</html>
```

### 步骤5：创建商店网站

```bash
sudo nano /var/www/shop/index.html
```

```html
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <title>在线商店 - 优质商品</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 0; padding: 20px; background: #fff8e1; }
        .header { background: #ff9800; color: white; padding: 20px; text-align: center; }
        .content { max-width: 1000px; margin: 20px auto; }
        .product { background: white; margin: 15px; padding: 20px; border-radius: 8px; box-shadow: 0 2px 5px rgba(0,0,0,0.1); display: inline-block; width: 250px; vertical-align: top; }
        .nav { background: #f57c00; padding: 10px; text-align: center; }
        .nav a { color: white; text-decoration: none; margin: 0 15px; }
        .price { color: #e65100; font-weight: bold; font-size: 1.2em; }
    </style>
</head>
<body>
    <div class="header">
        <h1>🛒 在线商店</h1>
        <p>优质商品，优惠价格</p>
    </div>
    
    <div class="nav">
        <a href="http://www.mycompany.local">返回主站</a>
        <a href="http://blog.mycompany.local">技术博客</a>
        <a href="/">商店首页</a>
    </div>
    
    <div class="content">
        <h2>🔥 热门商品</h2>
        
        <div class="product">
            <h3>💻 笔记本电脑</h3>
            <p>高性能办公笔记本，适合程序员使用</p>
            <p class="price">¥5,999</p>
            <button>加入购物车</button>
        </div>
        
        <div class="product">
            <h3>📱 智能手机</h3>
            <p>最新款智能手机，拍照效果出色</p>
            <p class="price">¥3,299</p>
            <button>加入购物车</button>
        </div>
        
        <div class="product">
            <h3>🎧 无线耳机</h3>
            <p>降噪无线耳机，音质清晰</p>
            <p class="price">¥899</p>
            <button>加入购物车</button>
        </div>
        
        <div class="product">
            <h3>⌚ 智能手表</h3>
            <p>健康监测智能手表，运动必备</p>
            <p class="price">¥1,599</p>
            <button>加入购物车</button>
        </div>
    </div>
</body>
</html>
```

### 步骤6：配置 Nginx 虚拟主机

#### 6.1 主网站配置

```bash
sudo nano /etc/nginx/sites-available/company
```

```nginx
server {
    listen 80;
    server_name www.mycompany.local mycompany.local;
    
    root /var/www/company;
    index index.html;
    
    location / {
        try_files $uri $uri/ =404;
    }
    
    access_log /var/log/nginx/company.access.log;
    error_log /var/log/nginx/company.error.log;
}
```

#### 6.2 博客网站配置

```bash
sudo nano /etc/nginx/sites-available/blog
```

```nginx
server {
    listen 80;
    server_name blog.mycompany.local;
    
    root /var/www/blog;
    index index.html;
    
    location / {
        try_files $uri $uri/ =404;
    }
    
    access_log /var/log/nginx/blog.access.log;
    error_log /var/log/nginx/blog.error.log;
}
```

#### 6.3 商店网站配置

```bash
sudo nano /etc/nginx/sites-available/shop
```

```nginx
server {
    listen 80;
    server_name shop.mycompany.local;
    
    root /var/www/shop;
    index index.html;
    
    location / {
        try_files $uri $uri/ =404;
    }
    
    access_log /var/log/nginx/shop.access.log;
    error_log /var/log/nginx/shop.error.log;
}
```

### 步骤7：启用网站配置

```bash
# 启用所有网站
sudo ln -s /etc/nginx/sites-available/company /etc/nginx/sites-enabled/
sudo ln -s /etc/nginx/sites-available/blog /etc/nginx/sites-enabled/
sudo ln -s /etc/nginx/sites-available/shop /etc/nginx/sites-enabled/

# 禁用默认网站（可选）
sudo rm /etc/nginx/sites-enabled/default

# 测试配置
sudo nginx -t

# 重新加载配置
sudo systemctl reload nginx
```

### 步骤8：配置本地域名解析

由于我们使用的是测试域名，需要在本地配置域名解析：

```bash
sudo nano /etc/hosts
```

添加以下内容：
```
127.0.0.1 www.mycompany.local
127.0.0.1 mycompany.local
127.0.0.1 blog.mycompany.local
127.0.0.1 shop.mycompany.local
```

### 步骤9：测试网站

在浏览器中分别访问：
1. http://www.mycompany.local
2. http://blog.mycompany.local
3. http://shop.mycompany.local

## ✅ 检查清单

- [ ] 创建了三个不同的网站目录
- [ ] 每个网站都有独特的内容和样式
- [ ] 配置了三个不同的 Nginx 虚拟主机
- [ ] 配置了本地域名解析
- [ ] 可以正常访问所有三个网站
- [ ] 网站之间的链接正常工作

## 🎉 恭喜！

你已经成功学会了：
1. 创建多个虚拟主机
2. 配置不同的域名
3. 管理多个网站

## 🚀 进阶挑战

1. 为每个网站添加更多页面
2. 配置不同的错误页面
3. 为不同网站设置不同的访问日志格式
4. 尝试配置 HTTPS
