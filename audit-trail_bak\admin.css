.options
{
list-style-type: none;
margin: 0;
padding: 0;
margin: 0.5em 0;
}

.options li
{
margin: 0; padding: 0;
line-height: 1.8;
}

.csv
{
float: right;
padding: 4px;
}

.csv a
{
border: none;
}

.pager
{
font-size: 0.9em;
text-align: right;
}

.pager select, .pager input { font-size: 0.9em }



#subsubmenu li {
	display: inline;
	line-height: 170%;
	list-style: none;
	text-align: center;
}

#subsubmenu {
  font-size: 0.9em;
	background: #CDD9E2;
	border-bottom: none;
	margin: 0;
	color: #4F5D69;
	padding: 2px 2em 0 5em;
}

#subsubmenu .current {
	background: #f9fcfe;
	border-top: 1px solid #045290;
	color: black;
}

#subsubmenu a {
	border: none;
	color: #4F5D69;
	font-size: 12px;
	padding: 3px 1em 2px 1em;
}

#subsubmenu a:hover {
	background: #89A5BB;
	color: #393939;
}

#subsubmenu li {
	line-height: 150%;
}

.audit
{
 width: 100%;
}

.alt
{
 background-color: #efefef;
}

.audit th
{
background-color: #ccc;
text-align: left;
}

.audit td
{
vertical-align: top;
}

.audit textarea
{
width: 95%;
}

.audit ins
{
color: green;
}

.audit del
{
color: red;
text-decoration: line-through;
}

.diff
{
border: 1px solid #999;
padding: 2px;
overflow: auto;
max-height: 200px;
}

fieldset
{
border: 1px solid #ccc;
margin-bottom: 1em;
padding-left: 1em;
}

legend
{
font-weight: bold;
font-size: 1.2em;
}

.bulleted li {
list-style-type: square;
margin-left: 20px;
}

.donations li
{
list-style-type: none;
float: left;
text-align: center;
padding: 1em 2em;
}

.translators li
{
float: left;
width: 300px;
margin-left: 1.5em;
}

div .supporter strong
{
background-color: yellow;
}


.wrap h5
{
font-size: 1em;
}

.detail-data {
    color: #777;
    font-size: 0.9em;
    font-style: italic;
    word-break: break-word;
}

.wp-list-table .column-item_id {
    width: 35%;
}

.wp-list-table .column-operation {
    width: 25%;
}

.visitor-label {
    display: inline-block;
    background-color: #f0f0f0;
    border: 1px solid #ddd;
    border-radius: 3px;
    padding: 2px 6px;
    font-size: 0.9em;
    color: #666;
}

/* Audit Trail Admin CSS */

.audit-search-form {
	background: #fff;
	border: 1px solid #ccd0d4;
	padding: 15px 20px;
	margin: 20px 0;
	border-radius: 4px;
	box-shadow: 0 1px 3px rgba(0,0,0,0.05);
}

.audit-search-form h3 {
	margin-top: 5px;
	margin-bottom: 15px;
	color: #23282d;
	font-size: 16px;
	border-bottom: 1px solid #eee;
	padding-bottom: 10px;
}

.audit-search-form .search-row {
	display: flex;
	flex-wrap: wrap;
	align-items: center;
	margin-bottom: 15px;
	gap: 10px;
}

.audit-search-form .search-row label {
	margin-right: 5px;
	font-weight: 500;
}

.audit-search-form .search-row input[type="text"],
.audit-search-form .search-row input[type="date"],
.audit-search-form .search-row select {
	min-width: 180px;
	height: 32px;
	margin-right: 15px;
}

.audit-search-form .button-secondary {
	height: 32px;
	line-height: 30px;
	padding: 0 12px;
	margin-left: 5px;
}

.audit-search-form .tablenav {
	margin: 5px 0;
	padding-top: 10px;
	border-top: 1px solid #f0f0f0;
}

.audit-search-form .tablenav .button-secondary {
	display: flex;
	align-items: center;
}

.audit-search-form .tablenav img {
	margin-right: 5px;
}

/* 表格样式 */
.wp-list-table {
	border: 1px solid #e5e5e5;
	box-shadow: 0 1px 1px rgba(0,0,0,.04);
}

.wp-list-table th {
	background: #f9f9f9;
	font-weight: 600;
}

.wp-list-table tr:nth-child(odd) {
	background-color: #f9f9f9;
}

.wp-list-table tr:hover {
	background: #f1f1f1;
}

/* 操作链接样式 */
.audit-view {
	text-decoration: none;
	color: #0073aa;
}

.audit-view:hover {
	color: #00a0d2;
}

.detail-data {
	color: #777;
	font-size: 12px;
}

/* 详情展开 */
tr.detail-view {
	background-color: #f8f8f8;
	border-left: 3px solid #0073aa;
}

tr.detail-view td {
	padding: 12px;
}

/* 顶部菜单导航 */
ul.subsubsub {
	margin: 12px 0;
}

ul.subsubsub li {
	padding-right: 10px;
}

ul.subsubsub a.current {
	font-weight: 600;
	color: #23282d;
}

/* 按钮美化 */
.button-secondary {
	transition: all 0.2s ease;
}

.button-secondary:hover {
	background: #f1f1f1;
	border-color: #999;
}

/* 选项页面样式 */
.options {
	list-style: none;
	padding: 0;
	margin: 15px 0;
	display: grid;
	grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
	gap: 10px;
}

.options li {
	margin-bottom: 10px;
}

.options label {
	display: flex;
	align-items: center;
	cursor: pointer;
}

.options input[type="checkbox"] {
	margin-right: 8px;
}

/* 用户选择区域 */
#user_selection {
	margin-top: 15px;
	padding: 10px;
	border: 1px solid #ddd;
	border-radius: 4px;
}

#available_users {
	width: 100%;
	height: 150px;
	margin-bottom: 10px;
}

#add_selected_users {
	margin-top: 5px;
}

/* 响应式调整 */
@media screen and (max-width: 782px) {
	.audit-search-form .search-row {
		flex-direction: column;
		align-items: flex-start;
	}
	
	.audit-search-form .search-row input[type="text"],
	.audit-search-form .search-row input[type="date"],
	.audit-search-form .search-row select {
		width: 100%;
		margin-right: 0;
		margin-bottom: 10px;
	}
	
	.options {
		grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
	}
}

/* 选项页面特定样式 */
.audit-options-form {
	max-width: 960px;
}

.audit-options-section {
	background: #fff;
	border: 1px solid #e5e5e5;
	box-shadow: 0 1px 1px rgba(0,0,0,0.04);
	padding: 15px 20px;
	margin-bottom: 20px;
	border-radius: 3px;
}

.audit-options-section h3 {
	border-bottom: 1px solid #eee;
	padding-bottom: 10px;
	margin-top: 0;
	font-size: 16px;
}

.audit-options-form .form-table th {
	width: 200px;
	font-weight: normal;
	padding: 15px 10px 15px 0;
}

.audit-options-form .form-table td {
	padding: 15px 10px;
}

.audit-options-form input[type="text"] {
	width: 350px;
	max-width: 100%;
}

.audit-options-form input[type="checkbox"] {
	margin-right: 5px;
}

.checkbox-label {
	display: flex;
	align-items: flex-start;
}

.checkbox-label input[type="checkbox"] {
	margin-top: 2px;
}

.description {
	color: #666;
	font-style: italic;
	margin-top: 5px;
	display: block;
}

.audit-options-form .button-primary {
	padding: 4px 20px;
	height: auto;
	line-height: 2;
	font-size: 14px;
}

.notice {
	padding: 10px 15px;
	margin: 15px 0;
	border-radius: 3px;
}

.notice-success {
	background-color: #ecf7ed;
	border-left: 4px solid #46b450;
}

.notice p {
	margin: 0.5em 0;
	padding: 2px;
}

@media screen and (max-width: 782px) {
	.audit-options-form .form-table th {
		width: 100%;
		display: block;
		padding-bottom: 0;
	}
	
	.audit-options-form .form-table td {
		display: block;
		padding-top: 5px;
	}
}

/* 详情页面样式 */
.audit-detail-view {
	background-color: #f9f9f9;
	padding: 15px;
	border-left: 4px solid #0073aa;
	margin: 10px 0;
}

.audit-detail-view input[type="text"],
.audit-detail-view textarea {
	width: 100%;
	padding: 8px;
	border: 1px solid #ddd;
	border-radius: 3px;
	background-color: #f9f9f9;
	color: #444;
	margin-bottom: 10px;
}

.audit-detail-view textarea {
	min-height: 120px;
	font-family: Consolas, Monaco, monospace;
	font-size: 13px;
	line-height: 1.5;
}

.audit-detail-view a {
	background-color: #f1f1f1;
	border: 1px solid #ccc;
	border-radius: 3px;
	color: #555;
	display: inline-block;
	text-decoration: none;
	font-size: 13px;
	line-height: 26px;
	height: 28px;
	margin: 0;
	padding: 0 10px 1px;
	cursor: pointer;
	text-align: center;
}

.audit-detail-view a:hover {
	background-color: #f9f9f9;
	border-color: #999;
	color: #23282d;
}

/* 增强表格样式 */
.wp-list-table .column-date {
	width: 15%;
}

.wp-list-table .column-user {
	width: 15%;
}

.wp-list-table .column-operation {
	width: 40%;
}

.wp-list-table .column-ip {
	width: 10%;
}

/* 日志记录突出显示 */
.wp-list-table tr.log-error {
	background-color: #fef7f1;
}

.wp-list-table tr.log-warning {
	background-color: #fcf9e8;
}

.wp-list-table tr.log-success {
	background-color: #ecf7ed;
}

/* 表头样式增强 */
.wp-list-table th {
	font-weight: 600;
	color: #32373c;
	background: #f1f1f1;
	border-bottom: 1px solid #ccc;
}

/* 翻页导航美化 */
.tablenav .tablenav-pages {
	margin: 5px 0;
}

.tablenav .tablenav-pages a,
.tablenav .tablenav-pages span.current {
	padding: 3px 10px;
	border: 1px solid #ddd;
	background: #f9f9f9;
	text-decoration: none;
	border-radius: 3px;
	margin-left: 5px;
}

.tablenav .tablenav-pages a:hover {
	background: #f1f1f1;
	border-color: #999;
}

.tablenav .tablenav-pages span.current {
	background: #0073aa;
	border-color: #0073aa;
	color: #fff;
	font-weight: 600;
}
