# 综合安全规则
# 合并：基础安全规则、SQL注入防护、路径遍历防护和敏感文件访问控制

# 限制请求方法 - 使用444错误代码可以更加减轻服务器负载压力
if ($request_method !~ ^(GET|POST|HEAD)$) {
    return 444;
}

# SQL注入防护 - 检查查询字符串
if ($query_string ~* (\$|'|--|[+|(%20)]union[+|(%20)]|[+|(%20)]insert[+|(%20)]|[+|(%20)]drop[+|(%20)]|[+|(%20)]truncate[+|(%20)]|[+|(%20)]update[+|(%20)]|[+|(%20)]from[+|(%20)]|[+|(%20)]grant[+|(%20)]|[+|(%20)]exec[+|(%20)]|[+|(%20)]where[+|(%20)]|[+|(%20)]select[+|(%20)]|[+|(%20)]and[+|(%20)]|[+|(%20)]or[+|(%20)]|[+|(%20)]count[+|(%20)]|[+|(%20)]exec[+|(%20)]|[+|(%20)]chr[+|(%20)]|[+|(%20)]mid[+|(%20)]|[+|(%20)]like[+|(%20)]|[+|(%20)]iframe[+|(%20)]|[\<|%3c]script[\>|%3e]|javascript|alert|webscan|dbappsecurity|style|confirm\(|innerhtml|innertext)(.*)$) { 
    return 555; 
}

# 防止路径遍历和特殊字符攻击
if ($uri ~* (/~).*) { return 501; }
if ($uri ~* (\\\\x[0-9a-f]+)) { return 501; }

# 防止SQL注入 - 检查特殊字符
if ($query_string ~* "[;'<>].*") { return 509; }
if ($request_uri ~ " ") { return 509; }
if ($request_uri ~ (\/\.+)) { return 509; }
if ($request_uri ~ (\.+\/)) { return 509; }

# 防止SQL注入 - 检查URI中的SQL关键字
if ($request_uri ~* "(cost\()|(concat\()") { return 504; }
if ($request_uri ~* "[+|(%20)]union[+|(%20)]") { return 504; }
if ($request_uri ~* "[+|(%20)]and[+|(%20)]") { return 504; }
if ($request_uri ~* "[+|(%20)]select[+|(%20)]") { return 504; }
if ($request_uri ~* "[+|(%20)]or[+|(%20)]") { return 504; }
if ($request_uri ~* "[+|(%20)]delete[+|(%20)]") { return 504; }
if ($request_uri ~* "[+|(%20)]update[+|(%20)]") { return 504; }
if ($request_uri ~* "[+|(%20)]insert[+|(%20)]") { return 504; }

# 防止XSS和其他常见攻击
if ($query_string ~ "(<|%3C).*script.*(>|%3E)") { return 505; }
if ($query_string ~ "GLOBALS(=|\[|\%[0-9A-Z]{0,2})") { return 505; }
if ($query_string ~ "_REQUEST(=|\[|\%[0-9A-Z]{0,2})") { return 505; }
if ($query_string ~ "proc/self/environ") { return 505; }
if ($query_string ~ "mosConfig_[a-zA-Z_]{1,21}(=|\%3D)") { return 505; }
if ($query_string ~ "base64_(en|de)code\(.*\)") { return 505; }

# 防止目录遍历和远程文件包含
if ($query_string ~ "[a-zA-Z0-9_]=http://") { return 506; }
if ($query_string ~ "[a-zA-Z0-9_]=(\.\.//?)+") { return 506; }
if ($query_string ~ "[a-zA-Z0-9_]=/([a-z0-9_.]//?)+") { return 506; }

# 过滤垃圾请求
if ($query_string ~ "b(ultram|unicauca|valium|viagra|vicodin|xanax|ypxaieo)b") { return 507; }
if ($query_string ~ "b(erections|hoodia|huronriveracres|impotence|levitra|libido)b") { return 507; }
if ($query_string ~ "b(ambien|bluespill|cialis|cocaine|ejaculation|erectile)b") { return 507; }
if ($query_string ~ "b(lipitor|phentermin|pro[sz]ac|sandyauer|tramadol|troyhamby)b") { return 507; }

# 阻止恶意爬虫和工具
if ($http_user_agent ~* YisouSpider|ApacheBench|WebBench|Jmeter|JoeDog|Havij|GetRight|TurnitinBot|GrabNet|masscan|mail2000|github|wget|curl|Java|python) { 
    return 508; 
}

# 阻止特定下载工具
if ($http_user_agent ~* "Go-Ahead-Got-It") { return 508; }
if ($http_user_agent ~* "GetWeb!") { return 508; }
if ($http_user_agent ~* "Go!Zilla") { return 508; }
if ($http_user_agent ~* "Download Demon") { return 508; }
if ($http_user_agent ~* "Indy Library") { return 508; }
if ($http_user_agent ~* "libwww-perl") { return 508; }
if ($http_user_agent ~* "Nmap Scripting Engine") { return 508; }
if ($http_user_agent ~* "~17ce.com") { return 508; }
if ($http_user_agent ~* "WebBench*") { return 508; }
#if ($http_user_agent ~* "spider") { return 508; } # 注意：这可能会影响搜狗等搜索引擎爬虫

# 阻止特定referrer
if ($http_referer ~* 17ce.com) { return 509; }
if ($http_referer ~* "WebBench*") { return 509; }

# 防止路径遍历攻击 - 来自path_traversal.conf
# 禁止通过URL路径中的双斜杠访问文件
location ~ //  {
    return 404;
}

# 禁止通过URL路径中的多个点访问文件
location ~ /\. {
    return 404;
}

# 明确禁止诸如image.jpg/file.php这样的路径尝试执行PHP
location ~ "^(.*)\.([a-z]+)\.(php|ph(p|tml|p3|p4|p5|ar|t|s))$" {
    # 排除wp-admin路径，因为有些WordPress后台功能可能使用这种格式
    location ~ ^/wp-admin/ {
        try_files $uri =404;
        fastcgi_pass php;
        include fastcgi_params;
        fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
    }
    
    # 拒绝所有其他路径
    return 403;
}

# 禁止访问敏感文件类型 - 归档和版本控制文件
location ~* \.(svn|git|sql|bak|old|tar|gz|tgz|zip|7z|rar|DS_store)$ {
    deny all;
    access_log off;
    log_not_found off;
}

# 禁止访问敏感文件类型 - 配置和系统文件
location ~* \.(csv|inc|config|conf|sh|sw[op]|bash_rc|tmp|ht|log|db|entries)$ {
    deny all;
    access_log off;
    log_not_found off;
}

# 禁止访问隐藏文件
location ~ /\.(git|svn|ht) {
    deny all;
    access_log off;
    log_not_found off;
}

# 添加安全相关的HTTP响应头 - 来自security_headers.conf
add_header X-Frame-Options "SAMEORIGIN" always;
add_header X-Content-Type-Options "nosniff" always;
add_header X-XSS-Protection "1; mode=block" always;
add_header Referrer-Policy "strict-origin-when-cross-origin" always;

# 如果启用了HTTPS，添加HSTS头
# 注意：仅在确认SSL配置正确后才启用此项
# add_header Strict-Transport-Security "max-age=63072000; includeSubDomains; preload" always;

# 限制大文件下载
location ~* \.(zip|rar|7z|tar|gz|sql|csv|txt|pdf|doc|docx|xls|xlsx|ppt|pptx)$ {
    if ($download_whitelist = 0) {
        return 403;
    }
    
    # 为允许的IP设置更高的下载限制
    limit_rate_after 5m;  # 前5MB不限速
    limit_rate 512k;      # 之后限制为512KB/s
    
    # 标准文件处理
    try_files $uri =404;
    expires 1d;
    access_log /var/log/nginx/download.log main;
}

# 允许访问特定路径
location ~ /.well-known/ {
    allow all;
}

# 修正后台的登录URL
location ~ ^/wp-admin$ {
    return 301 /wp-admin/;
}

# 可能是动态生成的资源，如sitemap
location ~* \.(xml|xsl)$ {
    try_files $uri /index.php?$args;
} 